/**
 * @enum
 * @public
 * @properties={typeid:35,uuid:"714E3952-E506-4477-AD31-A0DCFF4D617F",variableType:-4}
 */
var ITEM_TYPE_CODE = {
    ServiceItem :     'SE'
};

/**
 * @enum
 * @public
 * @properties={typeid:35,uuid:"3F1E7998-BC6E-4E64-A188-BEDEC05B2B07",variableType:-4}
 */
var APP_ITEM_CLASS_TYPE = {
    Envelopes :     'EN',
    PatchMaterial : 'PM',
    Cylinders :     'CY',
    FlexoPlates :   'FP',
    Finishing :     'FI',
    Coatings :      'CO',
    Postage :       'PO',
    Bindery :       'BI',
    RollCore :      'RC',
    Skids :         'SK',
    Inks :          'I',
    Tabs :          'TA',
    Other :         'O',
    Plates :        'PL',
    Dies :          'D',
    SheetPaper :    'P',
    Packing :       'PA',
    Blanket :       'BL',
    RollPaper :     'R',
    Adhesives :     'AD'
};

/**
 * @enum
 * @public
 *
 * @properties={typeid:35,uuid:"917C3E7F-F323-4BDD-ACB0-3E642C0DDC16",variableType:-4}
 */
var ENUM_TRANSACTION_STATUS_FIELD = {
	Open: "O",
	Updated: "U",
	Posted: "P"
}

/**
 * @enum
 * @public
 *
 * @properties={typeid:35,uuid:"4300BC21-F83D-46A9-8293-1B04B6B30E9D",variableType:-4}
 */
var ADJUSTMENT_TYPE = {
	Cost: "C",
	Quantity: "Q"
}

/**
 * @enum
 * @public
 * @properties={typeid:35,uuid:"8B5BFD98-308C-4B09-9E1E-21C450231913",variableType:-4}
 */
var APP_UOM_KEY = {
	MetricTons: 14,
	SqInches: 13,
	ThousandSqInches: 7,
	Ton: 19,
	Lot: 21,
	SqCentimetre: 25,
	SqMetre: 26,
	Litres: 15,
	Metres: 6,
	PerHundred: 22,
	Centimetres: 17,
	LinearFeet: 5,
	PerThousand: 9,
	Rolls: 20,
	Kilograms: 4,
	Millimetre: 23,
	SqFeet: 11,
	Sheets: 1,
	Gallons: 16,
	ThousandSqFeet: 12,
	Each: 8,
	HundredWeight: 2,
	Inches: 10,
	Pounds: 3,
	SqMillimetre: 24
};

/**
 * @type {String}
 *
 *
 * @properties={typeid:35,uuid:"AC510384-8CC2-4F95-9F5E-AF75C3D43700"}
 */
var containerForm = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"AEBB8844-42F1-431C-9EEB-CAF514E9B815"}
 */
var _sCurrentLabelExportFolder = '';

/**
 * @properties={typeid:35,uuid:"8227E36C-8F2D-4AFA-8DA8-3C92C34A392E",variableType:-4}
 */
var aInvComments = [];

/**
 * @properties={typeid:35,uuid:"B68A5E32-720F-467B-8435-CB741E8287BA",variableType:-4}
 */
var aInvCommentsNew = [];

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"172BC144-E069-46F9-877A-0DE9308625CC"}
 */
var patchMaterial = 'PM';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"265D6C74-65B7-4E91-84AC-DDE2D9FDCDA2"}
 */
var flexoPlates = 'FP';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"1C0951EC-D880-4090-A4C4-483A5E9ED9B4"}
 */
var adhesive = 'AD';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"AF530D38-B290-4A45-A4ED-90CF1ADE30BA"}
 */
var dieWindow = 'W';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"EE41E89A-640F-4014-B3F8-69F6506CE137"}
 */
var dieEnvelope = 'E';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"0B8D1A88-3A6D-4CD9-9ECD-96CEB55C8A69"}
 */
var dieStandard = 'S';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"31E7FF2D-1DDE-43AE-8C89-FA24D2C913BE"}
 */
var dieGummer = 'G';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"3F629249-C559-4420-AD45-BFF2AB6A14FD"}
 */
var processCyan = "641D9AB1-392C-4759-8403-6C1162A452BB";

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"*************-4466-86CF-4B279DA0BFD6"}
 */
var processYellow = 'A6ABD17A-F98A-4667-9BA2-5E4DC06492F7';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"151881DC-5B22-4463-B1FC-4F1B70FB8227"}
 */
var processMagenta = '7BE4B355-A6D3-4CDB-A3E5-8DC7AAB549E5';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"FEE77771-B6E4-4AA4-9DD1-71D94906F238"}
 */
var processBlack = 'D14C0EAB-8D22-4F9F-AD2B-B7BC7D051284';

/**
 * @enum
 * @public
 * @properties={typeid:35,uuid:"7C3CAD99-5D18-4D9F-880A-E97DB86740ED",variableType:-4}
 */
var DIMENSION_UNIT = {
	Metric: "M",
	Imperial: "I"
};

/**
 * @enum
 * @public
 * @properties={typeid:35,uuid:"FAE37180-06EF-49CE-A9BB-8D7B647E5237",variableType:-4}
 */
var INVOICE_RECORD_TYPE = {
	Invoice: "I",
	CreditNote: "C"
}

/**
 * @properties={typeid:35,uuid:"0B557A7D-4F17-4228-8994-8B9372FC0A20",variableType:-4}
 */
var INVENTORY_STATUS = {
	InActive: "I",
	Active: "A"
}

/**
 * @enum
 * @public
 *
 * @properties={typeid:35,uuid:"FD585126-D965-4D74-966B-CB24BFD67C8F",variableType:-4}
 */
var TRANSACTION_SOURCE = {
	AutoIssueRawMaterials: "AutoIssueRawMaterials",
	Integration: "Integration"
}

/**
 * @enum
 * @public
 *
 * @properties={typeid:35,uuid:"6C2335B8-D14B-4E22-87B6-AACA3ECAE45C",variableType:-4}
 */
var TRANSACTION_TYPE = {
	WhseTransferOut: "WTO",
	BinTransfer: "BTO",
	BinMove: "BINMOVE",
	RollBinTransfer: "RBTO",
	RollConversion: "RC",
	RollReceipts: "RR",
	PendingReceipts: "PR",
	InventoryReceipt: "IR",
	ReceiptFromProduction: "RFP",
	Shipment: "SH",
	CancelShipment: "CS",
	CostAdjustment: "CA",
	QuantityAdjustment: "QA",
	StockReceipt: "SR",
	CancelStockReceipt: "CSR",
	ShopFloorMaterialIssue: "SFMI",
	CountAdjustment: "CTA",
	TimeSheetMaterialAdjustment: "TSMA",
	InventoryIssue: "II",
	WarehousePick: "WP",
	IntegrationMaterialIssue: "IMI",
	PurchaseVarianceAdjustment: "PVA",
	FreightVarianceAdjustment: "FVA",
	CancelWhseTransferOut: "CWTO",
	ReturnToStock: "RTS",
	CancelReturnToStock: "CRTS",
	FulfillmentPick: "FP",
	CancelFulfillmentPick: "CFP"
}

/**
 * @type {Array}
 * @properties={typeid:35,uuid:"8FF4E574-D2A3-4C8B-B92B-5D7FBCB248E3",variableType:-4}
 */
var mAppUOMKey = null;

/**
 * @type {String}
 * @properties={typeid:35,uuid:"F085EF06-054A-4651-A771-07A1A81871EE"}
 */
var sOrdRevdItemIds = null;

/**
 * @type {Boolean}
 * @properties={typeid:35,uuid:"252E9776-BD9F-4F0A-9B65-A4856546F05D",variableType:-4}
 */
var bFirstShowItemFlushWindow = true;

/**
 * Sets the details for a Window Die
 * <AUTHOR> Dotzlaw
 * @since July 12, 2015
 * @param {String} sItemID - The Window Die item id
 * @properties={typeid:24,uuid:"5AC7075A-C48A-4B43-AE26-A83482CBCE9A"}
 */
function setVL_dieWindowForItem(sItemID) {

	var aReturn = [],
		aDisplay = [];

	scopes.avUtils.UUID_1 = sItemID
	if (utils.hasRecords(_to_in_item$avutils_uuid_1)) {

		aDisplay.push(_to_in_item$avutils_uuid_1.item_code + ": " + _to_in_item$avutils_uuid_1.item_desc1);
		aReturn.push(_to_in_item$avutils_uuid_1.item_id);

	}

	application.setValueListItems("avItemDiesWindow", aDisplay, aReturn);
}

/**
 * Create a vl
 *
 * <AUTHOR> Dotzlaw
 * @since Oct 21, 2014
 * @param {String|UUID} sTaskID - The ID for the press task being used
 * @properties={typeid:24,uuid:"10112F77-EFF5-4164-88C0-389E74DB04B8"}
 */
function setVL_dieWindow(sTaskID) {

	/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	var oSQL = { },
		/***@type {JSDataSet} */
		dsData,
		aReturn = [],
		aDisplay = [],
		bAllDies = false;

	if (sTaskID) {

		scopes.globals.avBase_selectedTaskID = sTaskID;

		if (utils.hasRecords(_to_sa_task$avbase_selectedtaskid)) {

			if (_to_sa_task$avbase_selectedtaskid.sa_task_to_sa_task_standard.taskstd_access_all_dies == 1) {

				bAllDies = true;
			}
		}

		// First get the die item classes used by the press
		oSQL.sql = "SELECT i.item_code + ': ' + i.item_desc1 AS DES, \
			i.item_id, i.itemclass_id AS CL \
			FROM in_item i \
			INNER JOIN in_item_die d ON i.item_id = d.item_id \
			INNER JOIN in_item_class c ON i.itemclass_id = c.itemclass_id";

		if (bAllDies) {

			oSQL.sql = scopes.avDB.safeSQL(oSQL.sql, "i.item_status = ? AND c.itemclass_die_type = ?", "in_item", null, "i");
			oSQL.args = ["A", "W"];

		}
		else {

			oSQL.sql = scopes.avDB.safeSQL(oSQL.sql, "i.item_status = ? AND c.itemclass_die_type = ? AND i.itemclass_id IN (SELECT DISTINCT(d.itemclass_id) FROM sa_task_class_die d WHERE d.task_id = ?) ", "in_item", null, "i");
			oSQL.args = ["A", "W", sTaskID.toString()];
		}
		oSQL.sql += " ORDER BY CL, DES";

		dsData = scopes.globals["avUtilities_sqlDataset"](oSQL);

		if (dsData) {

			aDisplay = dsData.getColumnAsArray(1);
			aReturn = dsData.getColumnAsArray(2);
		}
	}

	application.setValueListItems("avItemDiesWindow", aDisplay, aReturn);

}

/**
 * Create a vl
 *
 * <AUTHOR> Dotzlaw
 * @since June 13, 2014
 *
 * @param {JSRecord<db:/avanti/in_item_die>} rDie
 *
 * @properties={typeid:24,uuid:"1BBF8A82-875C-47B2-A508-5E79D5B0737D"}
 */
function setVL_avItemDiePressSheetSizes(rDie) {
	var aReturn = [],
		aDisplay = [],
		i = 0,
		iMax = 0,
		rRec,
		fs = rDie.in_item_die_to_in_item_die_size;

	if (utils.hasRecords(fs)) {
		fs.sort("sequence_nr asc");
		iMax = fs.getSize();
		for (i = 1; i <= iMax; i++) {
			rRec = fs.getRecord(i);

			aDisplay.push(rRec.itemdiesize_sheet_w + "x" + rRec.itemdiesize_sheet_l + " - " + rRec.itemdiesize_nr_dim1 + "/" + rRec.itemdiesize_nr_dim2);
			aReturn.push(rRec.itemdiesize_id);

		}
	}

	application.setValueListItems("avItemDiePressSheetSizes", aDisplay, aReturn);
}

/**
 * @param {JSFoundset<db:/avanti/in_item_roll>} fs
 *
 * @properties={typeid:24,uuid:"2390D83E-04D4-4E28-A2AD-C8279D244D84"}
 */
function removeRollItemFilters(fs) {
	fs.removeFoundSetFilterParam('itemwhseloc_id')
	fs.removeFoundSetFilterParam('itemwhse_id')
	fs.removeFoundSetFilterParam('initemroll_trans_posted')
	fs.removeFoundSetFilterParam('porecd_id')
}

/**
 * @param {UUID|String} itemID
 * @param {UUID|String} wareID
 * @param {String} [vlName]
 * @param {Boolean} [vbShowOHQ]
 *
 * @properties={typeid:24,uuid:"75BD271F-488D-4ACD-B093-D3F85EC09FD9"}
 * @AllowToRunInFind
 */
function loadRollByWarehouseVL(itemID, wareID, vlName, vbShowOHQ) {
	var aReturn = [];
	var aDisplay = [];

	if (!vlName) {
		vlName = 'vl_rollByItem';
	}

	/***@type {JSFoundset<db:/avanti/in_item_roll>} */
	var fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_roll');
	if (fs.find()) {
		fs.item_id = itemID;
		fs.whse_id = wareID;
		
		if (fs.search()) {
			fs.sort("initemroll_roll_number, sequence_nr");
			for (var i = 1; i <= fs.getSize(); i++) {

				var record = fs.getRecord(i);
				if (record.initemroll_qty_on_hand <= 0) {
					continue;
				}
				if (vlName == 'vl_rollByItem') {
					if (vbShowOHQ) {
						aDisplay.push(record.initemroll_roll_number + ' (OHQ:' + record.initemroll_qty_on_hand + ')');
					}
					else {
						aDisplay.push(record.initemroll_roll_number);
					}
				}
				else if (vlName == 'vl_rollByItem_millnum') {
					aDisplay.push(record.initemroll_roll_number);
				}
				aReturn.push(record.initemroll_id);
			}
		}
	}

	if (vlName) {
		application.setValueListItems(vlName, aDisplay, aReturn);
	}
	else {
		application.setValueListItems("vl_rollByWarehouse", aDisplay, aReturn);
	}
}

/**
 * @properties={typeid:24,uuid:"4F2B0266-0A1C-4F92-9287-2EE8E74E6C4F"}
 */
function clearRollByWarehouseVL() {
	application.setValueListItems("vl_rollByWarehouse", [], []);
}

/**
 * @param {UUID|String} itemID
 * @param {UUID|String} binlocID
 * @param {String} [vlName]
 * @param {Boolean} [vbShowOHQ]
 *
 * @properties={typeid:24,uuid:"871EFBBC-3D39-426A-86FD-B8C78C696206"}
 * @AllowToRunInFind
 */
function loadRollByBinLocVL(itemID, binlocID, vlName, vbShowOHQ) {
	var aReturn = []
	var aDisplay = []

	/***@type {JSFoundset<db:/avanti/in_item_roll>} */
	var fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_roll')
	if (fs.find()) {
		fs.item_id = itemID
		fs.whseloc_id = binlocID
		var num = fs.search()
		if (num > 0) {
			fs.sort("initemroll_roll_number, sequence_nr");
			for (var i = 1; i <= num; i++) {
				var record = fs.getRecord(i);
				if (record.initemroll_qty_on_hand <= 0) {
					continue;
				}
				if (vlName == 'vl_rollByItem') {
					if (vbShowOHQ) {
						aDisplay.push(record.initemroll_roll_number + ' (OHQ:' + record.initemroll_qty_on_hand + ')')
					}
					else {
						aDisplay.push(record.initemroll_roll_number)
					}
				}
				else if (vlName == 'vl_rollByItem_millnum') {
					aDisplay.push(record.initemroll_roll_number)
				}
				aReturn.push(record.initemroll_id)
			}
		}
	}

	if (vlName) {
		application.setValueListItems(vlName, aDisplay, aReturn);
	}
	else {
		application.setValueListItems("vl_rollByBinLoc", aDisplay, aReturn);
	}
}

/**
 * @AllowToRunInFind
 *
 * @param {UUID|String} itemID
 * @param {UUID|String} wareID
 * @param {UUID|String} [binlocID]
 * @param {Boolean} [vbShowOHQ]
 *
 * @properties={typeid:24,uuid:"70178ADD-66D0-4A96-ABC1-9F67516D7C2A"}
 */
function loadRollByItemVL(itemID, wareID, binlocID, vbShowOHQ) {
	/**@type {JSRecord<db:/avanti/in_item>} */
	var rItem = scopes.avDB.getRec('in_item', ['item_id'], [itemID])
	var vl = ''

	if (rItem) {
		if (rItem.in_item_to_in_item_class.itemclass_show_roll_unique_id == 1) {
			vl = 'vl_rollByItem'
			if (binlocID) {
				loadRollByBinLocVL(itemID, binlocID, vl, vbShowOHQ)
			}
			else {
				loadRollByWarehouseVL(itemID, wareID, vl, vbShowOHQ)
			}
		}
		if (rItem.in_item_to_in_item_class.itemclass_show_mill_roll_num == 1) {
			vl = 'vl_rollByItem_millnum'
			if (binlocID) {
				loadRollByBinLocVL(itemID, binlocID, vl, vbShowOHQ)
			}
			else {
				loadRollByWarehouseVL(itemID, wareID, vl, vbShowOHQ)
			}
		}

	}
}

/**
 * @properties={typeid:24,uuid:"6F88463F-6FD0-4849-8A4C-FEFF2ED36779"}
 */
function clearRollByBinLocVL() {
	application.setValueListItems("vl_rollByBinLoc", [], []);
}

/**
 * @param {UUID|String} itemID
 *
 * @return
 * @properties={typeid:24,uuid:"B7AEF135-C449-41A1-ABF3-CB64FD39D3A8"}
 * @AllowToRunInFind
 */
function isTrackedRoll(itemID) {
	/***@type {JSFoundset<db:/avanti/in_item>} */
	var fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item')
	if (fs.find()) {
		fs.item_id = itemID
		if (fs.search()) {
			return (fs.item_track_rolls == 1)
		}
	}

	return false
}

/**
 * @param {UUID|String} rollID
 * @param {Number} qty
 *
 * @properties={typeid:24,uuid:"453EC4C3-CF16-47DA-9B5A-1ACFA7807E97"}
 * @AllowToRunInFind
 */
function adjustRollQty(rollID, qty) {
	/***@type {JSFoundset<db:/avanti/in_item_roll>} */
	var fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_roll')
	if (fs.find()) {
		fs.initemroll_id = rollID
		if (fs.search()) {
			fs.initemroll_qty_on_hand += qty
			if (globals.nav_program_name == "Shop_Floor" && fs.initemroll_committed > 0) {
				fs.initemroll_committed += qty
			}
			databaseManager.saveData(fs.getSelectedRecord())
		}
	}
}

/**
 * @AllowToRunInFind
 *
 * @param {UUID|String} whseID
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"996B0F65-3142-4640-B982-8E90346E6776"}
 */
function isWarehouseBinEnabled(whseID) {
	if (whseID) {
		/***@type {JSFoundset<db:/avanti/in_warehouse>} */
		var fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_warehouse')
		if (fs.find() || fs.find()) {
			fs.whse_id = whseID
			if (fs.search()) {
				return fs.whse_enable_bin_locations == 1
			}
		}
	}

	return false
}

/**
 * @return {Boolean}
 * @properties={typeid:24,uuid:"E104C297-0698-4CA4-A66E-A75AB4426BBF"}
 */
function areAnyWarehousesUsingBinLocs() {
	return (_to_in_warehouse$bin_enabled.getSize() > 0)
}

/**
 * @return {Boolean}
 * @properties={typeid:24,uuid:"100C3028-33B4-45EE-86FA-3D3A57FE2EB2"}
 */
function areThereAnyTrackedRollItems() {
	return scopes.avDB.Exists('in_item', ['item_track_rolls'], [1])
}

/**
 * @param {String|UUID} itemID
 * @param {String|UUID} binLocID
 * @param {String|UUID} [excluding_ordrevd_id]
 *
 * @return
 * @properties={typeid:24,uuid:"128F8552-FF04-43D0-8C5D-9F4EADC8E2A2"}
 */
function getQtyAvailInBinLoc(itemID, binLocID, excluding_ordrevd_id) {

	var nRet = 0
	var nOnHandQty = scopes.avDB.Query('in_item_warehouse_location', ['item_id', 'whseloc_id'], [itemID, binLocID], 'itemwhseloc_onhand_qty');
	var nUavailibleQty = scopes.avDB.Query('in_item_warehouse_location', ['item_id', 'whseloc_id'], [itemID, binLocID], 'itemwhseloc_unavailible_qty');
	if (nOnHandQty - nUavailibleQty) {
		nRet = nOnHandQty - nUavailibleQty;
		if (excluding_ordrevd_id) {
			nRet -= getQtyAllocatedByOtherJobs(itemID, binLocID, excluding_ordrevd_id);
		}
	}

	return nRet;
}

/**
 * @param {String|UUID} itemID
 * @param {String|UUID} binLocID
 * @param {String|UUID} ordrevd_id
 *
 * @return
 * @properties={typeid:24,uuid:"D63A2E86-7470-4C79-AE9F-F88015864A01"}
 */
function getQtyAllocatedByOtherJobs(itemID, binLocID, ordrevd_id) {
	// Allocation is based on items that are in-picking or in-shipping and not confirmed.
	var nTotalAllocated = 0;

	itemID = itemID.toString()
	binLocID = binLocID.toString()
	ordrevd_id = ordrevd_id.toString()

	var sql = 'select sum(pickdb_qty) from sa_pick_detail_bin pickbin \
		inner join in_item_warehouse_location binloc on binloc.itemwhseloc_id = pickbin.itemwhseloc_id \
		inner join sa_pick_detail pickdet on pickdet.pickd_id = pickbin.pickd_id \
		where binloc.item_id = ? and binloc.whseloc_id = ? and pickdet.ordrevd_id != ?'
	var allocQty = globals.Query(sql, true, [itemID, binLocID, ordrevd_id], null, 'pickbin')
	if (allocQty) {
		nTotalAllocated += allocQty
	}

	sql = 'select sum(packdb_qty) from sa_pack_detail_bin packbin \
		inner join in_item_warehouse_location binloc on binloc.itemwhseloc_id = packbin.itemwhseloc_id \
		inner join sa_pack_detail packdet on packdet.packd_id = packbin.packd_id \
		where binloc.item_id = ? and binloc.whseloc_id = ? and packdet.ordrevd_id != ?'
	allocQty = globals.Query(sql, true, [itemID, binLocID, ordrevd_id], null, 'packbin')
	if (allocQty) {
		nTotalAllocated += allocQty
	}

	return nTotalAllocated;
}

/**
 * @param {String|UUID} roll_ID
 *
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"2ECE103E-68DD-43A0-8BAE-FBE89908E350"}
 */
function getQtyAvailInRoll(roll_ID) {
	return scopes.avDB.Query('in_item_roll', ['initemroll_id'], [roll_ID], 'initemroll_qty_avail')
}

/**
 * @param {UUID|String} whseID
 * @param {UUID|String} ordrevdID
 * @param {UUID|String} itemID
 * @param {Number} qty
 *
 * @return
 * @properties={typeid:24,uuid:"55C804D0-32A1-4D78-89E8-20ECC2DF67AA"}
 */
function getDefaultBin(whseID, ordrevdID, itemID, qty) {
	var binLocID = getDefaultBinFromPicklist(whseID, ordrevdID, itemID, qty)
	if (!binLocID) {
		binLocID = getBinFromFIFO(itemID, whseID, qty)
	}

	return binLocID
}

/**
 * @AllowToRunInFind
 *
 * @param {UUID|String} whseID
 * @param {UUID|String} ordrevdID
 * @param {UUID|String} itemID
 * @param {Number} qty
 *
 * @return
 * @properties={typeid:24,uuid:"D888435D-1E3F-4894-94B2-2CA8420C934B"}
 */
function getDefaultBinFromPicklist(whseID, ordrevdID, itemID, qty) {
	/***@type {JSFoundset<db:/avanti/sa_pick_detail>} */
	var fs_sa_pick_detail = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_pick_detail')
	if (fs_sa_pick_detail.find() || fs_sa_pick_detail.find()) {
		fs_sa_pick_detail.ordrevd_id = ordrevdID
		fs_sa_pick_detail.item_id = itemID
		if (fs_sa_pick_detail.search()) {
			if (utils.hasRecords(fs_sa_pick_detail.sa_pick_detail_to_sa_pick_detail_bin)) {
				for (var i = 1; i <= fs_sa_pick_detail.sa_pick_detail_to_sa_pick_detail_bin.getSize(); i++) {
					var rec = fs_sa_pick_detail.sa_pick_detail_to_sa_pick_detail_bin.getRecord(1)
					if (rec.sa_pick_detail_bin_to_in_item_warehouse_location.in_item_warehouse_location_to_in_item_warehouse.whse_id == whseID && rec.sa_pick_detail_bin_to_in_item_warehouse_location.itemwhseloc_available_qty >= qty) {
						return fs_sa_pick_detail.sa_pick_detail_to_sa_pick_detail_bin.sa_pick_detail_bin_to_in_item_warehouse_location.whseloc_id
					}
				}
			}
		}
	}

	return null
}

/**
 * @param {UUID|String} whseID
 * @param {UUID|String} itemID
 * @param {Number} qty
 *
 * @return
 * @properties={typeid:24,uuid:"9CE13EEF-8CA1-4982-9BD2-9AFBA2D63AC3"}
 * @AllowToRunInFind
 */
function getBinFromFIFO(itemID, whseID, qty) {
	/***@type {JSFoundset<db:/avanti/in_item_warehouse>} */
	var fsItemWare = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_warehouse')
	if (fsItemWare.find() || fsItemWare.find()) {
		fsItemWare.item_id = itemID
		fsItemWare.whse_id = whseID
		if (fsItemWare.search()) {
			/***@type {JSFoundset<db:/avanti/in_item_fifo>} */
			var fsItemFifo = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_fifo')
			if (fsItemFifo.find() || fsItemFifo.find()) {
				fsItemFifo.itemwhseloc_id = getBinsThatHaveEnoughQty(fsItemWare.itemwhse_id, qty)
				if (fsItemFifo.search()) {
					fsItemFifo.sort('fifo_date asc, fifo_qty_remaining asc');
					if (utils.hasRecords(fsItemFifo.getRecord(1), 'in_item_fifo_to_in_item_warehouse_location')) {
						return fsItemFifo.getRecord(1).in_item_fifo_to_in_item_warehouse_location.whseloc_id
					}
				}
			}
		}
	}

	return null
}

/**
 * @AllowToRunInFind
 *
 * @param {UUID|String} whseID
 * @param {UUID|String} itemID
 * @param {Number} qty
 *
 * @return
 * @properties={typeid:24,uuid:"98A0BA95-A78D-4CA8-859F-9D33E4E50CD8"}
 */
function getRollFromFIFO(itemID, whseID, qty) {
	/***@type {JSFoundset<db:/avanti/in_item_warehouse>} */
	var fsItemWare = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_warehouse')
	if (fsItemWare.find()) {
		fsItemWare.item_id = itemID
		fsItemWare.whse_id = whseID
		if (fsItemWare.search()) {
			/***@type {JSFoundset<db:/avanti/in_item_fifo>} */
			var fsItemFifo = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_fifo');
			if (fsItemFifo.find()) {
				fsItemFifo.initemroll_id = null
				// get partial rolls fst
				if (globals.avBase_getSystemPreference_Number(106)) {
					fsItemFifo.initemroll_id = getRollsThatHaveEnoughQty(fsItemWare.itemwhse_id, qty, true)
				}
				if (!fsItemFifo.initemroll_id) {
					fsItemFifo.initemroll_id = getRollsThatHaveEnoughQty(fsItemWare.itemwhse_id, qty)
				}

				if (fsItemFifo.initemroll_id) {
					if (fsItemFifo.search()) {
						fsItemFifo.sort('fifo_date asc, fifo_qty_remaining asc');
						if (utils.hasRecords(fsItemFifo.getRecord(1), 'in_item_fifo_to_in_item_roll')) {
							return fsItemFifo.getRecord(1).in_item_fifo_to_in_item_roll.getRecord(1)
						}
					}
				}
			}
		}
	}

	return null
}

/**
 * @param {String|UUID} itemwhseID
 * @param {Number} qty
 *
 * @return
 * @properties={typeid:24,uuid:"91AEB32E-AC7B-40C7-A5A6-31AACD9E9CFA"}
 */
function getBinsThatHaveEnoughQty(itemwhseID, qty) {
	var locs = ''
	var oSQL = new Object();
	oSQL.sql = "select itemwhseloc_id " + "from in_item_fifo " + "where itemwhse_id = ? and org_id = ? " + "group by itemwhseloc_id " + "having sum(fifo_qty_remaining) > ?"
	oSQL.args = [itemwhseID, globals.org_id, qty];

	/** @type {JSDataSet}*/
	var dsData = globals['avUtilities_sqlDataset'](oSQL);

	if (dsData && dsData.getMaxRowIndex() > 0) {
		for (var d = 1; d <= dsData.getMaxRowIndex(); d++) {
			if (locs) {
				locs += '||'
			}
			locs += dsData.getValue(d, 1);
		}
	}

	return locs
}

/**
 * @param {String|UUID} itemwhseID
 * @param {Number} qty
 * @param {Boolean} [bPartials]
 *
 * @return
 * @properties={typeid:24,uuid:"8CD58D8E-3B22-473C-BCFC-C75E224605F7"}
 */
function getRollsThatHaveEnoughQty(itemwhseID, qty, bPartials) {
	var rolls = ''
	var oSQL = new Object();
	oSQL.sql = "select fifo.initemroll_id " + "from in_item_fifo fifo " + "inner join in_item_roll roll on roll.initemroll_id = fifo.initemroll_id " + "where fifo.itemwhse_id = ? and fifo.org_id = ? " + "and fifo.fifo_qty_remaining > ? "

	if (bPartials) {
		oSQL.sql += "and roll.initemroll_org_qty > roll.initemroll_qty_on_hand "
	}
	oSQL.args = [itemwhseID.toString(), globals.org_id, qty];

	/** @type {JSDataSet}*/
	var dsData = globals['avUtilities_sqlDataset'](oSQL);

	if (dsData && dsData.getMaxRowIndex() > 0) {
		for (var d = 1; d <= dsData.getMaxRowIndex(); d++) {
			if (rolls) {
				rolls += '||'
			}
			rolls += dsData.getValue(d, 1);
		}
	}

	return rolls
}

/**
 * @param {String|UUID} itemID
 * @param {String|UUID} wareID
 *
 * @return
 * @properties={typeid:24,uuid:"F86247C7-4945-4119-A6C8-53910C4BA342"}
 */
function getQtyAvailInWarehouse(itemID, wareID) {
	return scopes.avDB.Query('in_item_warehouse', ['item_id', 'whse_id'], [itemID, wareID], 'itemwhse_qtyAvailable')
}

/**
 * Get the quantity on hand for a specific item in a warehouse
 * @param itemID
 * @param wareID
 *
 * @return
 * @properties={typeid:24,uuid:"957E9A46-57F9-443D-89A5-2CA9E7F64314"}
 */
function getQtyOnHandInWarehouse(itemID, wareID) {
	return scopes.avDB.Query('in_item_warehouse', ['item_id', 'whse_id'], [itemID, wareID], 'itemwhse_onhand_qty');
}

/**
 * @param {String|UUID} binID
 * @param {String|UUID} itemID
 *
 * @return
 * @properties={typeid:24,uuid:"7C48B5A3-B29C-4FB5-896C-300917F2126B"}
 */
function binHasThisItem(binID, itemID) {
	return scopes.avDB.Exists('in_item_warehouse_location', ['item_id', 'whseloc_id'], [itemID, binID])
}

/**
 * @param {String|UUID} jobID
 * @param {String|UUID} rollID
 * @param {Number} qtyUsed
 *
 * @properties={typeid:24,uuid:"73003517-7D1D-4875-87F4-9BD8194CF49E"}
 */
function updateJobRollRec(jobID, rollID, qtyUsed) {
	/***@type {JSFoundset<db:/avanti/prod_job_rolls_used>} */
	var fs = scopes.avDB.getFS('prod_job_rolls_used', ['job_id', 'initemroll_id'], [jobID, rollID])
	var rec
	if (fs.getSize()) {
		rec = fs.getRecord(1)
		rec.qty_used += qtyUsed
		rec.last_date_used = application.getTimeStamp()
	}
	else {
		rec = fs.getRecord(fs.newRecord())
		rec.job_id = jobID
		rec.initemroll_id = rollID
		rec.qty_used = qtyUsed
		rec.last_date_used = application.getTimeStamp()
	}

	databaseManager.saveData(rec)
}

/**
 * @param {String|UUID} itemID
 *
 * @return
 * @properties={typeid:24,uuid:"3D89EEDF-A8C8-4A6B-8B36-C619356238B3"}
 */
function loadCompatibleRollItemsVL(itemID) {
	var aReturn = []
	var aDisplay = []
	var bFoundOne = false

	/***@type {JSRecord<db:/avanti/in_item>} */
	var recParent = scopes.avDB.getRec('in_item', ['item_id'], [itemID])
	if (recParent) {
		/***@type {JSFoundset<db:/avanti/in_item>} */
		var fs = scopes.avDB.getFS('in_item', ['item_track_rolls', 'itemclass_id', 'in_item_to_in_item_paper.paper_caliper', 'item_standard_uom_id'], [1, recParent.itemclass_id, recParent.in_item_to_in_item_paper.paper_caliper, recParent.item_standard_uom_id], 'in_item_to_in_item_roll.initemroll_roll_number asc')
		var num = fs.getSize()
		for (var i = 1; i <= num; i++) {
			var rec = fs.getRecord(i)
			if (rec.item_id != itemID) {
				aReturn.push(rec.item_id)
				aDisplay.push(rec.item_code)
				bFoundOne = true
			}
		}
	}

	application.setValueListItems("vl_compatibleRollItems", aDisplay, aReturn);

	return bFoundOne
}

/**
 *
 * @properties={typeid:24,uuid:"4A097207-744B-448B-9320-01F1390BC05A"}
 */
function clearCompatibleRollItemsVL() {
	application.setValueListItems("vl_compatibleRollItems", [], []);
}

/**
 * @param {String|UUID} itemID
 * @param {String|UUID} wareID
 *
 * @properties={typeid:24,uuid:"AE9B2233-0BC7-43DE-8796-3C6D07FDFFD9"}
 */
function loadBinByItemAndWareVL(itemID, wareID) {
	var itemwhse_id = scopes.avDB.Query('in_item_warehouse', ['item_id', 'whse_id'], [itemID, wareID], 'itemwhse_id')
	scopes.avVL.loadVL('vl_BinByItemAndWare', 'in_item_warehouse_location', ['itemwhse_id'], [itemwhse_id], ['whseloc_bin_location'], 'whseloc_id')
}

/**
 * @param {String} uom
 * @param {Number} length
 * @param {Number} width
 * @param {Number} weight
 * @param {Number} roll_diam
 * @param {Number} core_diam
 * @param {Number} caliper
 * @param {Number} area
 *
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"00AFC089-6F68-4B08-A5C9-FADFFE350F7E"}
 */
function getItemQty(uom, length, width, weight, roll_diam, core_diam, caliper, area) {
	var linear_feet = 0
	var sq_in = 0
	var sq_ft = 0
	var msi = 0
	var msf = 0
	var qty = 0

	if (!isSimpleUOM(uom, area)) {
		var linear_inches

		if (length) {
			// GD - 23 Apr 2015: SL-3929 Metric
			(scopes.globals.avPref_dimension_unit === "M") ? linear_inches = length * 1000 : linear_inches = length * 12;
		}
		else if (roll_diam && caliper) {
			/*** thanks to Bryce Martin at National Ticket for providing the formulas to calc qty from roll diameter + caliper ***/
			var pi = 3.1416
			linear_inches = pi * (roll_diam - core_diam) / caliper
		}

		(scopes.globals.avPref_dimension_unit === "M") ? linear_feet = linear_inches / 1000 : linear_feet = linear_inches / 12;
		sq_in = linear_inches * width;
		(scopes.globals.avPref_dimension_unit === "M") ? sq_ft = linear_feet * (width / 1000) : sq_ft = linear_feet * (width / 12);
		msi = sq_in / 1000
		msf = sq_ft / 1000
	}
	else if (area) {
		sq_in = area
		sq_ft = area
		msi = area
		msf = area
	}

	switch (uom) {
	case 'lb': // Pounds
		qty = weight
		break
	case 'kg': // Kilograms
		qty = weight
		break

	case 'in': // Inches (linear?)
		qty = linear_inches
		break
	case 'ft': // Linear Feet
		qty = linear_feet
		break

	case 'in2': // Sq Inches
		qty = sq_in
		break
	case 'ft2': // Sq Feet
		qty = sq_ft
		break
	case 'MSI': // Inches (linear?)
		qty = msi
		break
	case 'MSF': // Sq Feet
		qty = msf
		break
	}

	return qty
}

/**
 * @param {String} uom
 * @param {Number} area
 *
 * @return
 * @properties={typeid:24,uuid:"3D0D77EC-24D1-43F5-B478-1D9FC9E88058"}
 */
function isSimpleUOM(uom, area) {
	// simple - ie no calc required
	if (uom == 'RL' || uom == 'lb' || uom == 'kg' || (area && isAreaUOM(uom))) {
		return true
	}
	return false
}

/**
 * @param {String} uom
 *
 * @return
 * @properties={typeid:24,uuid:"E9670E98-D819-4D9A-A9DC-5983B0F5BC56"}
 */
function isAreaUOM(uom) {
	if (uom == 'in2' || uom == 'ft2' || uom == 'MSI' || uom == 'MSF') {
		return true
	}
	return false
}

/**
 * @AllowToRunInFind
 *
 * @param {String|UUID} itemID
 * @param {String|UUID} wareID
 *
 * @return
 * @properties={typeid:24,uuid:"3B5B3ABC-DC71-4163-9978-D6F2A98AA32D"}
 */
function getItemWarehouseID(itemID, wareID) {
	/** @type {JSFoundSet<db:/avanti/in_item_warehouse>} */
	var fs_in_item_warehouse = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_warehouse')
	if (fs_in_item_warehouse.find() || fs_in_item_warehouse.find()) {
		fs_in_item_warehouse.item_id = itemID
		fs_in_item_warehouse.whse_id = wareID
		if (fs_in_item_warehouse.search()) {
			return fs_in_item_warehouse.getRecord(1).itemwhse_id
		}
	}

	return null
}

/**
 * @param {String|UUID} itemWareID
 * @param {String|UUID} binID
 *
 * @return
 * @properties={typeid:24,uuid:"58293E74-7316-4033-B07B-392EB76DE9BE"}
 */
function getItemWareBinID(itemWareID, binID) {
	return scopes.avDB.Query('in_item_warehouse_location', ['itemwhse_id', 'whseloc_id'], [itemWareID, binID], 'itemwhseloc_id')
}

/**
 * @public
 * @param wareID
 * <AUTHOR>
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"A2C2A4D9-B736-4E28-84DA-11A7C03C6C04"}
 */
function areBinsEnabled(wareID) {
	return (scopes.avDB.getVal('in_warehouse', ['whse_id'], [wareID], 'whse_enable_bin_locations') == 1)
}

/**
 * Called when the valuelist needs data, it has 3 modes.
 * real and display params both null: return the whole list
 * only display is specified, called by a typeahead, return a filtered list
 * only real value is specified, called when the list doesnt contain the real value for the give record value, this will insert this value into the existing list
 *
 * @param {String} displayValue The value of a lookupfield that a user types
 * @param realValue The real value for a lookupfield where a display value should be get for
 * @param {JSRecord} record The current record for the valuelist.
 * @param {String} valueListName The valuelist name that triggers the method. (This is the FindRecord in find mode, which is like JSRecord has all the columns/dataproviders, but doesn't have its methods)
 * @param {Boolean} findMode True if foundset of this record is in find mode
 * @param {Boolean} rawDisplayValue The raw displayValue without being converted to lower case
 *
 * @returns {JSDataSet} A dataset with 1 or 2 columns display[,real]
 *
 * @properties={typeid:24,uuid:"3C247480-BD61-4ADB-AD6A-A2733B21F807"}
 */
function getDataSetForValueList_Items(displayValue, realValue, record, valueListName, findMode, rawDisplayValue) {
	var args = null;
	/** @type QBSelect<db:/avanti/in_item> */
	var query = databaseManager.createSelect('db:/avanti/in_item');
	/** @type  {JSDataSet} */
	var result = null;
	if (displayValue == null && realValue == null) {
		// TODO think about caching this result. can be called often!
		// return the complete list
		query.result.add(query.columns.item_code.concat(': ').concat(query.columns.item_desc1)).add(query.columns.item_id)
		query.where.add(query.columns.org_id.eq(globals.org_id));
		query.sort.add(query.columns.item_code.asc)
		result = databaseManager.getDataSetByQuery(query, 1000);
	}
	else if (displayValue != null) {
		// TYPE_AHEAD filter call, return a filtered list
		args = [displayValue + "%", displayValue + "%"];
		query.result.add(query.columns.item_code.concat(': ').concat(query.columns.item_desc1)).add(query.columns.item_id)
		query.where.add(query.and.add(query.columns.org_id.eq(globals.org_id)).add(query.or.add(query.columns.item_code.lower.like('%' + args[0] + '%')).add(query.columns.item_desc1.lower.like('%' + args[1] + '%'))));
		query.sort.add(query.columns.item_code.asc)
		result = databaseManager.getDataSetByQuery(query, 1000);
	}
	else if (realValue != null) {
		// TODO think about caching this result. can be called often!
		// real object not found in the current list, return 1 row with display,realvalue that will be added to the current list
		// dont return a complete list in this mode because that will be added to the list that is already there
		var re = new RegExp("[A-Fa-f0-9]{8}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{12}");
		if (re.test(realValue)) {
			args = [realValue];
			query.result.add(query.columns.item_code.concat(': ').concat(query.columns.item_desc1)).add(query.columns.item_id)
			query.where.add(query.and.add(query.columns.item_id.eq(args[0].toString())).add(query.columns.org_id.eq(globals.org_id)));
			query.sort.add(query.columns.item_code.asc)
			result = databaseManager.getDataSetByQuery(query, 1);
		}
	}

	return result;

}

/**
 * Get sides for a Window Die
 *
 * <AUTHOR> Dotzlaw
 * @since Nov 17, 2014
 *
 * @param {String|UUID} sWinDieID - The window die
 * @returns {String} The sides available, "f", "b", or "both"
 * @public
 *
 *
 * @properties={typeid:24,uuid:"3D4A3664-512E-434E-9349-9F9E9D7D7A07"}
 */
function getWindowDieSides(sWinDieID) {

	var sWinFront = "",
		sWinBack = "",
		sWinSides = null,
		fsWin,
		rWinDie,
		rItem,
		i = 0;

	scopes.avUtils.UUID_1 = sWinDieID;

	if (utils.hasRecords(_to_in_item$avutils_uuid_1)) {

		rItem = _to_in_item$avutils_uuid_1.getRecord(1);

		if (utils.hasRecords(rItem.in_item_to_in_item_die) && utils.hasRecords(rItem.in_item_to_in_item_die.in_item_die_to_in_item_die_window)) {

			fsWin = rItem.in_item_to_in_item_die.in_item_die_to_in_item_die_window;

			for (i = 1; i <= fsWin.getSize(); i++) {

				rWinDie = fsWin.getRecord(i);

				if (i === 1 || i > 2 && rWinDie.itemdiewin_side === null) {
					// assume the first is front, or any window other than the second is also front
					sWinFront = "f";

				}
				else if (i === 2 && rWinDie.itemdiewin_side === null) {
					// assume second is back, unless otherwise specified
					sWinBack = "b";

				}
				else {
					// assume front as default
					if (rWinDie.itemdiewin_side === "f" || rWinDie.itemdiewin_side === null) {

						sWinFront = "f";

					}
					else {

						sWinBack = "b";
					}
				}
			}
		}
	}

	if (sWinFront === "f" && sWinBack === "b") {
		sWinSides = "both";
	}
	else if (sWinFront === "f") {
		sWinSides = sWinFront;
	}
	else if (sWinBack === "B") {
		sWinSides = sWinBack;
	}

	return sWinSides;
}

/**
 * Gets the vl
 *
 * <AUTHOR> Dotzlaw
 * @since Nov 18, 2014
 * @public
 *
 * @properties={typeid:24,uuid:"10F93E7A-DB40-4B58-893A-D94FC1B3DDEE"}
 */
function setVL_avItemPaperFinish() {

	var oSQL = new Object(),
		/** @type {JSDataSet} */
		dsData = null,
		vlDisplayValues = [];

	oSQL.sql = " \
		SELECT DISTINCT(paperbrand_name) \
		FROM in_paper_brand pb\
		WHERE pb.org_id = ?";

	oSQL.args = [globals.org_id];

	oSQL.sql += " ORDER BY paperbrand_name";

	dsData = globals["avUtilities_sqlDataset"](oSQL);

	if (dsData && dsData.getMaxRowIndex() > 0) {

		vlDisplayValues = dsData.getColumnAsArray(1);

	}
	application.setValueListItems('avItemPaperFinish_allPaperBrands', vlDisplayValues);
}

/**
 * @return {JSFoundset<db:/avanti/in_item>}
 *
 * @properties={typeid:24,uuid:"A0CD8607-C36E-4876-AAE8-58299F277F96"}
 */
function getRollItems() {
	/***@type {JSFoundset<db:/avanti/in_item>} */
	var fs = scopes.avDB.getFS('in_item', ['in_item_to_in_item_class.in_item_class_to_app_item_class_type.itemclasstype_id', 'item_status'], ['R', 'A'], 'item_code')
	return fs

}

/**
 * @return {JSFoundset<db:/avanti/in_item>}
 *
 * @properties={typeid:24,uuid:"104A6940-80CD-4225-8075-7014F2B497DC"}
 */
function getSheetItems() {
	/***@type {JSFoundset<db:/avanti/in_item>} */
	var fs = scopes.avDB.getFS('in_item', ['in_item_to_in_item_class.in_item_class_to_app_item_class_type.itemclasstype_id', 'item_status'], ['P', 'A'], 'item_code')
	return fs

}

/**
 * @return {JSFoundset<db:/avanti/in_item>}
 *
 * @properties={typeid:24,uuid:"DC7B08B4-8B34-4CF9-BE90-13A0A0D043B7"}
 */
function getTabItems() {
	/***@type {JSFoundset<db:/avanti/in_item>} */
	var fs = scopes.avDB.getFS('in_item', ['in_item_to_in_item_class.in_item_class_to_app_item_class_type.itemclasstype_id', 'item_status'], ['TA', 'A'], 'item_code')
	return fs
}

/**
 * @return {JSFoundset<db:/avanti/in_item>}
 *
 * @properties={typeid:24,uuid:"83AF615C-1760-4F91-9513-7F6D5560C1D2"}
 */
function getEnvelopeItems() {
	/***@type {JSFoundset<db:/avanti/in_item>} */
	var fs = scopes.avDB.getFS('in_item', ['in_item_to_in_item_class.in_item_class_to_app_item_class_type.itemclasstype_id', 'item_status'], ['EN', 'A'], 'item_code')
	return fs
}

/**
 * @param {String|UUID} itemID
 * @param {String|UUID} binLocID
 *
 * @return
 * @properties={typeid:24,uuid:"22571E84-C9A8-4668-9F3B-6CEA0A35981D"}
 */
function getBinOHQ(itemID, binLocID) {
	var result = scopes.avDB.getVal('in_item_warehouse_location', ['item_id', 'whseloc_id'], [itemID, binLocID], 'itemwhseloc_onhand_qty');
	return result;
}

/**
 * Gets the item class type, like Area or Unit, from the sys_unit_of_measure_table
 *
 * <AUTHOR> Dotzlaw
 * @since Apr 22, 2015
 * @param {String} sUOM - The UOM id to look up
 * @returns {String} returns the class type
 * @public
 * @AllowToRunInFind
 *
 * @properties={typeid:24,uuid:"E959EDE3-B6D7-418A-B40B-2422774D6F11"}
 */
function getUomClassType(sUOM) {

	var fs = datasources.db.avanti.sys_unit_of_measure.getFoundSet();

	fs.loadAllRecords();

	if (fs.find() || fs.find()) {

		fs.uom_id = sUOM;
		fs.org_id = scopes.globals.org_id;

		fs.search();

		if (fs.getSize() > 0) {

			return fs.uom_class;

		}
		else {

			return null;
		}

	}
	else {

		return null;
	}
}

/**
 * @param {JSRecord<db:/avanti/in_item>} [rItem]
 * @param {String|UUID} [sItemID]
 *
 * @return
 * @properties={typeid:24,uuid:"C0AA2A13-81D1-410E-ACD4-B53AAE17A5C1"}
 */
function isRoll(rItem, sItemID) {

	var
		/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
		oSQL = { },
		/***@type {JSDataSet} */
		dsData;

	if (!rItem && !sItemID) return false;

	if (!sItemID) {

		sItemID = rItem.item_id.toString();
	}
	else {
		sItemID = sItemID.toString(); // convert uuid to string
	}

	if (globals.avBase_oCalcs && globals.avBase_oCalcs.getPaper && globals.avBase_oCalcs.getPaper["'" + sItemID + "'"]) {

		return (globals.avBase_oCalcs.getPaper["'" + sItemID + "'"] == 1) ? true : false;

	}
	else {

		oSQL.sql = "SELECT ict.itemclasstype_code FROM in_item i \
					INNER JOIN in_item_class ic ON i.itemclass_id = ic.itemclass_id \
					INNER JOIN app_item_class_type ict ON ict.itemclasstype_id = ic.itemclass_type \
					WHERE i.item_id = ?";
		oSQL.args = [sItemID];
		dsData = globals["avUtilities_sqlDataset"](oSQL);

		if (dsData && dsData.getMaxRowIndex() > 0) {

			if (dsData.getValue(1, 1) == 'ROLLPAPER') {

				if (globals.avBase_oCalcs && globals.avBase_oCalcs.getPaper) {

					globals.avBase_oCalcs.getPaper["'" + sItemID + "'"] = 1;

				}
				return true;

			}
			else {
				if (globals.avBase_oCalcs && globals.avBase_oCalcs.getPaper) {

					globals.avBase_oCalcs.getPaper["'" + sItemID + "'"] = 2;

				}
				return false;
			}
		}
	}

	return false;

	//	return (rItem && utils.hasRecords(rItem, 'in_item_to_in_item_class.in_item_class_to_app_item_class_type')
	//			&& rItem.in_item_to_in_item_class.in_item_class_to_app_item_class_type.itemclasstype_code == 'ROLLPAPER')

}

/**
 * <AUTHOR> Dotzlaw
 * @since March 3, 2021
 * @param {JSRecord<db:/avanti/sa_order_revision_detail_section> | JSRecord<db:/avanti/sa_task_worktype_section>} rSec
 * @param {String} sForm
 * @properties={typeid:24,uuid:"FCE9DE54-F76C-41B6-86AC-B89240A48C73"}
 * @AllowToRunInFind
 */
function getVL_avItemPatchMaterials(rSec, sForm) {

	var aReturn = [],
		aDisplay = [],
		/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
		oSQL = { },
		/***@type {JSDataSet} */
		dsData;
		
    oSQL.sql = "SELECT i.item_code, i.item_id FROM in_item i \
          INNER JOIN in_item_class ic ON i.itemclass_id = ic.itemclass_id  \
          WHERE i.org_id = ? AND ic.itemclass_type = ? "

    oSQL.args = [globals.org_id, scopes.avInv.patchMaterial];

    oSQL.sql += "ORDER BY item_code ASC ";

    dsData = globals["avUtilities_sqlDataset"](oSQL);

    if (dsData) {
        
        aDisplay = dsData.getColumnAsArray(1);
        aReturn = dsData.getColumnAsArray(2);
    }

	application.setValueListItems("avItemPatchMaterials_front", aDisplay, aReturn);
	application.setValueListItems("avItemPatchMaterials_back", aDisplay, aReturn);
}

/**
 * @param {UUID|String} appUomID
 *
 * @return
 * @properties={typeid:24,uuid:"139B3B89-3CD9-4C78-8269-BD97B0552ED2"}
 */
function getAppUomClass(appUomID) {
	// GD - Nov 15, 2015: SL-6620 - This needs to be sys unit of measure
	return scopes.avDB.getVal('sys_unit_of_measure', ['uom_id'], [appUomID], 'uom_class');
	//	return scopes.avDB.getVal('app_uom', ['appuom_id'], [appUomID], 'appuom_class');
}

/**
 * @param {String} appUomCode
 *
 * @return
 * @properties={typeid:24,uuid:"F30FFB0B-C991-40ED-9270-81E869C779F1"}
 */
function getAppUomIDFromCode(appUomCode) {
	return scopes.avDB.getVal('app_uom', ['appuom_code'], [appUomCode], 'appuom_id');
}

/**
 * @param {String} appUomCode
 *
 * @return
 * @properties={typeid:24,uuid:"7738C8C0-D8B0-4CAB-AA18-2DED46D58F39"}
 */
function getSysUomIDFromAppCode(appUomCode) {
	var uAppUomKey = scopes.avDB.getVal('app_uom', ['appuom_code'], [appUomCode], 'appuom_key');

	if (uAppUomKey) {
		return scopes.avDB.getVal('sys_unit_of_measure', ['appuom_key'], [uAppUomKey], 'uom_id');
	}
	else {
		return null;
	}
}

/**
 * @param {UUID|String} uSysUomID
 *
 * @return {String}
 *
 * @properties={typeid:24,uuid:"37E38A12-674C-465E-AC41-4D88778ABBBD"}
 */
function getAppUomCodeFromSysUomID(uSysUomID) {

	if (!mAppUOMKey) {
		loadUOMMap();
	}
	var oValue = mAppUOMKey[uSysUomID];
	/***@type {String}*/
	var appUOM = null;
	if (oValue) {
		appUOM = oValue.appUomCode;
	}
	return appUOM;
}

/**
 * @param {UUID} uSysUomID
 *
 * @return
 * @properties={typeid:24,uuid:"C062A132-B22E-4B82-B96F-EE190889B988"}
 */
function getCostConvFactorFromSysUomID(uSysUomID) {

	if (!mAppUOMKey) {
		loadUOMMap();
	}
	var oValue = mAppUOMKey[uSysUomID];
	/***@type {Number}*/
	var costConvFactor = null;
	if (oValue) {
		costConvFactor = oValue.costConvFactor;
	}
	return costConvFactor;
}

/**
 * @properties={typeid:24,uuid:"02A3E116-6F0E-40FC-B4C6-B96EA68E710E"}
 */
function loadUOMMap() {
	mAppUOMKey = new Array();
	var sSQL = "SELECT suom.uom_id, suom.uom_default_cost_conv_factor, auom.appuom_code \
	            FROM sys_unit_of_measure suom \
	            LEFT JOIN app_uom auom ON auom.appuom_key=suom.appuom_key \
	            WHERE suom.org_id = ? ";
	var aArgs = [globals.org_id];
	var ds = scopes.avDB.getDataset(sSQL, aArgs);
	if (ds) {
		for (var i = 1; i <= ds.getMaxRowIndex(); i++) {
			var key = ds.getValue(i, 1);
			var oValue = {
				costConvFactor: ds.getValue(i, 2),
				appUomCode: ds.getValue(i, 3)
			};
			mAppUOMKey[key] = oValue;
		}
	}
}

/**
 * Checks to see if the press type should have its inks refreshed
 *
 * <AUTHOR> Dotzlaw
 * @since Feb 5, 2016
 * @param {Number} iTaskTypeId
 * @returns {Boolean}
 * @public
 *
 * @properties={typeid:24,uuid:"DAC2E8DF-E253-49CB-9F46-AB670C829E78"}
 */
function updateInkRecords_pressTypes(iTaskTypeId) {

	if (iTaskTypeId === scopes.avTask.TASKTYPEID.ConventionalPress 
			|| iTaskTypeId === scopes.avTask.TASKTYPEID.FlexoPress 
			|| iTaskTypeId === scopes.avTask.TASKTYPEID.WebPress 
			|| iTaskTypeId === scopes.avTask.TASKTYPEID.GrandFormatPress 
			|| iTaskTypeId === scopes.avTask.TASKTYPEID.DigitalRollPress 
			|| iTaskTypeId === scopes.avTask.TASKTYPEID.WideFormatPress 
			|| iTaskTypeId === scopes.avTask.TASKTYPEID.DigitalSheetPress) {

		return true;

	}
	else {

		return false;
	}
}

/**
 * Checks to see if the inks should be updated for the press
 *
 * <AUTHOR> Dotzlaw
 * @since Feb 5, 2016
 *
 * @param {JSRecord<db:/avanti/sa_order_revision_detail_section>} rSection
 * @param {JSRecord<db:/avanti/sa_task>} [rTask] optional sa_task record for the press we are switching to
 * @param {Boolean} [bCalledFromOnDataChangeColors] - option to avoid going back to onDataChangeColor
 *
 * @public
 *
 * @properties={typeid:24,uuid:"F397A1A2-17E4-4528-B266-E92E4C02FEC5"}
 */
function updateInkRecords_checkUpdateMethodForSection(rSection, rTask, bCalledFromOnDataChangeColors) {

	var fsPressPool = null,
		rFirstPress = null,
		iTaskTypeId = null;

	if (!utils.hasRecords(rSection.sa_order_revision_detail_section_to_sa_order_revds_press_pool)) return;

	if (rTask) {

		iTaskTypeId = rTask.tasktype_id;

	}
	else {

		fsPressPool = rSection.sa_order_revision_detail_section_to_sa_order_revds_press_pool;
		fsPressPool.sort("sequence_nr asc");

		rFirstPress = fsPressPool.getRecord(1);

		if (!utils.hasRecords(rFirstPress.sa_order_revds_press_pool_to_sa_task)) return;
		rTask = rFirstPress.sa_order_revds_press_pool_to_sa_task.getRecord(1);
		iTaskTypeId = rTask.tasktype_id;

	}

	if (utils.hasRecords(rSection.sa_order_revision_detail_section_to_sa_order_revds_ink) && scopes.avInv.updateInkRecords_pressTypes(iTaskTypeId)) {

		// needed to update the coating towers
		forms["_sa_order_est_base"].onDataChange_colors(null, rSection.ordrevds_colours, null, rSection, true, rTask);

		if (!bCalledFromOnDataChangeColors) {

			updateInkRecords_section(rSection, rTask, rSection.sa_order_revision_detail_section_to_sa_order_revds_ink);

		}

	}
	else if (utils.hasRecords(rSection.sa_order_revision_detail_section_to_sa_order_revds_ink) || rSection.ordrevds_colours != "0/0") {

		// regenerate normal inks
		forms["_sa_order_est_base"].onDataChange_colors(null, rSection.ordrevds_colours, null, rSection, true, rTask);

		if (!bCalledFromOnDataChangeColors) {

			updateInkRecords_section(rSection, rTask, rSection.sa_order_revision_detail_section_to_sa_order_revds_ink);

		}
	}
}

/**
 * Changes the ink types for all ink records, using the new press ink type specified on the estimating standard
 *
 * <AUTHOR> Dotzlaw
 * @since Feb 5, 2016
 * @param {JSRecord<db:/avanti/sa_task_worktype_section>} rSection
 * @param {JSRecord<db:/avanti/sa_task>} rPress - The new press
 * @param {JSFoundset<db:/avanti/sa_task_worktype_ink>} fsInks - the foundset of ink records
 * @public
 *
 * @properties={typeid:24,uuid:"6F0ADD4E-9A7E-4966-A920-34DF7A2C62F3"}
 */
function updateInkRecords_worktemplate(rSection, rPress, fsInks) {

	var iInkTypeId = rPress.sa_task_to_sa_task_standard.inktype_id,
		iTaskTypeId = rPress.tasktype_id,
		i = 0,
		rInk = null,
		rInkType = null,
		bSave = false,
		iInkCount = 1,
		sInkSide = "F";

	if (!scopes.avInv.updateInkRecords_pressTypes(iTaskTypeId) || !iInkTypeId || !utils.hasRecords(rPress.sa_task_to_sa_task_standard.sa_task_standard_to_in_ink_type)) {

		iInkTypeId = globals.avBase_getSystemPreference_String(16);
		rInkType = scopes.avDB.getRec("in_ink_type", ["inktype_id"], [iInkTypeId]);

	}
	else {

		rInkType = rPress.sa_task_to_sa_task_standard.sa_task_standard_to_in_ink_type.getRecord(1);

	}

	if (rInkType) {

		for (i = 1; i <= fsInks.getSize(); i++) {

			rInk = fsInks.getRecord(i);

			// Avoid coating towers
			if (rInk.worktypeink_side.slice(0, 1) != "C" && rInk.worktypeink_side.slice(0, 2) != "FC" && rInk.worktypeink_side.slice(0, 2) != "BC") {

				rInk.inktype_id = rInkType.inktype_id;

				if (sInkSide === "F" && rInk.worktypeink_side === "B") {

					sInkSide = "B";
					iInkCount = 1;
				}

				if (rInk.inktype_id) {

					// Set the coverage
					if (rInkType.inktype_default_coverage != null) {

						rInk.worktypeink_coverage = rInkType.inktype_default_coverage * 100;

					}
					else {

						rInk.worktypeink_coverage = rSection.worktypesection_ink_coverage;
					}

					// Set the color
					if (rInkType.inktype_is_process == 1) {
						// Add Black first
						if (iInkCount == 1) {
							rInk.worktypeink_color = i18n.getI18NMessage("avanti.lbl.processInkBlack");
							rInk.itemink_id = scopes.avInv.processBlack;
						}
						else if (iInkCount == 2) {
							rInk.worktypeink_color = i18n.getI18NMessage("avanti.lbl.processInkMagenta");
							rInk.itemink_id = scopes.avInv.processMagenta;
						}
						else if (iInkCount == 3) {
							rInk.worktypeink_color = i18n.getI18NMessage("avanti.lbl.processInkYellow");
							rInk.itemink_id = scopes.avInv.processYellow;
						}
						else if (iInkCount == 4) {
							rInk.worktypeink_color = i18n.getI18NMessage("avanti.lbl.processInkCyan");
							rInk.itemink_id = scopes.avInv.processCyan;
						}
						else {
							rInk.worktypeink_color = i18n.getI18NMessage("avanti.lbl.processInkBlack");
							rInk.itemink_id = scopes.avInv.processBlack;
						}
					}
					else {

						rInk.worktypeink_color = null;
						rInk.itemink_id = null;
					}
				}

				iInkCount += 1;

				bSave = true;
			}
		}
	}
	if (bSave) databaseManager.saveData();
}

/**
 * Changes the ink types for all ink records, using the new press ink type specified on the estimating standard
 *
 * <AUTHOR> Dotzlaw
 * @since Feb 5, 2016
 * @param {JSRecord<db:/avanti/sa_order_revision_detail_section>} rSection
 * @param {JSRecord<db:/avanti/sa_task>} rPress - The new press
 * @param {JSFoundset<db:/avanti/sa_order_revds_ink>} fsInks - the foundset of ink records
 * @param {JSRecord<db:/avanti/sa_order_revds_page_set>} [rPageSet]
 * @public
 *
 * @properties={typeid:24,uuid:"6D66C519-CABE-46F6-8C4A-C50D30635DE3"}
 */
function updateInkRecords_section(rSection, rPress, fsInks, rPageSet) {

	var iInkTypeId = rPress.sa_task_to_sa_task_standard.inktype_id,
		iTaskTypeId = rPress.tasktype_id,
		i = 0,
		rInk = null,
		rInkType = null,
		bSave = false,
		iInkCount = 1,
		sInkSide = "F";

	if (!scopes.avInv.updateInkRecords_pressTypes(iTaskTypeId) || !iInkTypeId || !utils.hasRecords(rPress.sa_task_to_sa_task_standard.sa_task_standard_to_in_ink_type)) {

		iInkTypeId = globals.avBase_getSystemPreference_String(16);
		rInkType = scopes.avDB.getRec("in_ink_type", ["inktype_id"], [iInkTypeId]);

	}
	else {

		rInkType = rPress.sa_task_to_sa_task_standard.sa_task_standard_to_in_ink_type.getRecord(1);

	}

	if (rInkType) {

		for (i = 1; i <= fsInks.getSize(); i++) {

			rInk = fsInks.getRecord(i);

			// Avoid coating towers
			if (rInk.ordrevdsink_side.slice(0,1) != "C" 
				&& rInk.ordrevdsink_side.slice(0,2) != "FC" 
				&& rInk.ordrevdsink_side.slice(0,2) != "BC" ) {
					
				if (sInkSide === "F" && rInk.ordrevdsink_side === "B") {

					sInkSide = "B";
					iInkCount = 1;
				}
				
				// sl-19413 - dont change if old and new ink type are the same 
				if (rInkType.inktype_id && rInkType.inktype_id != rInk.inktype_id) {

					rInk.inktype_id = rInkType.inktype_id;
					rInk.ordrevdsink_qty = null;
					
					// Set the coverage
					if (rInkType.inktype_default_coverage != null) {

						rInk.ordrevdsink_coverage = rInkType.inktype_default_coverage * 100;

					}
					else {
						if (rPageSet) {
							rInk.ordrevdsink_coverage = rPageSet.ordrevdsps_ink_coverage;
						}
						else {
							rInk.ordrevdsink_coverage = rSection.ordrevds_ink_coverage;
						}
					}

					// Set the color
					if (rInkType.inktype_is_process == 1) {
						// Add Black first
						if (iInkCount == 1) {
							rInk.ordrevdsink_color = i18n.getI18NMessage("avanti.lbl.processInkBlack");
							rInk.itemink_id = scopes.avInv.processBlack;
						}
						else if (iInkCount == 2) {
							rInk.ordrevdsink_color = i18n.getI18NMessage("avanti.lbl.processInkMagenta");
							rInk.itemink_id = scopes.avInv.processMagenta;
						}
						else if (iInkCount == 3) {
							rInk.ordrevdsink_color = i18n.getI18NMessage("avanti.lbl.processInkYellow");
							rInk.itemink_id = scopes.avInv.processYellow;
						}
						else if (iInkCount == 4) {
							rInk.ordrevdsink_color = i18n.getI18NMessage("avanti.lbl.processInkCyan");
							rInk.itemink_id = scopes.avInv.processCyan;
						}
						else {
							rInk.ordrevdsink_color = i18n.getI18NMessage("avanti.lbl.processInkBlack");
							rInk.itemink_id = scopes.avInv.processBlack;
						}
					}
					else {

						rInk.ordrevdsink_color = null;
						rInk.itemink_id = null;
					}
					
					bSave = true;
				}

				iInkCount += 1;
			}
		}
	}
	
	if (bSave) {
		databaseManager.saveData();
	}
}

/**
 * Called when the valuelist needs data, it has 3 modes.
 * real and display params both null: return the whole list
 * only display is specified, called by a typeahead, return a filtered list
 * only real value is specified, called when the list doesnt contain the real value for the give record value, this will insert this value into the existing list
 *
 * @param {String} displayValue The value of a lookupfield that a user types
 * @param realValue The real value for a lookupfield where a display value should be get for
 * @param {JSRecord<db:/avanti/in_trans_entry_detail>} record The current record for the valuelist.
 * @param {String} valueListName The valuelist name that triggers the method. (This is the FindRecord in find mode, which is like JSRecord has all the columns/dataproviders, but doesn't have its methods)
 * @param {Boolean} findMode True if foundset of this record is in find mode
 * @param {Boolean} rawDisplayValue The raw displayValue without being converted to lower case
 *
 * @returns {JSDataSet} A dataset with 1 or 2 columns display[,real]
 *
 * @properties={typeid:24,uuid:"7EBD8FB9-B827-4FEE-943D-FC6FBF2E080C"}
 */
function getDataSetForValueList_ItemsForTransactions(displayValue, realValue, record, valueListName, findMode, rawDisplayValue) {

	var bRecordObject = false;
	if (record != null) {
		try {
			if (record.getDataSource() == "db:/avanti/in_trans_entry_detail") {
				bRecordObject = true;
			}
		} catch (e) {
			return null;
		}
	}

	var args = null;
	/** @type QBSelect<db:/avanti/in_item> */
	var query = databaseManager.createSelect('db:/avanti/in_item');
	/** @type  {JSDataSet} */
	var result = null;

	if (displayValue == null && realValue == null) {
		// TODO think about caching this result. can be called often!
		// return the complete list
		query.result.add(query.columns.item_code.concat(': ').concat(query.columns.item_desc1)).add(query.columns.item_id);
		query.where.add(query.columns.org_id.eq(globals.org_id));

		if (bRecordObject 
				&& record.in_trans_entry_detail_to_in_trans_entry_header != null 
				&& record.in_trans_entry_detail_to_in_trans_entry_header.in_trans_entry_header_to_in_transaction_type != null 
				&& record.in_trans_entry_detail_to_in_trans_entry_header.in_trans_entry_header_to_in_transaction_type.intranstype_trans_code == 'PR') {
			query.where.add(query.columns.itemtype_code.isin(['F']));
		}
		else {
			query.where.add(query.columns.itemtype_code.not.isin(['ZZ', 'P', 'SE']));
		}

		query.sort.add(query.columns.item_code.asc);
		result = databaseManager.getDataSetByQuery(query, 1000);
	}
	else if (displayValue != null) {
		// TYPE_AHEAD filter call, return a filtered list
		args = [displayValue + "%", displayValue + "%"];
		query.result.add(query.columns.item_code.concat(': ').concat(query.columns.item_desc1)).add(query.columns.item_id);
		if (bRecordObject 
				&& record.in_trans_entry_detail_to_in_trans_entry_header != null 
				&& record.in_trans_entry_detail_to_in_trans_entry_header.in_trans_entry_header_to_in_transaction_type != null 
				&& record.in_trans_entry_detail_to_in_trans_entry_header.in_trans_entry_header_to_in_transaction_type.intranstype_trans_code == 'PR') {
			query.where.add(query.and.add(query.columns.org_id.eq(globals.org_id)).add(query.columns.itemtype_code.isin(['F'])).add(query.or.add(query.columns.item_code.lower.like('%' + args[0] + '%')).add(query.columns.item_desc1.lower.like('%' + args[1] + '%'))));
		}
		else {
			query.where.add(query.and.add(query.columns.org_id.eq(globals.org_id)).add(query.columns.itemtype_code.not.isin(['ZZ', 'P', 'SE'])).add(query.or.add(query.columns.item_code.lower.like('%' + args[0] + '%')).add(query.columns.item_desc1.lower.like('%' + args[1] + '%'))));
		}
		query.sort.add(query.columns.item_code.asc);
		result = databaseManager.getDataSetByQuery(query, 1000);
	}
	else if (realValue != null) {
		// TODO think about caching this result. can be called often!
		// real object not found in the current list, return 1 row with display,realvalue that will be added to the current list
		// dont return a complete list in this mode because that will be added to the list that is already there
		
		var re = new RegExp("[A-Fa-f0-9]{8}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{12}");

		if (re.test(realValue)) {
			args = [realValue];
			query.result.add(query.columns.item_code.concat(': ').concat(query.columns.item_desc1)).add(query.columns.item_id);
			if (bRecordObject 
					&& record.in_trans_entry_detail_to_in_trans_entry_header != null 
					&& record.in_trans_entry_detail_to_in_trans_entry_header.in_trans_entry_header_to_in_transaction_type != null 
					&& record.in_trans_entry_detail_to_in_trans_entry_header.in_trans_entry_header_to_in_transaction_type.intranstype_trans_code == 'PR') {
				query.where.add(query.and.add(query.columns.item_id.eq(args[0].toString())).add(query.columns.org_id.eq(globals.org_id)).add(query.columns.itemtype_code.isin(['F'])));
			}
			else {
				query.where.add(query.and.add(query.columns.item_id.eq(args[0].toString())).add(query.columns.org_id.eq(globals.org_id)).add(query.columns.itemtype_code.not.isin(['ZZ', 'P', 'SE'])));
			}
			query.sort.add(query.columns.item_code.asc);
			result = databaseManager.getDataSetByQuery(query, 1);
		}
	}

	return result;
}

/**
 * 
 * Called when the valuelist needs data, it has 3 modes.
 * real and display params both null: return the whole list
 * only display is specified, called by a typeahead, return a filtered list
 * only real value is specified, called when the list doesnt contain the real value for the give record value, this will insert this value into the existing list
 *
 * @param {String} displayValue The value of a lookupfield that a user types
 * @param realValue The real value for a lookupfield where a display value should be get for
 * @param {JSRecord<db:/avanti/in_trans_entry_detail>} record The current record for the valuelist.
 * @param {String} valueListName The valuelist name that triggers the method. (This is the FindRecord in find mode, which is like JSRecord has all the columns/dataproviders, but doesn't have its methods)
 * @param {Boolean} findMode True if foundset of this record is in find mode
 * @param {Boolean} rawDisplayValue The raw displayValue without being converted to lower case
 *
 * @returns {JSDataSet} A dataset with 1 or 2 columns display[,real]
 *
 * @properties={typeid:24,uuid:"13F08551-AFD6-43EE-A64B-85386F14C81C"}
 */
function getDataSetForValueList_ItemsForReturns(displayValue, realValue, record, valueListName, findMode, rawDisplayValue) {

	var bRecordObject = false;
	if (record != null) {
		try {
			if (record.getDataSource() == "db:/avanti/sa_return_detail") {
				bRecordObject = true;
			}
		} catch (e) {
			return null;
		}
	}

	var args = null;
	/** @type QBSelect<db:/avanti/in_item> */
	var query = databaseManager.createSelect('db:/avanti/in_item');
	/** @type  {JSDataSet} */
	var result = null;

	if (displayValue == null && realValue == null) {
		
		if (!result) {
			// return the complete list
			query.result.add(query.columns.item_code.concat(': ').concat(query.columns.item_desc1)).add(query.columns.item_id);
			query.where.add(query.columns.org_id.eq(globals.org_id));
			query.where.add(query.columns.itemtype_code.isin(['F', 'S', 'N']));
			query.sort.add(query.columns.item_code.asc);
			result = databaseManager.getDataSetByQuery(query, 1000);
		}
	}
	else if (displayValue != null) {
		// TYPE_AHEAD filter call, return a filtered list
		args = [displayValue + "%", displayValue + "%"];
		query.result.add(query.columns.item_code.concat(': ').concat(query.columns.item_desc1)).add(query.columns.item_id);
		query.where.add(query.and.add(query.columns.org_id.eq(globals.org_id)).add(query.columns.itemtype_code.isin(['F'])).add(query.or.add(query.columns.item_code.lower.like('%' + args[0] + '%')).add(query.columns.item_desc1.lower.like('%' + args[1] + '%'))));
		query.sort.add(query.columns.item_code.asc);
		result = databaseManager.getDataSetByQuery(query, 1000);
	}
	else if (realValue != null) {
		var re = new RegExp("[A-Fa-f0-9]{8}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{12}");

		if (re.test(realValue)) {
			args = [realValue];
			query.result.add(query.columns.item_code.concat(': ').concat(query.columns.item_desc1)).add(query.columns.item_id);
			query.where.add(query.and.add(query.columns.item_id.eq(args[0].toString())).add(query.columns.org_id.eq(globals.org_id)).add(query.columns.itemtype_code.isin(['F'])));
			query.sort.add(query.columns.item_code.asc);
			result = databaseManager.getDataSetByQuery(query, 1);
		}
	}

	return result;
}

/**
 * @public
 *
 * @return {String} - U or M
 *
 * @properties={typeid:24,uuid:"6C3D2BD7-403C-46B3-AA95-099E2C45CC87"}
 */
function useUniqueRollIDOrMillNumberForTrackedRolls() {
	if (scopes.avDB.Exists('in_item_class', ['itemclass_show_roll_unique_id'], [1])) {
		return 'U';
	}
	else if (scopes.avDB.Exists('in_item_class', ['itemclass_show_mill_roll_num'], [1])) {
		return 'M';
	}
	else {
		return null;
	}
}

/**
 * @AllowToRunInFind
 *
 * Return the matching in_item_warehouse record for the given item_id and
 * whse_id (or null if no match is found)
 *
 * @param {UUID} sItemId - item_id
 * @param {UUID} sWarehouseId - whse_id
 * @param {Boolean} [bBypassErrorOutput]
 *
 * @properties={typeid:24,uuid:"9C1F7C7E-21BD-443D-AE45-64578D11211C"}
 *
 * @return {JSRecord<db:/avanti/in_item_warehouse>}
 */
function getRelatedInItemWarehouseRecord(sItemId, sWarehouseId, bBypassErrorOutput) {

	/** @type JSFoundSet<db:/avanti/in_item_warehouse> */
	var fsInItemWarehouse = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_warehouse');

	if (fsInItemWarehouse.find()) {

		fsInItemWarehouse.item_id = sItemId;
		fsInItemWarehouse.whse_id = sWarehouseId;

		if (fsInItemWarehouse.search() > 0) {
			return fsInItemWarehouse.getRecord(1);
		}
		else {
			if (!bBypassErrorOutput) {
				application.output('in_item_warehouse_count_dtl.js:getRelatedInItemWarehouseRecord():ERROR:No match found for item_id=' + sItemId + ',warehouse_id=' + sWarehouseId, LOGGINGLEVEL.WARNING);
			}
			
			return null;
		}
	}
	else {
		if (!bBypassErrorOutput) {
			application.output('in_item_warehouse_count_dtl.js:getRelatedInItemWarehouseRecord():ERROR:No in_item_warehouse records found.', LOGGINGLEVEL.WARNING);
		}
		
		return null;
	}

}

/**
 * Called when the valuelist needs data, it has 3 modes.
 * real and display params both null: return the whole list
 * only display is specified, called by a typeahead, return a filtered list
 * only real value is specified, called when the list doesnt contain the real value for the give record value, this will insert this value into the existing list
 *
 * @param {String} displayValue The value of a lookupfield that a user types
 * @param realValue The real value for a lookupfield where a display value should be get for
 * @param {JSRecord} record The current record for the valuelist.
 * @param {String} valueListName The valuelist name that triggers the method. (This is the FindRecord in find mode, which is like JSRecord has all the columns/dataproviders, but doesn't have its methods)
 * @param {Boolean} findMode True if foundset of this record is in find mode
 * @param {Boolean} rawDisplayValue The raw displayValue without being converted to lower case
 *
 * @returns {JSDataSet} A dataset with 1 or 2 columns display[,real]
 *
 * @properties={typeid:24,uuid:"CA03B3FC-94BE-434B-901C-7CCAC27937DF"}
 */
function getDataSetForValueList_TransactionsForInvReceipts(displayValue, realValue, record, valueListName, findMode, rawDisplayValue) {
	var args = null;
	/** @type  {JSDataSet} */
	var result = null;

	try {
		if (displayValue == null && realValue == null) {
			args = [globals.org_id];
			result = databaseManager.getDataSetByQuery("avanti", "SELECT in_trans_entry_header.intraneh_reference AS displayValue, intraneh_id \
					FROM in_trans_entry_header INNER JOIN \
	                in_transaction_type ON in_trans_entry_header.intranstype_id = in_transaction_type.intranstype_id\
	                WHERE (in_trans_entry_header.org_id = ?) AND \
	                (in_transaction_type.intranstype_trans_code = N'PR') AND \
	                (in_trans_entry_header.intraneh_complete <> 1) AND \
	                (in_trans_entry_header.intraneh_status_field = 'U' OR in_trans_entry_header.intraneh_status_field = 'P') AND \
	                (in_trans_entry_header.intraneh_complete = 0) \
	                ORDER BY in_trans_entry_header.intraneh_reference", args, 100);

		}
		else if (displayValue != null) {
			// TYPE_AHEAD filter call, return a filtered list
			args = [globals.org_id, displayValue + "%"];
			result = databaseManager.getDataSetByQuery("avanti", "SELECT in_trans_entry_header.intraneh_reference AS displayValue, intraneh_id \
				FROM in_trans_entry_header INNER JOIN \
	            in_transaction_type ON in_trans_entry_header.intranstype_id = in_transaction_type.intranstype_id\
	            WHERE (in_trans_entry_header.org_id = ?) AND \
	            (in_transaction_type.intranstype_trans_code = N'PR') AND \
	            (in_trans_entry_header.intraneh_complete <> 1) AND \
	            (in_trans_entry_header.intraneh_status_field = 'U' OR in_trans_entry_header.intraneh_status_field = 'P') AND \
	            (in_trans_entry_header.intraneh_complete = 0) AND \
	            (in_trans_entry_header.intraneh_reference LIKE ?) \
	            ORDER BY in_trans_entry_header.intraneh_reference", args, 100);
		}
		else if (realValue != null) {
			// TODO think about caching this result. can be called often!
			// real object not found in the current list, return 1 row with display,realvalue that will be added to the current list
			// dont return a complete list in this mode because that will be added to the list that is already there
			var re = new RegExp("[A-Fa-f0-9]{8}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{12}");

			if (re.test(realValue)) {
				args = [globals.org_id, realValue.toString()];
				result = databaseManager.getDataSetByQuery("avanti", "SELECT in_trans_entry_header.intraneh_reference AS displayValue, intraneh_id \
					FROM in_trans_entry_header INNER JOIN\
		            in_transaction_type ON in_trans_entry_header.intranstype_id = in_transaction_type.intranstype_id\
		            WHERE (in_trans_entry_header.org_id = ?) AND \
		            (in_trans_entry_header.intraneh_id = ? )", args, 1);
			}
		}
		return result;
	} catch (e) {
		// TODO: handle exception
	}
}

/**
 * @public
 * @param {String} sSheetSize
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"EFC81B57-6DE2-4F6F-A510-C46F602ADF66"}
 */
function isSheetSizeValid(sSheetSize) {
	var aNums = [];

	if (sSheetSize.indexOf('x') > -1) {
		aNums = sSheetSize.split('x');
	}
	else if (sSheetSize.indexOf('X') > -1) {
		aNums = sSheetSize.split('X');
	}

	if (aNums.length == 2 && scopes.avMath.isNumber(aNums[0]) && scopes.avMath.isNumber(aNums[1]) && aNums[0] > 0 && aNums[1] > 0) {
		return true;
	}
	else {
		return false;
	}
}

/**
 * @public
 * @param {String} sSheetSize
 * @param {Number} nDimNum
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"B70712DD-33C1-4BC9-A02D-7DC3091F4CF6"}
 */
function getSheetDimFromSizeString(sSheetSize, nDimNum) {
	var aNums = [];

	if (sSheetSize.indexOf('x') > -1) {
		aNums = sSheetSize.split('x');
	}
	else if (sSheetSize.indexOf('X') > -1) {
		aNums = sSheetSize.split('X');
	}

	if (aNums.length == 2 && scopes.avMath.isNumber(aNums[0]) && scopes.avMath.isNumber(aNums[1]) && aNums[0] > 0 && aNums[1] > 0) {
		if (nDimNum == 1) {
			return parseFloat(aNums[0]);
		}
		else if (nDimNum == 2) {
			return parseFloat(aNums[1]);
		}
	}

	return null;
}

/**
 * @public
 *
 * @param sItemID
 *
 * @return {String}
 *
 * @properties={typeid:24,uuid:"F8EABC95-7AC7-46F8-82BC-B8ABE0AB0E57"}
 */
function getItemClassType(sItemID) {
	/**@type {JSRecord<db:/avanti/in_item>} */
	var rItem = scopes.avDB.getRec('in_item', ['item_id'], [sItemID]);

	if (rItem && utils.hasRecords(rItem.in_item_to_in_item_class)) {
		return rItem.in_item_to_in_item_class.itemclass_type;
	}
	else {
		return null;
	}
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/in_item>} rItem
 *
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"16ACD0AD-402D-4BC9-962F-5B48549E7DAB"}
 */
function getSubstrateWidth(rItem) {
	return getSubstrateDim(rItem, 'w');
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/in_item>} rItem
 *
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"1C3A635F-9B5A-4595-99D2-25895502FEE4"}
 */
function getSubstrateLength(rItem) {
	return getSubstrateDim(rItem, 'l');
}

/**
 * @private
 *
 * @param {JSRecord<db:/avanti/in_item>} rItem
 * @param {String} sDim
 *
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"5924DFFA-55EC-43C8-85B0-DFB9CCD55059"}
 */
function getSubstrateDim(rItem, sDim) {
	var nItemW = 0;
	var nItemL = 0;
	var aPaperClassTypes = ['P', 'R', 'TA', 'EN'];

	if (rItem) {
		var sClassType = rItem.in_item_to_in_item_class.itemclass_type;

		if (aPaperClassTypes.indexOf(sClassType) > -1 && utils.hasRecords(rItem.in_item_to_in_item_paper)) {
			nItemW = rItem.in_item_to_in_item_paper.paper_first_dim;

			if (sClassType == 'R' && rItem.item_track_rolls == 1) {
				nItemL = rItem.in_item_to_in_item_paper.paper_roll_avg_length;
			}
			else {
				nItemL = rItem.in_item_to_in_item_paper.paper_second_dim;
			}
		}
		else {
			nItemW = rItem.item_dimension_width;
			nItemL = rItem.item_dimension_length;
		}
	}

	if (sDim == 'w') {
		return nItemW;
	}
	else if (sDim == 'l') {
		return nItemL;
	}
	else {
		return 0;
	}
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/in_item>} rItem
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"26E70BD6-ECDB-4B80-8078-FF4B75824BA5"}
 */
function isRoll_includingStockingUomTest(rItem) {
	if (rItem && utils.hasRecords(rItem.in_item_to_in_item_class)) {
		var rItemClass = rItem.in_item_to_in_item_class.getRecord(1);

		if (rItemClass.itemclass_type == "R") {
			return true;
		}
		else if (utils.hasRecords(rItem, 'in_item_to_sys_unit_of_measure_stocking_uom.sys_unit_of_measure_to_app_uom')) {
			if (rItem.in_item_to_sys_unit_of_measure_stocking_uom.sys_unit_of_measure_to_app_uom.appuom_code == 'RL') {
				return true;
			}
		}
	}

	return false;
}

/**
 * Gets the addresses for the Invoice Account (cust_id) specified on the item
 *
 * <AUTHOR> Dotzlaw
 * @since Nov 19, 2016
 * @param {String} sCustID
 * @public
 *
 * @properties={typeid:24,uuid:"F2C6FA70-797D-4418-B817-B8338A64BD27"}
 */
function getVL_itemCustomerAddresses(sCustID) {
	var aReturn = [],
		aDisplay = [],
		i = 0,
		iMax = 0,
		/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
		oSQL = { },
		/***@type {JSDataSet} */
		dsData;

	if (sCustID) {
		oSQL.sql = "SELECT custaddr_id, custaddr_code, custaddr_address_name \
					FROM sa_customer_address \
					WHERE \
						org_id = ? \
						AND cust_id = ? \
						AND ISNULL(custaddr_one_time, 0) = 0 \
						AND custaddr_active = 1  \
					ORDER BY custaddr_code ASC, custaddr_address_name ASC";
		oSQL.args = [globals.org_id, sCustID];
		
		dsData = globals["avUtilities_sqlDataset"](oSQL);

		if (dsData) {
			iMax = dsData.getMaxRowIndex();
			
			for (i = 1; i <= iMax; i++) {
				aDisplay.push(dsData.getValue(i, 2) + ": " + dsData.getValue(i, 3));
				aReturn.push(dsData.getValue(i, 1));
			}
		}
	}

	application.setValueListItems("vl_itemCustomerAddresses", aDisplay, aReturn);
}

/**
 * Gets the primary custaddr_id for a given cust_id
 *
 * <AUTHOR> Dotzlaw
 * @since Nov 19, 2016
 * @param {String} sCustID
 * @public
 *
 * @return
 * @properties={typeid:24,uuid:"74658D28-2330-4FEA-89B3-128821622484"}
 */
function getCustomerPrimaryAddress(sCustID) {

	var
		/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
		oSQL = { },
		/***@type {JSDataSet} */
		dsData;

	oSQL.sql = "SELECT custaddr_id FROM sa_customer_address WHERE cust_id = ? AND custaddr_code = ?";
	oSQL.args = [sCustID, "PRIMARY"];
	dsData = globals["avUtilities_sqlDataset"](oSQL);

	if (dsData && dsData.getMaxRowIndex() > 0) {

		return dsData.getValue(1, 1);
	}

	return null;
}

/**
 * Gets the employees Divisions (string of UUIDs)
 *
 * <AUTHOR> Dotzlaw
 * @since 01/25/2018
 * @return {String}
 * @public
 * @properties={typeid:24,uuid:"42A983A7-4B49-404F-B987-3B03E11E4B28"}
 */
function getEmpDivs() {

	var rDiv = null,
		i = 0,
		sDivIds = "",
		sEmpBak = globals.avEmpSetup_curEmployeeID;

	globals.avEmpSetup_curEmployeeID = globals.avBase_employeeUUID;
	;

	for (i = 1; i <= _to_sys_employee_div$cur_emp.getSize(); i++) {

		rDiv = _to_sys_employee_div$cur_emp.getRecord(i);

		sDivIds += "'" + rDiv.div_id + "',";
	}

	if (sDivIds.length > 0) sDivIds = sDivIds.slice(0, sDivIds.length - 1);

	globals.avEmpSetup_curEmployeeID = sEmpBak;

	return sDivIds;
}

/**
 * Gets the employees Plants (string of UUIDs)
 *
 * <AUTHOR> Dotzlaw
 * @since 01/25/2018
 * @return {String}
 * @public
 * @properties={typeid:24,uuid:"D4A8930F-BD05-49AE-ADD3-015A9C5AD388"}
 */
function getEmpPlants() {

	var i = 0,
		rPlant = null,
		sPlantIds = "",
		bCheckDiv = false,
		sEmpBak = globals.avEmpSetup_curEmployeeID;

	globals.avEmpSetup_curEmployeeID = globals.avBase_employeeUUID;

	// plants for all divs
	if (globals.avBase_divID && globals.avBase_divID != i18n.getI18NMessage('avanti.lblAllCaps')) {
		bCheckDiv = true;
	}

	if (utils.hasRecords(_to_sys_employee_plant$cur_setup_emp)) {

		for (i = 1; i <= _to_sys_employee_plant$cur_setup_emp.getSize(); i++) {

			rPlant = _to_sys_employee_plant$cur_setup_emp.getRecord(i);

			if (!bCheckDiv || rPlant.sys_employee_plant_to_sys_plant.div_id == globals.avBase_divID) {

				sPlantIds += "'" + rPlant.plant_id + "',";
			}
		}

		if (sPlantIds.length > 0) sPlantIds = sPlantIds.slice(0, sPlantIds.length - 1);
	}
	globals.avEmpSetup_curEmployeeID = sEmpBak;

	return sPlantIds;
}

/**
 * Will create comments for the invoice
 *
 * <AUTHOR> Dotzlaw
 * @since Feb 13, 2018
 * @param {JSRecord<db:/avanti/sa_invoice_det>} rInvDet
 * @param {Number} [iCount]
 * @return {Number} iCount will be returned, so you can pass it back in again
 * @public
 *
 * @properties={typeid:24,uuid:"07D4497D-269A-4632-BB54-3821AC0583F1"}
 */
function createInvoiceComments(rInvDet, iCount) {

	var i = 0,
		k = 0,
		j = 0,
		m = 0,
		rComment = null,
		rDetail = null,
		rSection = null,
		rTask = null,
		sKey = "",
		sKeyNew = "",
		fs = null;

	if (!iCount) {

		iCount = 1;

	}

	if (iCount == 1) {

		aInvComments = [];
		aInvCommentsNew = [];
	}

	if (utils.hasRecords(rInvDet.sa_invoice_det_to_sa_order) && utils.hasRecords(rInvDet.sa_invoice_det_to_sa_order.sa_order_to_sys_comment$invoice)) {

		fs = rInvDet.sa_invoice_det_to_sa_order.sa_order_to_sys_comment$invoice;
		fs.sort('sequence_nr asc');

		for (i = 1; i <= fs.getSize(); i++) {
			rComment = fs.getRecord(i);
			sKey = rInvDet.sa_invoice_det_to_sa_order.ordh_id.toString() + "_" + rComment.comment_id.toString();

			if (aInvComments.indexOf(sKey) == -1 && aInvCommentsNew.indexOf(sKey) == -1) {
				rComment = fs.getRecord(fs.duplicateRecord(i, false, true));
				rComment.inv_id = rInvDet.inv_id;
				rComment.ordh_id = null;
				rComment.sequence_nr = iCount;
				iCount++;

				aInvComments.push(sKey);

				sKeyNew = rInvDet.sa_invoice_det_to_sa_order.ordh_id.toString() + "_" + rComment.comment_id.toString();
				aInvCommentsNew.push(sKeyNew);
			}
		}
	}

	// Process Detail lines
	if (utils.hasRecords(rInvDet.sa_invoice_det_to_sa_order_revision_detail)) {
		rDetail = rInvDet.sa_invoice_det_to_sa_order_revision_detail.getRecord(1);

		// Process Detail comments
		fs = rDetail.sa_order_revision_detail_to_sys_comment$invoice;

		if (utils.hasRecords(fs)) {
			var sCommentString = null;

			fs.sort('sequence_nr asc');

			for (i = 1; i <= fs.getSize(); i++) {
				rComment = fs.getRecord(i);
				sKey = rInvDet.inv_id.toString() + "_" + rDetail.ordrevd_id.toString() + "_" + rComment.comment_id.toString();

				if (aInvComments.indexOf(sKey) == -1 && aInvCommentsNew.indexOf(sKey) == -1) {
					aInvComments.push(sKey);
					sKeyNew = rInvDet.inv_id.toString() + "_" + rDetail.ordrevd_id.toString() + "_" + rComment.comment_id.toString();
					aInvCommentsNew.push(sKeyNew);

					if (sCommentString) {
						sCommentString += "\n\n";
					}

					sCommentString += rComment.comment_desc;
				}
			}

			rInvDet.invd_comments = sCommentString;
		}

		// Process Sections for the Detail line
		if (utils.hasRecords(rDetail.sa_order_revision_detail_to_sa_order_revision_detail_section$is_deleted)) {
			rDetail.sa_order_revision_detail_to_sa_order_revision_detail_section$is_deleted.sort('sequence_nr asc');

			for (i = 1; i <= rDetail.sa_order_revision_detail_to_sa_order_revision_detail_section$is_deleted.getSize(); i++) {
				rSection = rDetail.sa_order_revision_detail_to_sa_order_revision_detail_section$is_deleted.getRecord(i);

				// Process Section comments
				fs = rSection.sa_order_revision_detail_section_to_sys_comment$invoice;

				if (utils.hasRecords(fs)) {
					fs.sort('sequence_nr asc');

					for (k = 1; k <= fs.getSize(); k++) {
						rComment = fs.getRecord(k);
						sKey = rInvDet.inv_id.toString() + "_" + rSection.ordrevds_id.toString() + "_" + rComment.comment_id.toString();

						if (aInvComments.indexOf(sKey) == -1 && aInvCommentsNew.indexOf(sKey) == -1) {
							rComment = fs.getRecord(fs.duplicateRecord(k, false, true));
							rComment.inv_id = rInvDet.inv_id;
							rComment.invd_id = rInvDet.invd_id;
							rComment.ordrevds_id = null;
							rComment.sequence_nr = iCount;
							iCount++;

							aInvComments.push(sKey);

							sKeyNew = rInvDet.inv_id.toString() + "_" + rSection.ordrevds_id.toString() + "_" + rComment.comment_id.toString();
							aInvCommentsNew.push(sKeyNew);
						}
					}
				}

				// Process Tasks for the Section
				rSection.sa_order_revision_detail_section_to_sa_order_revds_task$is_deleted.sort('sort_key asc');
				for (j = 1; j <= rSection.sa_order_revision_detail_section_to_sa_order_revds_task$is_deleted.getSize(); j++) {
					rTask = rSection.sa_order_revision_detail_section_to_sa_order_revds_task$is_deleted.getRecord(j);

					// Process Task comments
					fs = rTask.sa_order_revds_task_to_sys_comment$invoice;
					fs.sort('sequence_nr asc');

					if (utils.hasRecords(fs)) {
						for (m = 1; m <= fs.getSize(); m++) {
							rComment = fs.getRecord(m);
							sKey = rInvDet.inv_id.toString() + "_" + rTask.ordrevdstask_id.toString() + "_" + rComment.comment_id.toString();

							if (aInvComments.indexOf(sKey) == -1 && aInvCommentsNew.indexOf(sKey) == -1) {
								rComment = fs.getRecord(fs.duplicateRecord(m, false, true));
								rComment.inv_id = rInvDet.inv_id;
								rComment.invd_id = rInvDet.invd_id;
								rComment.ordrevdstask_id = null;
								rComment.sequence_nr = iCount;
								iCount++;

								aInvComments.push(sKey);

								sKeyNew = rInvDet.inv_id.toString() + "_" + rTask.ordrevdstask_id.toString() + "_" + rComment.comment_id.toString();
								aInvCommentsNew.push(sKeyNew);
							}
						}
					}
				}
			}
		}
	}

	return iCount;
}

/**
 * Sets the item statistics, which might get filtered by Div/Plant
 * <AUTHOR> Dotzlaw
 * @since Jan 25, 2018
 * @param {UUID} uItemUUID
 * @param {Number} nQtyType
 *
 * @return {Number}
 * @public
 *
 * @properties={typeid:24,uuid:"1740B484-E0D4-4DCE-9F71-362723FB40CB"}
 */
function getItemStatistics(uItemUUID, nQtyType) {

	var
		/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
		oSQL = { },
		/***@type {JSDataSet} */
		dsData,
		sPlantID = null,
		sDivID = null,
		/**@type {Number} */
		nQuantity = 0;

	if (!uItemUUID) {
		return nQuantity;
	}

	if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.DivPlantFilter)) {

		oSQL.sql = "select \
                    sum(iiw.itemwhse_onhand_qty), \
                    sum(iiw.itemwhse_committed_qty), \
                    sum(iiw.itemwhse_unavailable_qty), \
                    sum(iiw.itemwhse_unusable_qty), \
                    sum(iiw.itemwhse_onhand_qty) - sum(iiw.itemwhse_committed_qty) - sum(iiw.itemwhse_unavailable_qty), \
                    sum(iiw.itemwhse_reserved_qty), \
                    sum(iiw.itemwhse_backord_qty), \
                    sum(iiw.itemwhse_onpo_qty), \
                    sum(iiw.itemwhse_onporeq_qty), \
                    sum(iiw.itemwhse_intransit_qty), \
                    sum(iiw.itemwhse_inproduction_qty) \
                    from in_item_warehouse iiw inner join in_warehouse iw on iiw.whse_id = iw.whse_id where iiw.org_id = ? and iiw.item_id = ? ";
		oSQL.args = [globals.org_id, uItemUUID.toString()];

		sPlantID = globals.avBase_plantIDTemp ? globals.avBase_plantIDTemp : globals.avBase_plantID;
		sDivID = globals.avBase_divIDTemp ? globals.avBase_divIDTemp : globals.avBase_divID;

		if (!globals["avBase_orgIsSingleDivision"]) {
			if (!sDivID || sDivID == i18n.getI18NMessage('avanti.lblAllCaps')) {

				oSQL.sql += " and iw.div_id IN (" + scopes.avInv.getEmpDivs() + ") ";

			}
			else {

				oSQL.sql += " and iw.div_id = ? ";
				oSQL.args.push(sDivID.toString());
			}
		}
		if (!globals["avBase_orgIsSinglePlant"]) {

			if (!sPlantID || sPlantID == i18n.getI18NMessage('avanti.lblAllCaps')) {

				oSQL.sql += " and iw.plant_id IN (" + scopes.avInv.getEmpPlants() + ") ";
			}
			else {

				oSQL.sql += " and iw.plant_id = ? ";
				oSQL.args.push(sPlantID.toString());
			}
		}
		dsData = globals["avUtilities_sqlDataset"](oSQL);

		if (dsData && dsData.getMaxRowIndex() > 0) {
			nQuantity = dsData.getValue(1, nQtyType);
		}
	}

	return nQuantity;
}

/**
 * @public
 *
 * Return whether or not a worktype with that worktype code exists
 *
 * @param {String} sWorkTypeCode
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"A2AFC1D6-2D3C-42FA-BF33-1CE8833FC3AA"}
 */
function doesActiveWorkTemplateWithThisWorkTypeCodeExist(sWorkTypeCode) {
	var sSQL = "SELECT worktype_id FROM sa_task_worktype WHERE org_id = ? AND worktype_code = ? AND worktype_is_active='1'";
	var aArgs = [globals.org_id, sWorkTypeCode.toString()];

	var sResult = scopes.avDB.SQLQuery(sSQL, null, aArgs);

	if (sResult) {
		return true;
	}
	else {
		return false;
	}
}

/**
 * @public
 *
 * Return whether or not an item with that item code exists
 *
 * @param {String} sItemCode
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"BC8379CD-EC49-489A-B5CA-ABB2C17CC75C"}
 */
function doesActiveItemWithThisItemCodeExist(sItemCode) {
	var sSQL = "SELECT item_id FROM in_item WHERE org_id = ? AND item_code = ? AND item_status='A'";
	var aArgs = [globals.org_id, sItemCode.toString()];

	var sResult = scopes.avDB.SQLQuery(sSQL, null, aArgs);

	if (sResult) {
		return true;
	}
	else {
		return false;
	}
}

/**
 * @public
 *
 * @param {UUID|String} sItemID
 * @param {UUID|String} sDivID
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"DFFBC55D-3FB0-437F-B222-E93A495086CC"}
 */
function doesItemUseThisDiv(sItemID, sDivID) {
	var sSQL = "SELECT COUNT(iw.itemwhse_id) \
                FROM in_item_warehouse iw \
                INNER JOIN in_warehouse w ON iw.whse_id = w.whse_id \
                WHERE iw.org_id = ? AND iw.item_id = ? AND w.div_id = ?";
	var aArgs = [globals.org_id, sItemID.toString(), sDivID.toString()];

	return scopes.avDB.SQLQuery(sSQL, null, aArgs) > 0;
}

/**
 * @public
 *
 * @param {UUID|String} sItemID
 * @param {UUID|String} sPlantID
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"97D4C191-543E-49B4-AB3C-3C873733A47D"}
 */
function doesItemUseThisPlant(sItemID, sPlantID) {
	var sSQL = "SELECT COUNT(iw.itemwhse_id) \
                FROM in_item_warehouse iw \
                INNER JOIN in_warehouse w ON iw.whse_id = w.whse_id \
                WHERE iw.org_id = ? AND iw.item_id = ? AND w.plant_id = ?";
	var aArgs = [globals.org_id, sItemID.toString(), sPlantID.toString()];

	return scopes.avDB.SQLQuery(sSQL, null, aArgs) > 0;
}

/**
 * Are all invoices for a order line posted
 *
 * <AUTHOR> Dol
 * @since May 10, 2018
 * @param {JSRecord<db:/avanti/sa_order_revision_detail>} rOrderDetail
 * @returns {Boolean}
 * @public
 *
 * @properties={typeid:24,uuid:"3B5871B6-69BA-418C-B553-6CC96E808988"}
 */
function isOrderLineInvoicesPosted(rOrderDetail) {

	if (!rOrderDetail || !utils.hasRecords(rOrderDetail.sa_order_revision_detail_to_sa_invoice_det)) {
		return false;
	}
	
	/** @type {JSRecord<db:/avanti/prod_job>} **/
	var rJob;
	
	var bAllPosted = true;
	var nInvoiceCount = 0;	
	
	if (utils.hasRecords(rOrderDetail.sa_order_revision_detail_to_prod_job)) {
		rJob = rOrderDetail.sa_order_revision_detail_to_prod_job.getRecord(1);
		bAllPosted = rJob.invoice_status == scopes.avUtils.JOB_INVOICE_STATUS.FullyInvoiced;
		
		for (var j = 1; j <= rOrderDetail.sa_order_revision_detail_to_sa_invoice_det.getSize(); j++) {
			var rInvoiceDetail = rOrderDetail.sa_order_revision_detail_to_sa_invoice_det.getRecord(j);

			if (utils.hasRecords(rInvoiceDetail.sa_invoice_det_to_sa_invoice)) {
				var rInvoice = rInvoiceDetail.sa_invoice_det_to_sa_invoice.getRecord(1);

				if (rInvoice.inv_type != scopes.avUtils.INVOICE_TYPE.AdvanceBilling) {
					nInvoiceCount++;
				}
			}
			else {
				bAllPosted = false;
				break;
			}
		}
	}


	

	//Added this to handle situations where the invoice is advance bill, should not be included in logic.
	if (bAllPosted && nInvoiceCount == 0) {
		bAllPosted = false;
	}

	return bAllPosted;
}

/**
 * @public
 *
 * @param {String|UUID} sItemID
 * @param {String|UUID} [sWarehouseID]
 * @param {String|UUID} [sBinID]
 * @param {String} [sProject]
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"2F143EFE-22BD-4CE4-8933-D0F84800082B"}
 */
function areThereTransactionsOnThisItem(sItemID, sWarehouseID, sBinID, sProject) {
	var sSQL = "SELECT COUNT(*) FROM in_item_trans_detail WHERE org_id = ? AND item_id = ?";
	var aArgs = [globals.org_id, sItemID.toString()];

	if (sWarehouseID) {
		sSQL += " AND whse_id = ? ";
		aArgs.push(sWarehouseID.toString());
	}

	if (sBinID) {
		sSQL += " AND itemtransd_whseloc_id = ?";
		aArgs.push(sBinID.toString());
	}
	
	if (sProject) {
		sSQL += " AND custproj_desc = ? ";
		aArgs.push(sProject);
	}

	return scopes.avDB.SQLQuery(sSQL, null, aArgs) > 0;
}

/**
 * @param {UUID|String} sItemID
 * @param {UUID|String} sItemWhseID
 * @param {UUID|String} [sItemWhseLocID]
 *
 * @return {UUID}
 *
 * @properties={typeid:24,uuid:"69E9607A-18CC-4C38-BCD0-FD96968DB863"}
 */
function getItemFifoRecID(sItemID, sItemWhseID, sItemWhseLocID) {
	var sSQL = "SELECT fifo_id \
                FROM in_item_fifo \
                WHERE org_id = ? AND item_id = ? AND itemwhse_id = ?";
	var aArgs = [globals.org_id, sItemID.toString(), sItemWhseID.toString()];

	if (sItemWhseLocID) {
		sSQL += " AND itemwhseloc_id = ?";
		aArgs.push(sItemWhseLocID.toString());
	}
	else {
		sSQL += " AND itemwhseloc_id IS NULL";
	}

	/**@type {UUID} */
	var uFifoID = scopes.avDB.SQLQuery(sSQL, null, aArgs);

	return uFifoID;
}

/**
 * @public
 *
 * @param {String} sReceiptType - 'PROD', 'PO', 'INV'
 * @param {UUID|String} sReceiptID
 * @param {UUID|String} [sEmpID]
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"814FC4A5-FD7B-4599-84A9-F22C17ABB4BC"}
 */
function autoGenerateInventoryLabel(sReceiptType, sReceiptID, sEmpID) {

    try {
        
        /** @type {JSFoundset<db:/avanti/in_label_export>} **/
        var fsInLabelExport = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_label_export');
        var aReceiptTypes = ['PROD', 'PO', 'INV'];
        var sCurEmpID = sEmpID ? sEmpID : globals.avBase_employeeUUID;
        var oParams = new Object();
        var sFolder;
        var sTransNum;
        var sFileName,
            sFileName_lbl2,
            sFileName_pallet,
            sFileName_roll_placard;
        var breturnFlag = false;

        fsInLabelExport.loadAllRecords();

        if (utils.hasRecords(fsInLabelExport) 
                && fsInLabelExport.export_ftp_host 
                && fsInLabelExport.export_ftp_username 
                && fsInLabelExport.export_ftp_password 
                && fsInLabelExport.export_ftp_folder 
                && sReceiptType && aReceiptTypes.indexOf(sReceiptType) > -1) {

            sFolder = fsInLabelExport.export_ftp_folder;
            if (sCurEmpID) {
                var sSubFolder = getInventoryLabelExportEmpSubFolder(sCurEmpID);

                if (sSubFolder) {
                    sFolder += "/" + sSubFolder;
                }
            }

            oParams.pReceiptType = sReceiptType;
            oParams.pReceipt_id = sReceiptID;

            if (sReceiptType == 'PO') {
                var dsProdReceipts = scopes.avPurchasing.getReceiptsFromProductionForPOReceipt(sReceiptID);

                // if there is a prod receipt for this po receipt then we print the prod rec instead of the po rec
                if (dsProdReceipts && dsProdReceipts.getMaxRowIndex() > 0) {
                    var bSuccess;

                    for (var pr = 1; pr <= dsProdReceipts.getMaxRowIndex(); pr++) {
                        var sProdReceiptID = dsProdReceipts.getValue(pr, 1);

                        bSuccess = autoGenerateInventoryLabel('PROD', sProdReceiptID) && bSuccess;
                    }

                    return bSuccess;
                }
                else {
                    sTransNum = scopes.avDB.SQLQuery("SELECT itemtransh_transaction_no FROM in_item_trans_header WHERE org_id = ? AND porec_id = ?",
                        null, [globals.org_id, sReceiptID.toString()]);
                    sFileName = "Box-Label-for-PO-Receipt-Transaction-No" + sTransNum + ".pdf";
                    sFileName_lbl2 = "Box-Label-Style2-for-PO-Receipt-Transaction-No" + sTransNum + ".pdf";
                    sFileName_pallet = "Pallet-Tag-for-Receipt-PO-Receipt-Transcation-No" + sTransNum + ".pdf";
                    sFileName_roll_placard = "Roll-Placard-for-Inventory_RollBinTransfer-Transcation-No" + sTransNum + ".pdf";
                }
            }
            else if (sReceiptType == 'PROD') {
                sTransNum = scopes.avDB.SQLQuery("SELECT prodrec_number FROM prod_receipt WHERE org_id = ? AND prodrec_id = ?",
                    null, [globals.org_id, sReceiptID.toString()]);
                sFileName = "Box-Label-for-Receipt-from-Production-Transaction-No" + sTransNum + ".pdf";
                sFileName_lbl2 = "Box-Label-Style2-for-Receipt-from-Production-Transaction-No" + sTransNum + ".pdf";
                sFileName_pallet = "Pallet-Tag-for-Receipt-from-Production-Transcation-No" + sTransNum + ".pdf";
            }
            else if (sReceiptType == 'INV') {
                sTransNum = scopes.avDB.SQLQuery("SELECT intraneh_transaction_no FROM in_trans_entry_header WHERE org_id = ? AND intraneh_id = ?",
                    null, [globals.org_id, sReceiptID.toString()]);
                sFileName = "Box-Label-for-Inventory-Receipt-Transaction-No" + sTransNum + ".pdf";
                sFileName_lbl2 = "Box-Label-Style2-for-Inventory-Receipt-Transaction-No" + sTransNum + ".pdf";
                sFileName_pallet = "Pallet-Tag-for-Inventory_Receipt-Transcation-No" + sTransNum + ".pdf";
                sFileName_roll_placard = "Roll-Placard-for-Inventory_RollBinTransfer-Transcation-No" + sTransNum + ".pdf";

            }

            if (fsInLabelExport.export_ftp_labelstyle1_report == 1 || ( fsInLabelExport.export_ftp_labelstyle1_report == 0 
                    && fsInLabelExport.export_ftp_labelstyle2_report == 0 && fsInLabelExport.export_ftp_pallettag_report == 0 )) {

                var pdfByteArray_lbl1 = scopes.avReporting.runReportRemote(null, [sReceiptID],
                    globals.org_id, 'Inventory Label Style 1', 'pdf', 'en', oParams, 'receipt_id');

                if (pdfByteArray_lbl1 && pdfByteArray_lbl1.length > 0) {
                    var pdfFile = plugins.file.createFile(scopes.avReporting.serverGetPath("/server/webapps/ROOT/uploads/" + globals.org_id + "_downloads/" + sFileName));

                    if (plugins.file.writeFile(pdfFile, pdfByteArray_lbl1)) {
                        if (scopes.avUtils.uploadFileDirectlyToFTP(pdfFile.getAbsolutePath(),
                            fsInLabelExport.export_ftp_host, fsInLabelExport.export_ftp_port,
                            fsInLabelExport.export_ftp_username, fsInLabelExport.export_ftp_password,
                            fsInLabelExport.export_ftp_hostkey, sFolder,
                            fsInLabelExport.export_ftp_use_sftp, application.getUUID(globals.org_id))) {

                            breturnFlag = true;
                        }
                    }
                }
            }

            if (fsInLabelExport.export_ftp_labelstyle2_report == 1) {

                var pdfByteArray_lbl2 = scopes.avReporting.runReportRemote(null, [sReceiptID],
                    globals.org_id, 'Inventory Label Style 2', 'pdf', 'en', oParams, 'receipt_id');

                if (pdfByteArray_lbl2 && pdfByteArray_lbl2.length > 0) {
                    var pdfFile_lbl2 = plugins.file.createFile(scopes.avReporting.serverGetPath("/server/webapps/ROOT/uploads/" + globals.org_id + "_downloads/" + sFileName_lbl2));

                    if (plugins.file.writeFile(pdfFile_lbl2, pdfByteArray_lbl2)) {
                        if (scopes.avUtils.uploadFileDirectlyToFTP(pdfFile_lbl2.getAbsolutePath(),
                            fsInLabelExport.export_ftp_host, fsInLabelExport.export_ftp_port,
                            fsInLabelExport.export_ftp_username, fsInLabelExport.export_ftp_password,
                            fsInLabelExport.export_ftp_hostkey, sFolder,
                            fsInLabelExport.export_ftp_use_sftp, application.getUUID(globals.org_id))) {

                            breturnFlag = true;
                        }
                    }
                }
            }

            if (fsInLabelExport.export_ftp_pallettag_report == 1) {

                var pdfByteArray_pallet = scopes.avReporting.runReportRemote(null, [sReceiptID],
                    globals.org_id, 'Pallet Tag', 'pdf', 'en', oParams, 'receipt_id');

                if (pdfByteArray_pallet && pdfByteArray_pallet.length > 0) {
                    var pdfFile_pallet = plugins.file.createFile(scopes.avReporting.serverGetPath("/server/webapps/ROOT/uploads/" + globals.org_id + "_downloads/" + sFileName_pallet));

                    if (plugins.file.writeFile(pdfFile_pallet, pdfByteArray_pallet)) {
                        if (scopes.avUtils.uploadFileDirectlyToFTP(pdfFile_pallet.getAbsolutePath(),
                            fsInLabelExport.export_ftp_host, fsInLabelExport.export_ftp_port,
                            fsInLabelExport.export_ftp_username, fsInLabelExport.export_ftp_password,
                            fsInLabelExport.export_ftp_hostkey, sFolder,
                            fsInLabelExport.export_ftp_use_sftp, application.getUUID(globals.org_id))) {

                            breturnFlag = true;
                        }
                    }
                }
            }

            if (fsInLabelExport.export_ftp_roll_placard_report == 1) {

                var pdfByteArray_rollPlacard = scopes.avReporting.runReportRemote(null, [sReceiptID],
                    globals.org_id, 'Roll Placard', 'pdf', 'en', oParams, 'intraneh_or_porec_id');

                if (pdfByteArray_rollPlacard && pdfByteArray_rollPlacard.length > 0) {
                    var pdfFile_rollPlacard = plugins.file.createFile(scopes.avReporting.serverGetPath("/server/webapps/ROOT/uploads/" + globals.org_id + "_downloads/" + sFileName_roll_placard));

                    if (plugins.file.writeFile(pdfFile_rollPlacard, pdfByteArray_rollPlacard)) {
                        if (scopes.avUtils.uploadFileDirectlyToFTP(pdfFile_rollPlacard.getAbsolutePath(),
                            fsInLabelExport.export_ftp_host, fsInLabelExport.export_ftp_port,
                            fsInLabelExport.export_ftp_username, fsInLabelExport.export_ftp_password,
                            fsInLabelExport.export_ftp_hostkey, sFolder,
                            fsInLabelExport.export_ftp_use_sftp, application.getUUID(globals.org_id))) {

                            breturnFlag = true;
                        }
                    }
                }
            }

            if (breturnFlag == true) {

                return true;
            }
        }
    }
    catch (ex) {
        var sMessage = i18n.getI18NMessage("avanti.dialog.inventoryLabelExportFailed_msg") + " " + ex.message;
        scopes.avUtils.quickLog("autoGenerateInventoryLabel", sMessage, ex.stack);
        scopes.avText.showInfo(sMessage, true);
    }

    return false;
}


/**
 * @param {UUID|String} [sEmpID]
 *
 * @return
 * @properties={typeid:24,uuid:"01F01A21-5E25-4A96-80E0-502F152423C0"}
 */
function getInventoryLabelExportEmpSubFolder(sEmpID) {
	var sSQL = "SELECT F.folder \
                FROM in_label_export_folder F \
                INNER JOIN in_label_export_folder_emp E ON F.in_label_export_folder_id = E.in_label_export_folder_id \
                WHERE E.empl_id = ?";
	return scopes.avDB.SQLQuery(sSQL, null, [sEmpID.toString()]);
}

/**
 * @param {UUID|String} sWarehouseID
 * @param {String} sReference
 * @param {UUID} sItemID
 * @param {Number} nQty
 * @param {String|UUID} [sBinLocID]
 * @param {Number} [nQtyPerBox]
 * @param {Number} [nNumBoxes]
 * @param {Number} [nCostPerUnit]
 * @param {String|UUID} [sGLAcctID]
 * @param {String} [sProject]
 *
 *
 * @properties={typeid:24,uuid:"010BAABA-B4DE-4B86-9D92-718216B4A76B"}
 */
function createInventoryReceiptTransaction(sWarehouseID, sReference, sItemID, nQty, sBinLocID, nQtyPerBox, nNumBoxes, nCostPerUnit, sGLAcctID, sProject) {

	if (sWarehouseID && sReference && sItemID && nQty) {
		/**@type {JSRecord<db:/avanti/in_trans_entry_header>} */
		var rTransHeader = scopes.avDB.newRecord('in_trans_entry_header');
		var sTransTypeSQL = "SELECT intranstype_id \
                             FROM in_transaction_type \
                             WHERE org_id = ? AND intranstype_trans_code = 'IR'";
		var sItemWhseLocSQL = "SELECT itemwhseloc_id \
                               FROM in_item_warehouse_location \
                               WHERE org_id = ? AND item_id = ? AND whseloc_id = ? "

		rTransHeader.created_by_id = globals.avBase_employeeUUID;
		rTransHeader.created_date = application.getTimeStamp();
		rTransHeader.intraneh_transaction_no = globals.getNextItemTransactionNumber('IT');
		rTransHeader.intraneh_whse_id = sWarehouseID;
		rTransHeader.intraneh_reference = sReference;
		rTransHeader.intranstype_id = scopes.avDB.SQLQuery(sTransTypeSQL, null, [globals.org_id]);
		// init to Open - it will be changed when transaction is updated at bottom of function
		rTransHeader.intraneh_status = 0;
		rTransHeader.intraneh_status_field = 'O';

		var rTransDetail = rTransHeader.in_trans_entry_header_to_in_trans_entry_detail.getRecord(rTransHeader.in_trans_entry_header_to_in_trans_entry_detail.newRecord());

		rTransDetail.sequence_nr = 1;
		rTransDetail.intraned_whse_id = sWarehouseID;
		rTransDetail.item_id = sItemID;
		rTransDetail.intraned_trans_qty = nQty;
		rTransDetail.intraned_cost_uom_id = rTransDetail.in_trans_entry_detail_to_in_item.item_standard_uom_id;
		rTransDetail.uom_id = rTransDetail.in_trans_entry_detail_to_in_item.item_standard_uom_id;
		rTransDetail.itemwhse_id = rTransDetail.in_trans_entry_detail_to_in_item_warehouse.itemwhse_id;
		rTransDetail.custproj_desc = sProject;

		if (sBinLocID) {
			rTransDetail.intraned_whseloc_id = sBinLocID;
			rTransDetail.intraned_itemwhseloc_id = scopes.avDB.SQLQuery(sItemWhseLocSQL, null,
				[globals.org_id, sItemID.toString(), sBinLocID.toString()]);
		}

		if (nQtyPerBox) {
			rTransDetail.intraned_qty_per_box = nQtyPerBox;
		}

		if (nNumBoxes) {
			rTransDetail.intraned_num_boxes = nNumBoxes;
		}

		if (nCostPerUnit) {
			rTransDetail.intraned_cost_amt = nCostPerUnit;
		}
		else {
			rTransDetail.intraned_cost_amt = globals.getItemCost(sItemID, sWarehouseID);
		}

		if (sGLAcctID) {
			rTransDetail.glacct_id = sGLAcctID;
		}
		else {
			if (scopes.globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.AllowDefaultOffsetAccount) == 1) {
				sGLAcctID = scopes.avInv.getDefaultOffsetGLA(rTransHeader, rTransDetail.item_id);
			}

			// Set the GL account id based on the item or item class
			if (!sGLAcctID && utils.hasRecords(rTransDetail.in_trans_entry_detail_to_in_item)) {
				if (rTransDetail.in_trans_entry_detail_to_in_item.item_glacct_id_inventory_adj != null) {
					sGLAcctID = rTransDetail.in_trans_entry_detail_to_in_item.item_glacct_id_inventory_adj;
				}
				else {
					if (utils.hasRecords(rTransDetail.in_trans_entry_detail_to_in_item.in_item_to_in_item_class)) {
						if (rTransDetail.in_trans_entry_detail_to_in_item.in_item_to_in_item_class.itemclass_gl_inventory_adj) {
							sGLAcctID = rTransDetail.in_trans_entry_detail_to_in_item.in_item_to_in_item_class.itemclass_gl_inventory_adj;
						}
					}
				}
			}

			if (!sGLAcctID && utils.hasRecords(rTransDetail.in_trans_entry_detail_to_in_warehouse)) {
				var rWarehouse = rTransDetail.in_trans_entry_detail_to_in_warehouse.getRecord(1);
				sGLAcctID = scopes.avInv.getInventoryAdjustmentControlAccount(rWarehouse);
			}

			rTransDetail.glacct_id = sGLAcctID;
		}

		rTransDetail.intraned_extended_cost = rTransDetail.intraned_cost_amt * nQty;

		databaseManager.saveData(rTransHeader);
		databaseManager.saveData(rTransDetail);

		// Update the transaction
		buildTempTransactionTables(rTransHeader, null, true);
	}
}

/**
 * Handles fifo quantity adjustments when a PO receipt is cancelled
 * @param {JSRecord<db:/avanti/in_item_trans_detail>} rTransDetail
 * @param {UUID} [uAdjustFifoID]
 *
 * @properties={typeid:24,uuid:"5B8AA5B7-1B43-4E3C-AB58-E5110C1BC246"}
 */
function adjustFifoForCancelledStockReceipt(rTransDetail, uAdjustFifoID) {
	var rFifo = null;

	// if we need to adjust a fifo that different from the 1 attached to the po receipt, IE the inventory was moved somewhere else
	if (uAdjustFifoID) {
		rFifo = scopes.avDB.getRec("in_item_fifo", ["fifo_id"], [uAdjustFifoID]);
	}
	else {
		/** @type {JSFoundSet<db:/avanti/in_item_fifo>} */
		var fsFifo = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_fifo');
		var oSQL = new Object();

		oSQL.sql = "SELECT itf.fifo_id\
	                FROM in_item_fifo AS itf\
	                INNER JOIN in_item_trans_detail AS itd ON itd.itemtransd_id = itf.itemtransd_id\
	                WHERE itf.org_id = ? AND itd.porecd_id = ?";

		oSQL.args = [globals.org_id, rTransDetail.porecd_id.toString()];

		fsFifo.clear();
		fsFifo.loadRecords(oSQL.sql, oSQL.args);
		
		//Only expect 1 record from the query
		rFifo = utils.hasRecords(fsFifo) ? fsFifo.getRecord(1) : null;
	}
	
	if (rFifo) {
		var sFifo_id = rFifo.fifo_id;
		var nDetQty = rTransDetail.itemtransd_qty;
		var nDiff = rFifo.fifo_qty_remaining + nDetQty;
		var iDecimals = 0;
		
		if (utils.hasRecords(rTransDetail.in_item_trans_detail_to_in_item)) {
			iDecimals = (rTransDetail.in_item_trans_detail_to_in_item.item_decimal_places == null ? 0 : rTransDetail.in_item_trans_detail_to_in_item.item_decimal_places);
		}
		
		if (nDiff >= 0) {
			rFifo.fifo_qty_remaining = nDiff;
			createFifoDetRecord(sFifo_id, rTransDetail.itemtransd_id, nDetQty, 0);
			rTransDetail.itemtransd_fifo_balance += globals["avUtilities_roundNumber"](nDetQty * -1, iDecimals);
		}
		else if (rFifo.fifo_qty_remaining > 0) {
			createFifoDetRecord(sFifo_id, rTransDetail.itemtransd_id, rFifo.fifo_qty_remaining, 0);
			rTransDetail.itemtransd_fifo_balance += globals["avUtilities_roundNumber"](rFifo.fifo_qty_remaining, iDecimals);
			rFifo.fifo_qty_remaining = 0;
		}
	}
}

/**
 * Create a new FIFO record
 *
 * @param {JSRecord<db:/avanti/in_item_trans_detail>} rTransDetail
 * @param {UUID} rollID
 * @param {Number} nFifoRemainingQty
 * @param {Number} nShipped
 * @param {Date} dFifoDate
 * @param {String} [sProject]
 * @return {JSRecord<db:/avanti/in_item_fifo>}
 *
 * @AllowToRunInFind
 *
 *
 * @properties={typeid:24,uuid:"81A5CBE9-5770-4A81-85C3-88B6197688FB"}
 */
function createFifoRecord(rTransDetail, rollID, nFifoRemainingQty, nShipped, dFifoDate, sProject) {
	if (rTransDetail == null) {
		return null;
	}
	
	if (sProject && !scopes.avInv.isProjectInventoryOn()) {
		sProject = null;
	}

	if (utils.hasRecords(rTransDetail.in_item_trans_detail_to_in_item) 
			&& rTransDetail.in_item_trans_detail_to_in_item.itemtype_code != scopes.avUtils.ITEM_TYPE.OutsourcedService) {
		var rItem = rTransDetail.in_item_trans_detail_to_in_item.getRecord(1);
		var iDecimals = (rItem.item_decimal_places ? rItem.item_decimal_places : 0);
		nFifoRemainingQty = globals["avUtilities_roundNumber"](nFifoRemainingQty, iDecimals);
		nShipped = nShipped;
		
		/** @type {JSFoundSet<db:/avanti/in_item_fifo>} */
		var fsItemFifo = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_fifo');
		
		//Before we create a new fifo record we need to make any existing record with negative rem qty 0
		if (nFifoRemainingQty == null) {
			nFifoRemainingQty = adjustNegativeFifoRecords(rTransDetail, nShipped);
			if (nFifoRemainingQty <= 0) { 
				return null;
			}
		}


		fsItemFifo.newRecord();
		fsItemFifo.itemtransd_id = rTransDetail.itemtransd_id;
		fsItemFifo.item_id = rTransDetail.item_id;
		fsItemFifo.itemwhse_id = rTransDetail.in_item_trans_detail_to_in_item_warehouse$to_warehouse.itemwhse_id;
		fsItemFifo.itemwhseloc_id = rTransDetail.itemwhseloc_id;
		
		if (utils.hasRecords(rTransDetail.in_item_trans_detail_to_in_item_warehouse_location) 
				&& rTransDetail.in_item_trans_detail_to_in_item_warehouse_location.item_id != rTransDetail.item_id) {
			scopes.avUtils.devLog('SL-17807', "item id on transaction:" + rTransDetail.item_id + ", item id on itemwhseloc " + rTransDetail.in_item_trans_detail_to_in_item_warehouse_location.item_id, true);
		}

		if (rollID) {
			fsItemFifo.initemroll_id = rollID;
		}
		else if (rTransDetail.initemroll_id) {
			fsItemFifo.initemroll_id = rTransDetail.initemroll_id;
		}

		fsItemFifo.fifo_qty_remaining = nFifoRemainingQty;
		fsItemFifo.fifo_qty_reserved = 0;
		fsItemFifo.fifo_qty_reserved_project = 0;
		fsItemFifo.fifo_cost = rTransDetail.itemtransd_cost;
		fsItemFifo.fifo_qty_original = fsItemFifo.fifo_qty_remaining;
		fsItemFifo.custproj_desc = sProject;

		//Set the default fifo date, It's not necessarily always the transaction date.
		if (utils.hasRecords(rTransDetail.in_item_trans_detail_to_in_item_trans_header)) {
			if (dFifoDate 
					&& utils.hasRecords(rTransDetail.in_item_trans_detail_to_in_item_trans_header.in_item_trans_header_to_in_transaction_type) 
					&& rTransDetail.in_item_trans_detail_to_in_item_trans_header.in_item_trans_header_to_in_transaction_type.intranstype_trans_code == TRANSACTION_TYPE.BinTransfer
					|| rTransDetail.in_item_trans_detail_to_in_item_trans_header.in_item_trans_header_to_in_transaction_type.intranstype_trans_code == TRANSACTION_TYPE.RollBinTransfer) {
				fsItemFifo.fifo_date = dFifoDate;
			}
			else {
				fsItemFifo.fifo_date = rTransDetail.in_item_trans_detail_to_in_item_trans_header.itemtransh_date;
			}

			if (utils.hasRecords(rTransDetail.in_item_trans_detail_to_in_item_trans_header.in_item_trans_header_to_in_transaction_type)) {
				var transType = rTransDetail.in_item_trans_detail_to_in_item_trans_header.in_item_trans_header_to_in_transaction_type.intranstype_trans_code;
				var rSaOrderRevd = null;

				if (transType == TRANSACTION_TYPE.StockReceipt && utils.hasRecords(rTransDetail.in_item_trans_detail_to_po_receipt_detail) && utils.hasRecords(rTransDetail.in_item_trans_detail_to_po_receipt_detail.po_receipt_detail_to_po_receipt_detail_qty) && utils.hasRecords(rTransDetail.in_item_trans_detail_to_po_receipt_detail.po_receipt_detail_to_po_receipt_detail_qty.po_receipt_detail_qty_to_po_purchase_detail_qty) && utils.hasRecords(rTransDetail.in_item_trans_detail_to_po_receipt_detail.po_receipt_detail_to_po_receipt_detail_qty.po_receipt_detail_qty_to_po_purchase_detail_qty.po_purchase_detail_qty_to_sa_order_revision_detail)) {
					rSaOrderRevd = rTransDetail.in_item_trans_detail_to_po_receipt_detail.po_receipt_detail_to_po_receipt_detail_qty.po_receipt_detail_qty_to_po_purchase_detail_qty.po_purchase_detail_qty_to_sa_order_revision_detail.getSelectedRecord();
				}

				if (transType == TRANSACTION_TYPE.ReceiptFromProduction && utils.hasRecords(rTransDetail.in_item_trans_detail_to_prod_receipt_detail) && utils.hasRecords(rTransDetail.in_item_trans_detail_to_prod_receipt_detail.prod_receipt_detail_to_sa_order_revision_detail)) {
					rSaOrderRevd = rTransDetail.in_item_trans_detail_to_prod_receipt_detail.prod_receipt_detail_to_sa_order_revision_detail.getSelectedRecord();
				}

				if (rSaOrderRevd && utils.hasRecords(rSaOrderRevd.sa_order_revision_detail_to_sa_order_revision_header) && utils.hasRecords(rSaOrderRevd.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order) && utils.hasRecords(rSaOrderRevd.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order.sa_order_to_sa_customer_project)) {

					fsItemFifo.custproj_desc = rSaOrderRevd.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order.sa_order_to_sa_customer_project.custproj_desc;
				}
			}
		}

		return fsItemFifo.getSelectedRecord();
	}

	return null;
}

/**
 * Handles fifo quantity adjustments when a Shipment is cancelled
 * @param {JSRecord<db:/avanti/in_item_trans_detail>} rTransDetail
 * @param {Number} iDecimals
 *
 *
 * @properties={typeid:24,uuid:"*************-4CBF-81DF-DA6510A8A4CA"}
 */
function adjustFifoForShipCancellation(rTransDetail, iDecimals) {
	if (utils.hasRecords(rTransDetail.in_item_trans_detail_to_in_item) && rTransDetail.in_item_trans_detail_to_in_item.item_is_virtual) {
		return;
	}
	
	/** @type {JSFoundSet<db:/avanti/in_item_fifo_det>} */
	var fsFifoDet = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_fifo_det');

	var oSQL = new Object();

	var itemwhseloc_id = rTransDetail.itemwhseloc_id ? rTransDetail.itemwhseloc_id.toString() : null;

	oSQL.sql = "SELECT itfd.fifod_id  \
                FROM in_item_fifo_det AS itfd\
                INNER JOIN in_item_trans_detail AS itd ON itd.itemtransd_id = itfd.itemtransd_id \
                WHERE itfd.org_id = ? \
                AND itd.packd_id = ? AND itfd.shipped = 1 AND itd.itemwhseloc_id = ?";

	oSQL.args = [globals.org_id, rTransDetail.packd_id.toString(), itemwhseloc_id];

	fsFifoDet.clear();
	fsFifoDet.loadRecords(oSQL.sql, oSQL.args);

	if (fsFifoDet && fsFifoDet.getSize() > 0) {
		var nAppliedBalance = rTransDetail.itemtransd_qty;
		var fdMax = databaseManager.getFoundSetCount(fsFifoDet);
		for (var i = 1; i <= fdMax; i++) {
			var rFifoDet = fsFifoDet.getRecord(i);
			if (!rFifoDet) {
				continue;
			}
			rFifoDet.shipped = 0;
			if (utils.hasRecords(rFifoDet.in_item_fifo_det_to_in_item_fifo)) {
				fsFifoDet.newRecord(false, true);
				fsFifoDet.fifo_id = rFifoDet.fifo_id;
				fsFifoDet.itemtransd_id = rTransDetail.itemtransd_id;
				fsFifoDet.fifod_qty = rFifoDet.fifod_qty * -1;
				nAppliedBalance -= fsFifoDet.fifod_qty;
				fsFifoDet.shipped = 0;
				rFifoDet.in_item_fifo_det_to_in_item_fifo.fifo_qty_remaining += fsFifoDet.fifod_qty;

				databaseManager.saveData(rFifoDet.in_item_fifo_det_to_in_item_fifo);
				databaseManager.saveData(fsFifoDet);

				rTransDetail.itemtransd_fifo_balance += globals["avUtilities_roundNumber"](rFifoDet.fifod_qty, iDecimals);
			}
			databaseManager.saveData(rFifoDet);
			
			// Create New FIFO for the unapplied cancel shipment balance
            if (nAppliedBalance > 0) {
            	scopes.avInv.createFifoRecord(rTransDetail, null, nAppliedBalance, null, null, rTransDetail.custproj_desc);
            	rTransDetail.itemtransd_fifo_balance += globals["avUtilities_roundNumber"](nAppliedBalance * -1, iDecimals);
                databaseManager.saveData();
            }
		}
	}
	else {
        //Create new FIFO if cannot locate shipment
        scopes.avInv.createFifoRecord(rTransDetail, null, null, null, null, rTransDetail.custproj_desc);
        rTransDetail.itemtransd_fifo_balance += globals["avUtilities_roundNumber"](rTransDetail.itemtransd_qty * -1, iDecimals);
        databaseManager.saveData();
    }
}


/**
 * Handles fifo quantity adjustments when a Shipment is cancelled
 * @param {JSRecord<db:/avanti/in_item_trans_detail>} rTransDetail
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"9FBE4B19-F65E-41B9-8CD7-A4820C37F7FC"}
 */
function adjustFifoForTimeSheetAdjustment(rTransDetail) {
	if (utils.hasRecords(rTransDetail.in_item_trans_detail_to_prod_job_cost_material) 
			&& utils.hasRecords(rTransDetail.in_item_trans_detail_to_prod_job_cost_material.prod_job_cost_material_to_in_item_trans_detail$jcm_adjustment_jcm_id) 
			&& utils.hasRecords(rTransDetail.in_item_trans_detail_to_prod_job_cost_material.prod_job_cost_material_to_in_item_trans_detail$jcm_adjustment_jcm_id.in_item_trans_detail_to_in_item_fifo_det)) {

		var iDecimals = 0;
		if (utils.hasRecords(rTransDetail.in_item_trans_detail_to_in_item)) {
			iDecimals = rTransDetail.in_item_trans_detail_to_in_item.item_decimal_places;
		}
		
		/** @type {JSFoundSet<db:/avanti/in_item_fifo_det>} */
		var fsFifoDet = rTransDetail.in_item_trans_detail_to_prod_job_cost_material.prod_job_cost_material_to_in_item_trans_detail$jcm_adjustment_jcm_id.in_item_trans_detail_to_in_item_fifo_det;
		fsFifoDet.sort("fifod_qty desc");
		var nRemainingQty = globals["avUtilities_roundNumber"](rTransDetail.itemtransd_qty, iDecimals);
		var fdMax = databaseManager.getFoundSetCount(fsFifoDet);
		for (var i = 1; i <= fdMax; i++) {
			var rFifoDet = fsFifoDet.getRecord(i);
			if (nRemainingQty <= 0) {
				break;
			}
			if (!rFifoDet) {
				continue;
			}
			rFifoDet.shipped = 0;
			if (utils.hasRecords(rFifoDet.in_item_fifo_det_to_in_item_fifo)) {
				fsFifoDet.newRecord(false, true);
				fsFifoDet.fifo_id = rFifoDet.fifo_id;
				fsFifoDet.itemtransd_id = rTransDetail.itemtransd_id;
				var nAbsValue = Math.abs(globals["avUtilities_roundNumber"](rFifoDet.fifod_qty, iDecimals));
				fsFifoDet.fifod_qty = nAbsValue > nRemainingQty ? nRemainingQty : nAbsValue;
				nRemainingQty -= fsFifoDet.fifod_qty;

				fsFifoDet.shipped = 0;
				rFifoDet.in_item_fifo_det_to_in_item_fifo.fifo_qty_remaining += fsFifoDet.fifod_qty;
				rTransDetail.itemtransd_fifo_balance += globals["avUtilities_roundNumber"](fsFifoDet.fifod_qty * -1, iDecimals);

				/** @type {JSFoundSet<db:/avanti/in_item_fifo_reserved>} */
				var fsFifoReserved = rFifoDet.in_item_fifo_det_to_in_item_fifo.in_item_fifo_to_in_item_fifo_reserved;

				var fsMat = rTransDetail.in_item_trans_detail_to_prod_job_cost_material;
				if (fsFifoReserved && utils.hasRecords(fsMat.prod_job_cost_material_to_sa_order_revision_detail) 
						&& utils.hasRecords(fsMat.prod_job_cost_material_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_pick_detail)) {

					var sPickdId = fsMat.prod_job_cost_material_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_pick_detail.pickd_id;
					for (var j = 1; j <= fsFifoReserved.getSize(); j++) {
						var rFifoReserved = fsFifoReserved.getRecord(j);
						if (rFifoReserved.fifor_source_pk == sPickdId) {
							rFifoReserved.fifor_qty += fsFifoDet.fifod_qty;
							//keeping the change on the same in_item_fifo foundset object to leverage the save call
							rFifoDet.in_item_fifo_det_to_in_item_fifo.fifo_qty_reserved += fsFifoDet.fifod_qty;
							databaseManager.saveData(rFifoReserved);
							break;
						}
					}
				}
				databaseManager.saveData(rFifoDet.in_item_fifo_det_to_in_item_fifo);
				databaseManager.saveData(fsFifoDet);
			}
			databaseManager.saveData(rFifoDet);
		}
		return true;
	}
	return false;
}

/**
 * Brings existing negative fifo records for the location upto 0 and returns the remaining qty after the adjustment
 * @param {JSRecord<db:/avanti/in_item_trans_detail>} rTransDetail
 * @param {Number} nShipped
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"CF7099F8-5869-4DF0-A32C-F0D3B2D822DA"}
 */
function adjustNegativeFifoRecords(rTransDetail, nShipped) {
	var iDecimals = 0;
	if (utils.hasRecords(rTransDetail.in_item_trans_detail_to_in_item)){
		var rItem = rTransDetail.in_item_trans_detail_to_in_item.getRecord(1);
		iDecimals = (rItem.item_decimal_places ? rItem.item_decimal_places : 0);
	}
	
	var nFifoRemainingQty = globals["avUtilities_roundNumber"](rTransDetail.itemtransd_qty, iDecimals);

	if (nFifoRemainingQty <= 0) {
		return nFifoRemainingQty;
	}

	globals.avBase_SelectedItemWhseBinLocationUUID = null;
	globals.avBase_SelectedRollUUID = null;

	if (rTransDetail.itemwhseloc_id) {
		globals.avBase_SelectedItemWhseBinLocationUUID = rTransDetail.itemwhseloc_id;
	}

	if (rTransDetail.initemroll_id) {
		globals.avBase_SelectedRollUUID = rTransDetail.initemroll_id;
	}

	var fsFifo = rTransDetail.in_item_trans_detail_to_in_item_warehouse$to_warehouse.in_item_warehouse_to_in_item_fifo$warehouse_bin_roll_not_zero;

	if (!fsFifo) {
		return nFifoRemainingQty;
	}

	var fdMax = databaseManager.getFoundSetCount(fsFifo);

	for (var i = 1; i <= fdMax; i++) {
		if (nFifoRemainingQty <= 0) {
			break;
		}

		var rFifo = fsFifo.getRecord(i);
		if (rFifo && rFifo.fifo_qty_remaining < 0 && rFifo.custproj_desc == rTransDetail.custproj_desc) {
			nFifoRemainingQty += rFifo.fifo_qty_remaining;
			scopes.avInv.createFifoDetRecord(rFifo.fifo_id, rTransDetail.itemtransd_id, rFifo.fifo_qty_remaining * -1, nShipped);
			rTransDetail.itemtransd_fifo_balance += globals["avUtilities_roundNumber"](rFifo.fifo_qty_remaining, iDecimals);
			
			if (nFifoRemainingQty < 0) {
				rFifo.fifo_qty_remaining = nFifoRemainingQty;
			}
			else {
				rFifo.fifo_qty_remaining = 0;
			}
			databaseManager.saveData(rFifo);
		}
	}
	return nFifoRemainingQty;
}

/**
 * Finds all the fifo records for the item that the transaction is for, and
 * then tries to reduce the records based on the avaialble qty and transaction qty
 * @AllowToRunInFind
 *
 * @param {JSRecord<db:/avanti/in_item_trans_detail>} rTransDetail
 * @param {String} sTransCode
 * @param {JSRecord<db:/avanti/in_item_trans_detail>} rBinTransferOutTransDet in case of Bin Transfers the rTransdetail
 * @param {String} [source_param]
 * represents the Bin Transfer In transaction and rBinTransferOutTransDet is for Bin Transfer Out Transaction
 *
 * @properties={typeid:24,uuid:"1A9BF241-1579-4391-811E-403FDB3A867B"}
 */
function reduceItemFifo(rTransDetail, sTransCode, rBinTransferOutTransDet, source_param) {
	if (rTransDetail == null || !utils.hasRecords(rTransDetail.in_item_trans_detail_to_in_item)) {
		return;
	}

	var rItem = rTransDetail.in_item_trans_detail_to_in_item.getRecord(1);
	var iDecimals = (rItem.item_decimal_places ? rItem.item_decimal_places: 0);
	var nTransQuantity = Math.abs(rTransDetail.itemtransd_qty);
	var bProjectInventory = scopes.avInv.isProjectInventoryOn();

	/** @type {JSFoundSet<db:/avanti/in_item_fifo>} */
	var fsFifo = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_fifo');
	if (fsFifo.find()) {
		fsFifo.item_id = rTransDetail.item_id;
		fsFifo.itemwhse_id = rTransDetail.in_item_trans_detail_to_in_item_warehouse$to_warehouse.itemwhse_id;
		fsFifo.fifo_qty_remaining = ">0";

		if (sTransCode == TRANSACTION_TYPE.BinTransfer || sTransCode == TRANSACTION_TYPE.WhseTransferOut || sTransCode == TRANSACTION_TYPE.RollBinTransfer) {
			fsFifo.itemwhseloc_id = rTransDetail.itemtransd_from_itemwhseloc_id;
		}
		else if (rTransDetail.itemwhseloc_id) {
			fsFifo.itemwhseloc_id = rTransDetail.itemwhseloc_id;
		}

		if (bProjectInventory && rTransDetail.custproj_desc) {
			fsFifo.custproj_desc = rTransDetail.custproj_desc;
		}

		if (rTransDetail.initemroll_id) {
			fsFifo.initemroll_id = rTransDetail.initemroll_id;
		}
		//The following transactions by default do not reserve item qty fifo. However, exceptions will have to be handled
		var bIgnoreFifoReserveQty = sTransCode == TRANSACTION_TYPE.ShopFloorMaterialIssue 
												  || sTransCode == TRANSACTION_TYPE.TimeSheetMaterialAdjustment 
												  || sTransCode == TRANSACTION_TYPE.CountAdjustment 
												  || sTransCode == TRANSACTION_TYPE.IntegrationMaterialIssue 
												  || sTransCode == TRANSACTION_TYPE.ReceiptFromProduction 
												  || (sTransCode == TRANSACTION_TYPE.InventoryIssue && source_param == TRANSACTION_SOURCE.AutoIssueRawMaterials) 
												  || scopes.globals.bProcessingMobileAPI;

		//If Bin Locations are not enabled we will reduce without reservation
		if ( (utils.hasRecords(rTransDetail.in_item_trans_detail_to_in_item_warehouse.in_item_warehouse_to_in_warehouse) 
				&& rTransDetail.in_item_trans_detail_to_in_item_warehouse.in_item_warehouse_to_in_warehouse.whse_enable_bin_locations != 1) 
				|| (utils.hasRecords(rTransDetail.in_item_trans_detail_to_in_item) && rTransDetail.in_item_trans_detail_to_in_item.item_no_bin_location == 1)) {

			bIgnoreFifoReserveQty = true;
		}

		/** @type {JSRecord<db:/avanti/in_item_fifo>} */
		var rFifo;
		var nShipped = sTransCode == TRANSACTION_TYPE.Shipment || sTransCode == TRANSACTION_TYPE.FulfillmentPick ? 1 : 0;

		//Since we do not have a separate kind of transaction type for bin transfer in, we have to use a lot of if else using this boolean
		//in future if we decide to have a spearate transaction, we need to review all the logic that is done using this boolean.
		/** @type {Boolean} */
		var bReduceForBTO = (sTransCode == TRANSACTION_TYPE.RollBinTransfer || sTransCode == TRANSACTION_TYPE.BinTransfer) && rBinTransferOutTransDet != null;

		if (fsFifo.search()) {
			var nMaxFifo = databaseManager.getFoundSetCount(fsFifo);
			fsFifo.sort("fifo_date asc, fifo_qty_remaining asc");
			
			//Prod job cost material entry tied to a transaction can have a related pick, which can reserve item qty in fifo
			if (bIgnoreFifoReserveQty && utils.hasRecords(rTransDetail.in_item_trans_detail_to_prod_job_cost_material)) {
				processPickFifoForMaterials(rTransDetail, nTransQuantity, fsFifo);
			}

			for (var i = 1; i <= nMaxFifo; i++) {
				if (nTransQuantity == 0) {
					break;
				}
				rFifo = fsFifo.getRecord(i);

				if (!rFifo) {
					continue;
				}

				var nQtyToReduce = 0;
				var nQtyAvailable = rFifo.fifo_qty_remaining - rFifo.fifo_qty_reserved;
				if (sTransCode == TRANSACTION_TYPE.CountAdjustment) {
					nQtyAvailable = rFifo.fifo_qty_remaining;
				}
				if (bIgnoreFifoReserveQty) {
					nQtyToReduce = nTransQuantity;
					if (nQtyAvailable > 0) { // Not the last record and have available qty
						if (nTransQuantity >= nQtyAvailable) {
							nQtyToReduce = nQtyAvailable;
							nTransQuantity -= nQtyAvailable;
							rFifo.fifo_qty_remaining -= nQtyToReduce;
						}
						else { //We have more avaialable than the transaction qty
							rFifo.fifo_qty_remaining -= nQtyToReduce;
							nTransQuantity = 0;
						}
					}
				}
				else { //This block is specially designed to handle shipment transactions with or without pickslips and item transactions from Transactions modules
					/** @type {JSFoundSet<db:/avanti/in_item_fifo_reserved>} */
					var fsFifoReserved = getFifoReservedRecordsByTransaction(rTransDetail, sTransCode, rFifo.fifo_id.toString());

					for (var j = 1; j <= fsFifoReserved.getSize(); j++) {
						var rFifoReserved = fsFifoReserved.getRecord(j);
						//Need to skip pick reserved quantities, normally they should not exist, but sometimes they are not properly deleted
						if (sTransCode == TRANSACTION_TYPE.Shipment 
								&& utils.hasRecords(rTransDetail.in_item_trans_detail_to_sa_pack_detail) 
								&& rTransDetail.in_item_trans_detail_to_sa_pack_detail.pickd_id == rFifoReserved.fifor_source_pk) {
							continue;
						}
						nQtyToReduce += rFifoReserved.fifor_qty;
					}
					if (bReduceForBTO) {
						nQtyToReduce = reduceFifoQtyRemainingToZero(rBinTransferOutTransDet, rFifo, nQtyToReduce, rFifo.fifo_qty_remaining, bIgnoreFifoReserveQty, nShipped);
					}
					else {
						nQtyToReduce = reduceFifoQtyRemainingToZero(rTransDetail, rFifo, nQtyToReduce, rFifo.fifo_qty_remaining, bIgnoreFifoReserveQty, nShipped);
					}
					if (nQtyToReduce > 0) {
						nTransQuantity -= nQtyToReduce;
					}

					fsFifoReserved.deleteAllRecords();
				}

				if (nQtyToReduce > 0) {
					if (bReduceForBTO) {
						createFifoDetRecord(rFifo.fifo_id, rBinTransferOutTransDet.itemtransd_id, nQtyToReduce * -1, nShipped);
						rBinTransferOutTransDet.itemtransd_fifo_balance += globals["avUtilities_roundNumber"](nQtyToReduce, iDecimals); 
						//creating the positive fifo for the bin transfer in, must retain the original fifo date
						var rBTOFifo = createFifoRecord(rTransDetail, rTransDetail.initemroll_id, nQtyToReduce, 0, rFifo.fifo_date, rFifo.custproj_desc);
						if (rBTOFifo) {
							createFifoDetRecord(rBTOFifo.fifo_id, rTransDetail.itemtransd_id, nQtyToReduce, 0);
							rTransDetail.itemtransd_fifo_balance += globals["avUtilities_roundNumber"](nQtyToReduce * -1, iDecimals); 
						}
					}
					else {
						createFifoDetRecord(rFifo.fifo_id, rTransDetail.itemtransd_id, nQtyToReduce * -1, nShipped);
						rTransDetail.itemtransd_fifo_balance += globals["avUtilities_roundNumber"](nQtyToReduce, iDecimals); 
					}

				}

			}
			databaseManager.saveData(fsFifo);
		}
	}
}

/**
 * When item issue is being done from a location without any existing fifo, it has to create a negative fifo record to
 * keep the stats consistent.
 * @param {JSRecord<db:/avanti/in_item_trans_detail>} rTransDetail
 * @param {Boolean} bReduceForBTO
 * @param {JSRecord<db:/avanti/in_item_trans_detail>} rBinTransferOutTransDet
 * @param {Number} nShipped
 *
 * @properties={typeid:24,uuid:"757979D1-F05D-4695-AF15-D583F92B8E3A"}
 */
function createNegativeFifoRecords(rTransDetail, bReduceForBTO, rBinTransferOutTransDet, nShipped) {
	if (bReduceForBTO) {
		var rFifo = createFifoRecord(rBinTransferOutTransDet, rBinTransferOutTransDet.initemroll_id, rBinTransferOutTransDet.itemtransd_qty, nShipped, null);
		if (rFifo) {
			createFifoDetRecord(rFifo.fifo_id, rBinTransferOutTransDet.itemtransd_id, rBinTransferOutTransDet.itemtransd_qty, nShipped);
			//We need to create another positve transaction for bin transfers
			var rBTOFifo = createFifoRecord(rTransDetail, rTransDetail.initemroll_id, rTransDetail.itemtransd_qty, 0, rFifo.fifo_date);
			if (rBTOFifo) {
				createFifoDetRecord(rBTOFifo.fifo_id, rTransDetail.itemtransd_id, rBTOFifo.fifo_qty_remaining, 0);
			}
		}
	}
	else {
		rFifo = createFifoRecord(rTransDetail, rTransDetail.initemroll_id, rTransDetail.itemtransd_qty, nShipped, null);
		if (rFifo) {
			createFifoDetRecord(rFifo.fifo_id, rTransDetail.itemtransd_id, rTransDetail.itemtransd_qty, nShipped);
		}
	}
}

/**
 * @param {JSRecord<db:/avanti/in_item_trans_detail>} rTransDetail
 * @param {Number} nTransQuantity
 * @param {JSFoundSet<db:/avanti/in_item_fifo>} fsFifo
 *
 *
 * @properties={typeid:24,uuid:"CCBDACDC-5009-44F0-A0FB-12953AD5DE19"}
 */
function processPickFifoForMaterials(rTransDetail, nTransQuantity, fsFifo) {
	/** @type {JSFoundSet<db:/avanti/prod_job_cost_material>} */
	var fsMat = rTransDetail.in_item_trans_detail_to_prod_job_cost_material;
	if (utils.hasRecords(fsMat.prod_job_cost_material_to_sa_order_revision_detail) && fsMat.prod_job_cost_material_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_pick_detail) {
		
		var bHasAddtlJobs = false;
		if (utils.hasRecords(fsMat.prod_job_cost_material_to_prod_job_cost_labour) 
				&& utils.hasRecords(fsMat.prod_job_cost_material_to_prod_job_cost_labour.prod_job_cost_labour_to_prod_shopfloor_labor_job) 
				&& fsMat.prod_job_cost_material_to_prod_job_cost_labour.prod_job_cost_labour_to_prod_shopfloor_labor_job.getSize() > 1) {
			bHasAddtlJobs = true;
		}

		for (var i = 1; i <= fsMat.prod_job_cost_material_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_pick_detail.getSize(); i++) {
			var rPickDetail = fsMat.prod_job_cost_material_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_pick_detail.getRecord(i);
			if (rPickDetail.item_id == fsMat.item_id) {
				/** @type {JSFoundSet<db:/avanti/in_item_fifo_reserved>} */
				var fsFifoReserved;
				if (globals.nav_program_name == "Shop_Floor" 
						&& bHasAddtlJobs == false
						&& utils.hasRecords(rPickDetail.sa_pick_detail_to_in_item) 
						&& rPickDetail.sa_pick_detail_to_in_item.item_track_rolls == 1 
						&& utils.hasRecords(rPickDetail.sa_pick_detail_to_in_item.in_item_to_in_item_roll)) {
					var aArgs = new Array(globals.org_id, rPickDetail.pickd_id.toString(), fsFifo.fifo_id.toString());
					fsFifoReserved = scopes.avDB.getFSFromSQL("SELECT fifor_id FROM in_item_fifo_reserved WHERE org_id = ? AND fifor_source_pk = ? AND fifo_id = ?",'in_item_fifo_reserved',aArgs)
					fsFifoReserved.sort('in_item_fifo_reserved_to_in_item_fifo.in_item_fifo_to_in_item_roll.sequence_nr asc');
				}
				else {
					fsFifoReserved = rPickDetail.sa_pick_detail_to_in_item_fifo_reserved;
					fsFifoReserved.sort('in_item_fifo_reserved_to_in_item_fifo.fifo_date asc, in_item_fifo_reserved_to_in_item_fifo.fifo_qty_remaining asc');
				}
				//Reduce the qty that the shopfloor or time entry is using from the reserved.
				reduceFifoReserveForTransQty(nTransQuantity, fsFifoReserved, fsFifo.itemwhseloc_id, bHasAddtlJobs);
				databaseManager.saveData(fsFifoReserved);
			}
		}
	}
}

/**
 * Reduces the transaction qty accross all the fifo reserved records
 * @param nTransQuantity
 * @param {JSFoundSet<db:/avanti/in_item_fifo_reserved>} fsFifoReserved
 * @param {UUID} itemwhseloc_id
 * @param {Boolean} [bHasAddtlJobs]
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"5E4B52C7-A668-4142-86AF-2A3ECE65D6DD"}
 */
function reduceFifoReserveForTransQty(nTransQuantity, fsFifoReserved, itemwhseloc_id, bHasAddtlJobs) {
	var nQtyReducedSoFar = 0;
	if (!fsFifoReserved) {
		return 0;
	}

	var nTotalReserved = 0;
	var nTotalSkipped = 0;
	var j;
	var rFifoReserved;
	var nMax = databaseManager.getFoundSetCount(fsFifoReserved);
	for (j = 1; j <= nMax; j++) {

		rFifoReserved = fsFifoReserved.getRecord(j);
		if (!utils.hasRecords(rFifoReserved.in_item_fifo_reserved_to_in_item_fifo) || itemwhseloc_id != rFifoReserved.in_item_fifo_reserved_to_in_item_fifo.itemwhseloc_id) {
			nTotalSkipped++;
			continue;
		}
		var nDiff = nTransQuantity - nQtyReducedSoFar;
		nTotalReserved += rFifoReserved.fifor_qty;
		if (nDiff > 0) {
			if (nDiff >= rFifoReserved.fifor_qty) {
				nQtyReducedSoFar += rFifoReserved.fifor_qty;
				rFifoReserved.in_item_fifo_reserved_to_in_item_fifo.fifo_qty_reserved -= rFifoReserved.fifor_qty;
				//Reset unused committed roll quantity if applicable
				if (bHasAddtlJobs === false) {
					reduceRollCommitedQty(rFifoReserved, rFifoReserved.fifor_qty);
				}
				rFifoReserved.fifor_qty = 0;
			}
			else {
				nQtyReducedSoFar += nDiff;
				rFifoReserved.in_item_fifo_reserved_to_in_item_fifo.fifo_qty_reserved -= nDiff;
				//Reset unused committed roll quantity if applicable
				if (bHasAddtlJobs === false) {
					reduceRollCommitedQty(rFifoReserved, nDiff);
				}
				rFifoReserved.fifor_qty -= nDiff;
			}
		}
	}

	// The production pick allocated from different bins than the bin that the transaction used,
	//in this case we just need to reduce the reserve fifo and return 0
	if (nTotalSkipped == nMax) {
		for (j = 1; j <= nMax; j++) {
			rFifoReserved = fsFifoReserved.getRecord(j);
			if (!utils.hasRecords(rFifoReserved.in_item_fifo_reserved_to_in_item_fifo)) {
				continue;
			}
			nDiff = nTransQuantity - nQtyReducedSoFar;
			if (nDiff > 0) {
				if (nDiff >= rFifoReserved.fifor_qty) {
					nQtyReducedSoFar += rFifoReserved.fifor_qty;
					rFifoReserved.in_item_fifo_reserved_to_in_item_fifo.fifo_qty_reserved -= rFifoReserved.fifor_qty;
					//Reset unused committed roll quantity if applicable
					if (bHasAddtlJobs === false) {
						reduceRollCommitedQty(rFifoReserved, rFifoReserved.fifor_qty);
					}
					rFifoReserved.fifor_qty = 0;
				}
				else {
					nQtyReducedSoFar += nDiff;
					rFifoReserved.in_item_fifo_reserved_to_in_item_fifo.fifo_qty_reserved -= nDiff;
					rFifoReserved.fifor_qty -= nDiff;
					//Reset unused committed roll quantity if applicable
					if (bHasAddtlJobs === false) {
						reduceRollCommitedQty(rFifoReserved, nDiff);
					}
				}
			}
		}
		return 0;
	}
	return nTotalReserved - nQtyReducedSoFar;
}

/**
 * Reduces fifo record's remaining qty to 0, if there is still nQtyToReduce it will create a
 * new fifo record with a negative remaining qty for the difference
 * @param {JSRecord<db:/avanti/in_item_trans_detail>} rTransDetail
 * @param {JSRecord<db:/avanti/in_item_fifo>} rFifo
 * @param {Number} nQtyToReduce
 * @param {Number} nQtyRemaining
 * @param {Boolean} bIgnoreFifoReserveQty
 * @param {Number} nShipped
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"BD381D35-7F72-4B63-AE1B-3CB6337FEBF8"}
 */
function reduceFifoQtyRemainingToZero(rTransDetail, rFifo, nQtyToReduce, nQtyRemaining, bIgnoreFifoReserveQty, nShipped) {
	var nDiff = nQtyRemaining - nQtyToReduce;
	if (nDiff >= 0) {
		rFifo.fifo_qty_remaining = nDiff;
		if (!bIgnoreFifoReserveQty) {
			rFifo.fifo_qty_reserved -= nQtyToReduce;
		}
	}
	else {
		if (rFifo.fifo_qty_remaining < 0) {
			rFifo.fifo_qty_remaining -= nQtyToReduce;
			if (!bIgnoreFifoReserveQty) {
				rFifo.fifo_qty_reserved -= nQtyToReduce;
			}
		}
	}

	return nQtyToReduce;
}

/**
 * this function calls reserveQtyForFifo(). trying first to reserve this-project-inventory, then non-project-inventory, then other-project-inventory
 *
 * @param {Number} nQty
 * @param {JSFoundset<db:/avanti/in_item_fifo>} fsFifo
 * @param {String} sSourceType
 * @param {UUID} sSourcePK
 * @param {String|UUID} [sSourcePK2] - an additional pk, eg in shipping pk1 is for pack detail and, when shipping a bto kit, pk2 is for kit item line item
 * @param {String} [sProject]
 *
 * @properties={typeid:24,uuid:"D2BF73BF-FD69-4548-8BC3-8EB746FBD1B1"}
 */
function reserveQtyForFifo_preferProject(nQty, fsFifo, sSourceType, sSourcePK, sSourcePK2, sProject) {
	// first try to reserve qty using project fifo recs
	var nQtyNotReserved = reserveQtyForFifo(nQty, fsFifo, sSourceType, sSourcePK, sSourcePK2, true, sProject, true);

	// the user may have overridden the auto-allocation - so if we use project inv and we couldnt reserve the full qty then try again with the unreserved amt, with project inv off
	if (nQtyNotReserved > 0) {
		// next try to use non-project inventory - if they passed in a project (could be we are looking for non-project inventory)
		if (sProject) {
			nQtyNotReserved = reserveQtyForFifo(nQtyNotReserved, fsFifo, sSourceType, sSourcePK, sSourcePK2, true, null, true);
		}

		// if there's still not enough then try other project inventory, and take off bBypassReserveIfUnavailable flag
		if (nQtyNotReserved > 0) {
			reserveQtyForFifo(nQtyNotReserved, fsFifo, sSourceType, sSourcePK, sSourcePK2);
		}
	}
}

/**
 * Loops through the fifo foundset and sets reserved the nQty and
 * creates the neccessary in item fifo reserved record
 * @param {Number} nQty
 * @param {JSFoundset<db:/avanti/in_item_fifo>} fsFifo
 * @param {String} sSourceType
 * @param {UUID} sSourcePK
 * @param {String|UUID} [sSourcePK2] - an additional pk, eg in shipping pk1 is for pack detail and, when shipping a bto kit, pk2 is for kit item line item
 * @param {Boolean} [bUseProjectInventory]
 * @param {String} [sProject]
 * @param {Boolean} [bBypassReserveIfUnavailable]
 * @param {UUID} [uItemRollId]
 *
 * @return {Number} - return number we couldnt reserve
 *
 *
 * @properties={typeid:24,uuid:"EE62E4A5-5116-40FA-91A9-09DBBF235D1E"}
 */
function reserveQtyForFifo(nQty, fsFifo, sSourceType, sSourcePK, sSourcePK2, bUseProjectInventory, sProject, bBypassReserveIfUnavailable, uItemRollId) {
	if (sProject && !scopes.avInv.isProjectInventoryOn()) {
		sProject = null;
	}

	var nMaxFifo = databaseManager.getFoundSetCount(fsFifo);
	for (var j = 1; j <= nMaxFifo; j++) {

		if (nQty == 0) {
			break;
		}
		var rFifo = fsFifo.getRecord(j);
		
		if (uItemRollId && rFifo.initemroll_id != uItemRollId) {
			continue;
		}

		if (!rFifo || rFifo && rFifo.fifo_qty_remaining - rFifo.fifo_qty_reserved <= 0) {
			continue;
		}

		// sl-17284 - if there is a project then we can only use fifo for that project - or if no project then we can only use non-project inv
		if (bUseProjectInventory && rFifo.custproj_desc != sProject) {
			continue;
		}

		var nQtyAvailable = rFifo.fifo_qty_remaining - rFifo.fifo_qty_reserved;
		var nQtyReserve = nQty;
		
		if (nQtyAvailable > nQty) {
			nQtyReserve = nQty;
			rFifo.fifo_qty_reserved += nQtyReserve;
			nQty = 0;
		}
		else {
			nQtyReserve = nQtyAvailable;
			rFifo.fifo_qty_reserved += nQtyReserve;
			nQty -= nQtyReserve;
		}

		createFifoReservedRecord(rFifo.fifo_id, sSourceType, sSourcePK, nQtyReserve, sSourcePK2);
	}

	// return number we couldnt reserve
	return nQty;
}

/**
 * Creates a pick fifo detail record for the pick detail record
 * @param {UUID} fifo_id
 * @param {String} fifor_source_type
 * @param {UUID} fifor_source_pk
 * @param {Number} qty
 * @param {String|UUID} [sSourcePK2] - an additional pk, eg in shipping pk1 is for pack detail and, when shipping a
 *                              bto kit, pk2 is for kit item line item
 *
 * @return {JSRecord<db:/avanti/in_item_fifo_reserved>}
 *
 * @properties={typeid:24,uuid:"7DFCCE30-B34E-4B92-9989-9096B332B7F7"}
 */
function createFifoReservedRecord(fifo_id, fifor_source_type, fifor_source_pk, qty, sSourcePK2) {
	/***@type {JSFoundset<db:/avanti/in_item_fifo_reserved>} */
	var fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_fifo_reserved');
	var rec = fs.getRecord(fs.newRecord());

	rec.fifo_id = fifo_id;
	rec.fifor_source_type = fifor_source_type;
	rec.fifor_source_pk = fifor_source_pk;
	rec.fifor_qty = qty;

	if (sSourcePK2) {
		rec.fifor_source_pk_2 = sSourcePK2;
	}

	// sl-20710 - have to save this or 2 records end up being created in shipping instead of 1, because the later code doesnt see the record that was created earlier
	databaseManager.saveData(rec);
	
	return rec;
}

/**
 * @param {String} fifor_source_pk
 * @param {String} fifor_source_type
 * @return {JSFoundset<db:/avanti/in_item_fifo_reserved>}
 *
 * @properties={typeid:24,uuid:"613C18F7-7BCF-4DFB-AED7-A23D767A7EBE"}
 */
function getFifoReservedBySourcePK(fifor_source_pk, fifor_source_type) {
	/***@type {JSFoundset<db:/avanti/in_item_fifo_reserved>} */
	var fsFifoReserved = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_fifo_reserved');
	var oSQL = new Object();
	oSQL.sql = "SELECT fifor_id\
        FROM in_item_fifo_reserved \
        WHERE org_id = ? \
        AND fifor_source_type = ? AND fifor_source_pk = ?";

	oSQL.args = [globals.org_id, fifor_source_type, fifor_source_pk];

	fsFifoReserved.clear();
	fsFifoReserved.loadRecords(oSQL.sql, oSQL.args);
	return fsFifoReserved;
}

/**
 * Returns in_item_fifo_reserved foundset for the transaction
 * @param {JSRecord<db:/avanti/in_item_trans_detail>} rTransDetail
 * @param {String} sTransCode
 * @param {String} fifo_id
 * @return {JSFoundSet<db:/avanti/in_item_fifo_reserved>}
 *
 * @properties={typeid:24,uuid:"A76563D6-AB22-4BDA-B6A5-A6271A8FBE45"}
 */
function getFifoReservedRecordsByTransaction(rTransDetail, sTransCode, fifo_id) {
	var aFiforPKs = new Array();
	/** @type {JSFoundSet<db:/avanti/in_item_fifo_reserved>} */
	var fsFifoReserved = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_fifo_reserved');

	var oSQL = new Object();
	if (sTransCode == TRANSACTION_TYPE.WarehousePick) {
		oSQL.sql = "SELECT itfr.fifor_id\
            FROM in_item_fifo_reserved AS itfr\
            INNER JOIN in_item_fifo AS itf ON itf.fifo_id = itfr.fifo_id\
            WHERE itfr.fifo_id = ? \
            AND itfr.org_id = ? \
            AND itfr.fifor_source_pk IN\
            (SELECT spd.pickd_id\
                    FROM sa_pick_detail spd\
                    INNER JOIN sa_pick sp ON spd.pick_id = sp.pick_id\
                    WHERE sp.pick_type = 'W' \
                    AND spd.item_id = ? AND spd.whse_id = ?)";

		oSQL.args = [fifo_id, globals.org_id, rTransDetail.item_id.toString(), rTransDetail.whse_id.toString()];

		fsFifoReserved.clear();
		fsFifoReserved.loadRecords(oSQL.sql, oSQL.args);
		return fsFifoReserved;
	}
	else if (sTransCode == TRANSACTION_TYPE.FulfillmentPick) {
		oSQL.sql = "SELECT itfr.fifor_id\
            FROM in_item_fifo_reserved AS itfr\
            INNER JOIN in_item_fifo AS itf ON itf.fifo_id = itfr.fifo_id\
            WHERE itfr.fifo_id = ? \
            AND itfr.org_id = ? \
            AND itfr.fifor_source_pk IN\
            (SELECT spd.pickd_id\
                    FROM sa_pick_detail spd\
                    INNER JOIN sa_pick sp ON spd.pick_id = sp.pick_id\
                    WHERE sp.pick_type = 'F' \
                    AND spd.item_id = ? AND spd.whse_id = ?)";

		oSQL.args = [fifo_id, globals.org_id, rTransDetail.item_id.toString(), rTransDetail.whse_id.toString()];
		fsFifoReserved.loadRecords(oSQL.sql, oSQL.args);
		return fsFifoReserved;
	}
	else if (sTransCode == TRANSACTION_TYPE.Shipment && utils.hasRecords(rTransDetail.in_item_trans_detail_to_sa_pack_detail)) {
		aFiforPKs.push(rTransDetail.in_item_trans_detail_to_sa_pack_detail.packd_id);
		if (rTransDetail.in_item_trans_detail_to_sa_pack_detail.pickd_id) {
			aFiforPKs.push(rTransDetail.in_item_trans_detail_to_sa_pack_detail.pickd_id);
		}
	}
	else {
		aFiforPKs.push(rTransDetail.itemtransd_id);
	}

	oSQL.sql = "SELECT itfr.fifor_id\
                FROM in_item_fifo_reserved AS itfr\
                INNER JOIN in_item_fifo AS itf ON itf.fifo_id = itfr.fifo_id\
                WHERE itfr.fifo_id = ? \
                AND itfr.org_id = ? \
                AND itfr.fifor_source_pk in ('" + aFiforPKs.join("','") + "')";

	oSQL.args = [fifo_id, globals.org_id];

	fsFifoReserved.clear();
	fsFifoReserved.loadRecords(oSQL.sql, oSQL.args);
	return fsFifoReserved;
}

/**
 * Loops through the fifo reserved records and resets the reserved qty in the releated item fifo record
 * finally deletes all the fifo reserved records.
 * @param {JSFoundSet<db:/avanti/in_item_fifo_reserved>} fsFifoReserved
 *
 * @properties={typeid:24,uuid:"5C8D5CC6-9E4A-4EB5-92FC-F6296F8FABFD"}
 */
function deleteFifoReservedRecords(fsFifoReserved) {
	reduceAllFifoReservedQty(fsFifoReserved);
	databaseManager.saveData();
	fsFifoReserved.deleteAllRecords();
}

/**
 * @param {JSFoundSet<db:/avanti/in_item_fifo_reserved>} fsFifoReserved
 *
 * @properties={typeid:24,uuid:"A6CBFEEB-2709-4028-BBA4-975D5EF7CCD0"}
 */
function reduceAllFifoReservedQty(fsFifoReserved) {
	if (!fsFifoReserved) {
		return;
	}

	for (var j = 1; j <= fsFifoReserved.getSize(); j++) {
		var rFifoRes = fsFifoReserved.getRecord(j);
		if (utils.hasRecords(rFifoRes.in_item_fifo_reserved_to_in_item_fifo)) {
			rFifoRes.in_item_fifo_reserved_to_in_item_fifo.fifo_qty_reserved -= rFifoRes.fifor_qty;
			//Reset unused committed roll quantity
			reduceRollCommitedQty(rFifoRes, rFifoRes.fifor_qty);
		}
	}
}

/**
 * @param {JSRecord<db:/avanti/in_item_fifo_reserved>} rFifoRes
 * @param {Number} qty
 *
 * @properties={typeid:24,uuid:"C1757117-3332-4F78-83EC-3D1CB84BC049"}
 */
function reduceRollCommitedQty(rFifoRes, qty) {
	if (globals.nav_program_name == "Confirm_Shipment" 
		&& utils.hasRecords(rFifoRes.in_item_fifo_reserved_to_in_item_fifo)
		&& utils.hasRecords(rFifoRes.in_item_fifo_reserved_to_in_item_fifo.in_item_fifo_to_in_item_roll) 
		&& rFifoRes.in_item_fifo_reserved_to_in_item_fifo.in_item_fifo_to_in_item_roll.initemroll_committed > 0) {
		rFifoRes.in_item_fifo_reserved_to_in_item_fifo.in_item_fifo_to_in_item_roll.initemroll_committed -= qty;
		if (rFifoRes.in_item_fifo_reserved_to_in_item_fifo.in_item_fifo_to_in_item_roll.initemroll_committed < 0) {
			rFifoRes.in_item_fifo_reserved_to_in_item_fifo.in_item_fifo_to_in_item_roll.initemroll_committed = 0;
		}
	}
}


/**
 * @param {UUID} fifo_id
 * @param {UUID} itemtransd_id
 * @param {Number} qty
 * @param {Number} nShipped flag is used to mark the latest fifo details records
 * @return {JSRecord<db:/avanti/in_item_fifo_det>}
 *
 * @properties={typeid:24,uuid:"FEBB760D-F17F-4599-908E-E638A474A4E9"}
 */
function createFifoDetRecord(fifo_id, itemtransd_id, qty, nShipped) {
	/***@type {JSFoundset<db:/avanti/in_item_fifo_det>} */
	var fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_fifo_det');
	var rec = fs.getRecord(fs.newRecord());
	rec.fifo_id = fifo_id;
	rec.itemtransd_id = itemtransd_id;
	rec.fifod_qty = qty;
	//This flag is used to mark the latest fifo details records, that were created
	//for the latest confirmed shipment. This way during cancel shipment we can easily identify them.
	rec.shipped = nShipped;
//	globals.dbLog('Fifo Rebuild: createFifoDetRecord ' + itemtransd_id, 'fifo_rebuild', 'rebuild', 'file', 'rebuild', null, 'logging', 'Detail', null, null, null, null);
	return rec;
}

/**
 * Updates the TransEntryDetail for Cancelling Stock Receipt
 * @param {JSRecord<db:/avanti/in_item_trans_detail>} rTransDetail
 * @param {Date} itemtransh_date
 *
 * @properties={typeid:24,uuid:"FBC2F5F0-F55B-4C68-8409-3EBCDFEF123A"}
 */
function updateTransDetailForCSR(rTransDetail, itemtransh_date) {
	if (!utils.hasRecords(rTransDetail.in_item_trans_detail_to_po_receipt_detail)) {
		return;
	}
	//update stock allocation - detail quantity
	for (var i = 1; i <= rTransDetail.in_item_trans_detail_to_po_receipt_detail.po_receipt_detail_to_po_receipt_detail_qty.getSize(); i++) {
		/***@type {JSRecord<db:/avanti/po_receipt_detail_qty>} ***/
		var rReceiptDetailQty = rTransDetail.in_item_trans_detail_to_po_receipt_detail.po_receipt_detail_to_po_receipt_detail_qty.getRecord(i);
		rTransDetail.in_item_trans_detail_to_po_receipt_detail.po_receipt_detail_to_po_purchase_detail.podet_qty_received_stkuom -= rReceiptDetailQty.porecdq_qty_received;

		if (!utils.hasRecords(rReceiptDetailQty.po_receipt_detail_qty_to_po_purchase_detail_qty)) {
			continue;
		}
		//update the purchase detail quantity
		rReceiptDetailQty.po_receipt_detail_qty_to_po_purchase_detail_qty.podetq_qty_received -= rReceiptDetailQty.porecdq_qty_received;

		//OutSide Service?
		if (!utils.hasRecords(rReceiptDetailQty.po_receipt_detail_qty_to_po_receipt_detail) || !utils.hasRecords(rReceiptDetailQty.po_receipt_detail_qty_to_po_receipt_detail.po_receipt_detail_to_po_purchase_detail) || !utils.hasRecords(rReceiptDetailQty.po_receipt_detail_qty_to_po_receipt_detail.po_receipt_detail_to_po_purchase_detail.po_purchase_detail_to_in_item) || rReceiptDetailQty.po_receipt_detail_qty_to_po_receipt_detail.po_receipt_detail_to_po_purchase_detail.po_purchase_detail_to_in_item.itemtype_code != 'ZZ') {
			continue;
		}
		//Update the receipt quantity (qty shipped in revd_item)

		if (utils.hasRecords(rReceiptDetailQty.po_receipt_detail_qty_to_po_purchase_detail_qty.po_purchase_detail_qty_to_po_planned_master) && utils.hasRecords(rReceiptDetailQty.po_receipt_detail_qty_to_po_purchase_detail_qty.po_purchase_detail_qty_to_po_planned_master.po_planned_master_to_sa_order_revd_item$outside_services)) {

			var fsSaOrderRevdItem = rReceiptDetailQty.po_receipt_detail_qty_to_po_purchase_detail_qty.po_purchase_detail_qty_to_po_planned_master.po_planned_master_to_sa_order_revd_item$outside_services;
			fsSaOrderRevdItem.ordrevditem_shipped_qty -= rReceiptDetailQty.porecdq_qty_received;

			if (fsSaOrderRevdItem.ordrevditem_shipped_qty < 0) {
				fsSaOrderRevdItem.ordrevditem_shipped_qty = 0;
			}
		}

		if (!utils.hasRecords(rReceiptDetailQty.po_receipt_detail_qty_to_po_purchase_detail_qty.po_purchase_detail_qty_to_prod_job) || !utils.hasRecords(rReceiptDetailQty.po_receipt_detail_qty_to_po_purchase_detail_qty.po_purchase_detail_qty_to_po_planned_master)) {
			continue;
		}

		var rJob = rReceiptDetailQty.po_receipt_detail_qty_to_po_purchase_detail_qty.po_purchase_detail_qty_to_prod_job.getSelectedRecord();
		var fsProdJobCost = rJob.prod_job_to_prod_job_cost;

		fsProdJobCost.newRecord(false);
		fsProdJobCost.jc_cost_type = scopes.avUtils.JOB_COST_TYPE.Purchase;
		rReceiptDetailQty.jc_id = fsProdJobCost.jc_id;
		fsProdJobCost.jc_date = itemtransh_date;
		fsProdJobCost.empl_id = globals.avBase_employeeUUID;
		fsProdJobCost.ordrevds_id = rReceiptDetailQty.po_receipt_detail_qty_to_po_purchase_detail_qty.po_purchase_detail_qty_to_po_planned_master.po_planned_master_to_sa_order_revds_task.ordrevds_id;
		fsProdJobCost.ordrevdstask_id = rReceiptDetailQty.po_receipt_detail_qty_to_po_purchase_detail_qty.po_purchase_detail_qty_to_po_planned_master.ordrevdstask_id;

		if (utils.hasRecords(fsProdJobCost.prod_job_cost_to_sch_milestone$ordrevdstask_id)) {
			fsProdJobCost.ms_id = fsProdJobCost.prod_job_cost_to_sch_milestone$ordrevdstask_id.ms_id;
			fsProdJobCost.opcat_id = fsProdJobCost.prod_job_cost_to_sch_milestone$ordrevdstask_id.opcat_id;
			fsProdJobCost.cc_id = fsProdJobCost.prod_job_cost_to_sch_milestone$ordrevdstask_id.sch_milestone_to_sch_milestone_group.cc_id;
			if (utils.hasRecords(fsProdJobCost.prod_job_cost_to_sys_cost_centre)) {
				fsProdJobCost.jc_op_code = fsProdJobCost.prod_job_cost_to_sys_cost_centre.cc_op_code;
				fsProdJobCost.dept_id = fsProdJobCost.prod_job_cost_to_sys_cost_centre.dept_id;
			}
		}

		//get the original cost transaction type so we are cancelling the same type
		var sOriginalCostType = scopes.avUtils.JOB_COST_TRANSACTION_TYPE.WorkInProgress;
		var rReceiptDetail = rReceiptDetailQty.po_receipt_detail_qty_to_po_receipt_detail.getRecord(1);
		if (utils.hasRecords(rReceiptDetail.po_receipt_detail_to_prod_job_cost_transactions)) {
			sOriginalCostType = rReceiptDetail.po_receipt_detail_to_prod_job_cost_transactions.jct_transaction_type;
		}

		var fsPOReceipt = rReceiptDetailQty.po_receipt_detail_qty_to_po_receipt_detail.po_receipt_detail_to_po_receipt;

		fsProdJobCost.jc_charge_flag = 1;
		fsProdJobCost.jc_comment = "Cancel Stock Receipt: " + fsPOReceipt.porec_reference;

		var fsProdJobCostPurchases = fsProdJobCost.prod_job_cost_to_prod_job_cost_purchases;

		fsProdJobCostPurchases.newRecord(false);
		fsProdJobCostPurchases.item_id = rReceiptDetailQty.po_receipt_detail_qty_to_po_purchase_detail_qty.item_id;
		fsProdJobCostPurchases.jobp_qty += rTransDetail.itemtransd_qty;
		fsProdJobCostPurchases.jobp_total_cost = fsProdJobCostPurchases.jobp_qty * rReceiptDetailQty.po_receipt_detail_qty_to_po_receipt_detail.porecd_landed_cost;
		// If the freight cost method is WIP, subtract the freight cost to the purchase cost.
		if (utils.hasRecords(fsPOReceipt.po_receipt_to_po_purchase) && fsPOReceipt.po_receipt_to_po_purchase.po_freight_in_cost_method == scopes.avAccounting.FREIGHT_METHOD.WIP) {
			fsProdJobCostPurchases.jobp_total_cost -= fsPOReceipt.porec_freight_amt;
		}
		fsProdJobCostPurchases.itemtransd_id = rTransDetail.itemtransd_id;
		fsProdJobCostPurchases.supplier_id = fsPOReceipt.po_receipt_to_po_purchase.supplier_id;
		fsProdJobCostPurchases.jcp_is_posted = 1;
		fsProdJobCostPurchases.porecd_id = rReceiptDetailQty.porecd_id;

		//Create Cost Transaction based on Status
		fsProdJobCost.prod_job_cost_to_prod_job_cost_transactions.newRecord(false, true);
		fsProdJobCost.prod_job_cost_to_prod_job_cost_transactions.porecd_id_cancel = rReceiptDetailQty.po_receipt_detail_qty_to_po_receipt_detail.porecd_id;
		fsProdJobCost.prod_job_cost_to_prod_job_cost_transactions.jct_transaction_type = sOriginalCostType;
		if (sOriginalCostType == scopes.avUtils.JOB_COST_TRANSACTION_TYPE.FinishedGoods) {
			if (fsProdJobCost.finished_goods_date == null) {
				fsProdJobCost.finished_goods_date = application.getTimeStamp();
			}
		}
		else if (sOriginalCostType == scopes.avUtils.JOB_COST_TRANSACTION_TYPE.CostOfGoodsSold) {
			if (fsProdJobCost.cogs_date == null) {
				fsProdJobCost.cogs_date = application.getTimeStamp();
			}
		}
	}
}

/**
 * Updates the TransEntryDetail for Receipt from Production
 * @param {JSRecord<db:/avanti/in_item_trans_detail>} rTransDetail
 *
 * @properties={typeid:24,uuid:"B4A0B87A-56C1-41A5-938F-BCD1BC56E926"}
 */
function updateTransDetailForRFP(rTransDetail) {
	if (!utils.hasRecords(rTransDetail.in_item_trans_detail_to_prod_receipt_detail) || !utils.hasRecords(rTransDetail.in_item_trans_detail_to_prod_receipt_detail.prod_receipt_detail_to_sa_order_revision_detail)) {
		return;
	}
	/***@type {JSFoundset<db:/avanti/sa_order_revision_detail>} ***/
	var fsSaOrdRevDetail = rTransDetail.in_item_trans_detail_to_prod_receipt_detail.prod_receipt_detail_to_sa_order_revision_detail;

	var nOriginalReceiptBalance = fsSaOrdRevDetail.ordrevd_prod_receipt_qty;

	if (fsSaOrdRevDetail.ordrevd_prodrec_cancel_bal == 1) {
		return;
	}

	var nBalanceToApply = nOriginalReceiptBalance * -1;
	var nExpectedInAdj = 0;

	fsSaOrdRevDetail.ordrevd_prod_receipt_qty += rTransDetail.itemtransd_qty;

	if (fsSaOrdRevDetail.ordrevd_prod_receipt_qty <= fsSaOrdRevDetail.ordrevd_qty_ordered) {
		nExpectedInAdj = nBalanceToApply + fsSaOrdRevDetail.ordrevd_prod_receipt_qty;
	}
	else {
		var nDiff = fsSaOrdRevDetail.ordrevd_prod_receipt_qty - fsSaOrdRevDetail.ordrevd_qty_ordered;

		nExpectedInAdj = nBalanceToApply + fsSaOrdRevDetail.ordrevd_prod_receipt_qty - nDiff;
		if (nExpectedInAdj < 0) {
			nExpectedInAdj = 0;
		}
	}

	//If a production order decrease "Expected in" quantity by the qty being received (up to a maximum of the sales order quantity)
	if (fsSaOrdRevDetail.ordrevd_finished_good_item_id != null && utils.stringTrim(fsSaOrdRevDetail.ordrevd_finished_good_item_id.toString()) != "") {
		if (nExpectedInAdj != 0) {
			globals.avBase_Inventory_UpdateItemQuantities(fsSaOrdRevDetail.ordrevd_finished_good_item_id,
				fsSaOrdRevDetail.whse_id, null, 0, 0, 0, nExpectedInAdj * -1, 0);
		}

		//Cancel Balance logic. Only need to adjust up to the order quantity maximum
		if (rTransDetail.in_item_trans_detail_to_prod_receipt_detail.prodrec_cancel_bal == 1) {
			fsSaOrdRevDetail.ordrevd_prodrec_cancel_bal = 1;

			if (fsSaOrdRevDetail.ordrevd_prod_receipt_qty < fsSaOrdRevDetail.ordrevd_qty_ordered) {

				nExpectedInAdj = fsSaOrdRevDetail.ordrevd_qty_ordered - fsSaOrdRevDetail.ordrevd_prod_receipt_qty;

				globals.avBase_Inventory_UpdateItemQuantities(fsSaOrdRevDetail.ordrevd_finished_good_item_id,
					fsSaOrdRevDetail.whse_id, null, 0, 0, 0, nExpectedInAdj * -1, 0);
			}
		}
	}
}

/**
 * Updates the TransEntryDetail for Stock Receipt
 * @param {JSRecord<db:/avanti/in_item_trans_detail>} rTransDetail
 * @param {Date} itemtransh_date
 *
 * @properties={typeid:24,uuid:"79A8695D-3A0A-4787-958C-6B36CF2254B5"}
 */
function updateTransDetailForStockReceipt(rTransDetail, itemtransh_date) {
	if (!utils.hasRecords(rTransDetail.in_item_trans_detail_to_po_receipt_detail)) {
		return;
	}
	//update stock allocation - detail quantity
	for (var i = 1; i <= rTransDetail.in_item_trans_detail_to_po_receipt_detail.po_receipt_detail_to_po_receipt_detail_qty.getSize(); i++) {
		/***@type {JSRecord<db:/avanti/po_receipt_detail_qty>} ***/
		var rReceiptDetailQty = rTransDetail.in_item_trans_detail_to_po_receipt_detail.po_receipt_detail_to_po_receipt_detail_qty.getRecord(i);

		//OutSide Service?
		if (!utils.hasRecords(rReceiptDetailQty.po_receipt_detail_qty_to_po_purchase_detail_qty) || !utils.hasRecords(rReceiptDetailQty.po_receipt_detail_qty_to_po_receipt_detail.po_receipt_detail_to_po_purchase_detail) || !utils.hasRecords(rReceiptDetailQty.po_receipt_detail_qty_to_po_receipt_detail.po_receipt_detail_to_po_purchase_detail.po_purchase_detail_to_in_item) || rReceiptDetailQty.po_receipt_detail_qty_to_po_receipt_detail.po_receipt_detail_to_po_purchase_detail.po_purchase_detail_to_in_item.itemtype_code != 'ZZ') {
			continue;
		}

		var fsPoPlannedMaster;
		//Update the receipt quantity (qty shipped in revd_item)
		//Manullay copied over changes from SL-14878 by Clynn
		if (utils.hasRecords(rReceiptDetailQty.po_receipt_detail_qty_to_po_purchase_detail_qty.po_purchase_detail_qty_to_po_planned_master)) {
			fsPoPlannedMaster = rReceiptDetailQty.po_receipt_detail_qty_to_po_purchase_detail_qty.po_purchase_detail_qty_to_po_planned_master;
			if (utils.hasRecords(fsPoPlannedMaster.po_planned_master_to_sa_order_revd_item$outside_services)) {
				fsPoPlannedMaster.po_planned_master_to_sa_order_revd_item$outside_services.ordrevditem_shipped_qty += rReceiptDetailQty.porecdq_qty_received;
			}
		}

		if (!utils.hasRecords(rReceiptDetailQty.po_receipt_detail_qty_to_po_purchase_detail_qty.po_purchase_detail_qty_to_prod_job)) {
			continue;
		}

		var rJob = rReceiptDetailQty.po_receipt_detail_qty_to_po_purchase_detail_qty.po_purchase_detail_qty_to_prod_job.getSelectedRecord();
		var fsProdJobCost = rJob.prod_job_to_prod_job_cost;
		fsProdJobCost.newRecord(false);
		fsProdJobCost.jc_cost_type = scopes.avUtils.JOB_COST_TYPE.Purchase;
		rReceiptDetailQty.jc_id = fsProdJobCost.jc_id;
		fsProdJobCost.jc_date = itemtransh_date;
		fsProdJobCost.created_date = fsProdJobCost.jc_date; //This field drives the accounting transaction date.
		fsProdJobCost.empl_id = globals.avBase_employeeUUID;
		//Manullay copied over changes from SL-14878 by Clynn
		if (fsPoPlannedMaster && utils.hasRecords(fsPoPlannedMaster.po_planned_master_to_sa_order_revds_task)) {
			fsProdJobCost.ordrevds_id = fsPoPlannedMaster.po_planned_master_to_sa_order_revds_task.ordrevds_id;
			fsProdJobCost.ordrevdstask_id = fsPoPlannedMaster.ordrevdstask_id;
		}

		// SL-24139: Always get the cost centre information from the buyout task and not the milestones.
		if (utils.hasRecords(fsProdJobCost.prod_job_cost_to_sa_order_revds_task) 
				&& utils.hasRecords(fsProdJobCost.prod_job_cost_to_sa_order_revds_task.sa_order_revds_task_to_sa_task)) {
			var rTask = fsProdJobCost.prod_job_cost_to_sa_order_revds_task.sa_order_revds_task_to_sa_task.getRecord(1);

			/**@type {JSRecord<db:/avanti/app_task_operation>} **/
			var rTaskOperation = scopes.avDB.getRec("app_task_operation", ["taskoper_key"], ["i18n:avanti.lbl.buyout"]);

			if (rTaskOperation) {
				/**@type {JSRecord<db:/avanti/sa_task_cost_link>} **/
				var rTaskCostLink = scopes.avDB.getRec("sa_task_cost_link", ["task_id", "taskoper_id"], [rTask.task_id, rTaskOperation.taskoper_id]);

				if (rTaskCostLink && utils.hasRecords(rTaskCostLink.sa_task_cost_link_to_sys_cost_centre)) {
					fsProdJobCost.cc_id = rTaskCostLink.cc_id;
					fsProdJobCost.opcat_id = rTaskCostLink.sa_task_cost_link_to_sys_cost_centre.opcat_id;
					fsProdJobCost.jc_op_code = rTaskCostLink.sa_task_cost_link_to_sys_cost_centre.cc_op_code;
					fsProdJobCost.dept_id = rTaskCostLink.sa_task_cost_link_to_sys_cost_centre.dept_id;
				}
			}
		}

		var fsPOReceipt = rReceiptDetailQty.po_receipt_detail_qty_to_po_receipt_detail.po_receipt_detail_to_po_receipt;

		fsProdJobCost.jc_charge_flag = 1;
		fsProdJobCost.jc_comment = "Stock Receipt: " + fsPOReceipt.porec_reference;

		var fsProdJobCostPurchases = fsProdJobCost.prod_job_cost_to_prod_job_cost_purchases;

		fsProdJobCostPurchases.newRecord(false);
		fsProdJobCostPurchases.item_id = rReceiptDetailQty.po_receipt_detail_qty_to_po_purchase_detail_qty.item_id;
		fsProdJobCostPurchases.jobp_qty = rTransDetail.itemtransd_qty
		fsProdJobCostPurchases.jobp_total_cost = rTransDetail.itemtransd_qty * rReceiptDetailQty.po_receipt_detail_qty_to_po_receipt_detail.porecd_landed_cost

		// If the freight cost method is WIP, add the freight cost to the purchase cost.
		if (utils.hasRecords(fsPOReceipt.po_receipt_to_po_purchase) && fsPOReceipt.po_receipt_to_po_purchase.po_freight_in_cost_method == scopes.avAccounting.FREIGHT_METHOD.WIP) {
			fsProdJobCostPurchases.jobp_total_cost += rReceiptDetailQty.po_receipt_detail_qty_to_po_receipt_detail.porecd_freight_wip_cost_total;
		}

		fsProdJobCostPurchases.itemtransd_id = rTransDetail.itemtransd_id;
		fsProdJobCostPurchases.supplier_id = fsPOReceipt.po_receipt_to_po_purchase.supplier_id;
		fsProdJobCostPurchases.jcp_is_posted = 1;
		fsProdJobCostPurchases.porecd_id = rReceiptDetailQty.porecd_id;

		//Create Cost Transaction based on Status
		fsProdJobCost.prod_job_cost_to_prod_job_cost_transactions.newRecord(false, true);
		fsProdJobCost.prod_job_cost_to_prod_job_cost_transactions.porecd_id = rReceiptDetailQty.po_receipt_detail_qty_to_po_receipt_detail.porecd_id;

		var bAreOrderLineInvoicesPosted = utils.hasRecords(rJob.prod_job_to_sa_order_revision_detail) 
			&& utils.hasRecords(rJob.prod_job_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_ship_detail) 
			&& utils.hasRecords(rJob.prod_job_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_ship_detail.sa_ship_detail_to_sa_ship) 
			&& rJob.prod_job_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_ship_detail.sa_ship_detail_to_sa_ship.ship_fully_invoiced == 1 
			&& scopes.avInv.isOrderLineInvoicesPosted(rJob.prod_job_to_sa_order_revision_detail.getRecord(1));

		if (rJob.jobstat_id == scopes.avUtils.JOB_STATUS.Completed) {

			if (bAreOrderLineInvoicesPosted) { //All invoices must be posted

				fsProdJobCost.prod_job_cost_to_prod_job_cost_transactions.jct_transaction_type = scopes.avUtils.JOB_COST_TRANSACTION_TYPE.CostOfGoodsSold;
				if (fsProdJobCost.cogs_date == null) {
					fsProdJobCost.cogs_date = application.getTimeStamp();
					
					// If creating cost of goods record and there is no finished goods date, force a finished goods date so another cost of goods sold record can't be created.
					if (fsProdJobCost.finished_goods_date == null) {
						fsProdJobCost.finished_goods_date = fsProdJobCost.cogs_date;
					}
				}
			}
			else {
				fsProdJobCost.prod_job_cost_to_prod_job_cost_transactions.jct_transaction_type = scopes.avUtils.JOB_COST_TRANSACTION_TYPE.FinishedGoods;
				if (fsProdJobCost.finished_goods_date == null) {
					fsProdJobCost.finished_goods_date = application.getTimeStamp();
				}
			}
		}
		else {
			if (bAreOrderLineInvoicesPosted) { //All invoices must be posted
			
				fsProdJobCost.prod_job_cost_to_prod_job_cost_transactions.jct_transaction_type = scopes.avUtils.JOB_COST_TRANSACTION_TYPE.CostOfGoodsSold;
				if (fsProdJobCost.cogs_date == null) {
					fsProdJobCost.cogs_date = application.getTimeStamp();
					
					// If creating cost of goods record and there is no finished goods date, force a finished goods date so another cost of goods sold record can't be created.
					if (fsProdJobCost.finished_goods_date == null) {
						fsProdJobCost.finished_goods_date = fsProdJobCost.cogs_date;
					}
				}
			}
			else {
				fsProdJobCost.prod_job_cost_to_prod_job_cost_transactions.jct_transaction_type = scopes.avUtils.JOB_COST_TRANSACTION_TYPE.WorkInProgress;
			}
		}
	}
}

/**
 * Process the transaction used as reference in Inventory Receipt
 * @param {JSFoundSet<db:/avanti/in_item_trans_header>} _fsTransHeader
 * @param {JSRecord<db:/avanti/in_item_trans_detail>} rTransDetail
 *
 * @properties={typeid:24,uuid:"9833248D-22AB-436E-B60F-5CAD31A9BF17"}
 */
function updateTransDetailForIR(_fsTransHeader, rTransDetail) {
	var nAdjustmentQty = 0;
	var sTransReference;
	if (_fsTransHeader.itemtransh_reference_wto != null) {
		sTransReference = _fsTransHeader.itemtransh_reference_wto;
	}
	else if (_fsTransHeader.itemtransh_reference_pr != null) {
		sTransReference = _fsTransHeader.itemtransh_reference_pr;
	}

	/***@type {JSRecord<db:/avanti/in_trans_entry_header>} ***/
	var rTransactionHeader = scopes.avDB.getRec("in_trans_entry_header", ["intraneh_id"], [sTransReference]);

	if (!rTransactionHeader || rTransactionHeader.intraneh_complete == 1) {
		return;
	}
	/***@type {JSFoundSet<db:/avanti/in_trans_entry_detail>} ***/
	var rTransEntryDetail = scopes.avDB.getRec("in_trans_entry_detail", ['intraneh_id', 'item_id'], [rTransactionHeader.intraneh_id, rTransDetail.item_id]);
	if (rTransEntryDetail && rTransEntryDetail.intraned_balance > 0) {

		//SL-14038 only update for pending receipt
		if (_fsTransHeader.itemtransh_reference_pr) {
			if (rTransEntryDetail.intraned_trans_qty - rTransEntryDetail.intraned_received_to_date - rTransDetail.itemtransd_qty <= rTransEntryDetail.intraned_trans_qty) {
				if (rTransEntryDetail.intraned_trans_qty - rTransEntryDetail.intraned_received_to_date - rTransDetail.itemtransd_qty > 0) {
					nAdjustmentQty = rTransDetail.itemtransd_qty;
				}
				else {
					nAdjustmentQty = rTransEntryDetail.intraned_trans_qty - rTransEntryDetail.intraned_received_to_date;
				}
			}
			else {
				nAdjustmentQty = rTransEntryDetail.intraned_trans_qty - rTransEntryDetail.intraned_received_to_date;
				if (nAdjustmentQty < 0) {
					nAdjustmentQty = 0;
				}
			}

			if (nAdjustmentQty != 0) {
				globals.avBase_Inventory_UpdateItemQuantities(rTransDetail.item_id, rTransDetail.whse_id, null, 0, 0, 0, nAdjustmentQty * -1, 0, null);
			}
		}
		rTransEntryDetail.intraned_received_to_date += rTransDetail.itemtransd_qty;
		rTransEntryDetail.intraned_balance = rTransEntryDetail.intraned_trans_qty - rTransEntryDetail.intraned_received_to_date;
		if (rTransEntryDetail.intraned_balance < 0) {
			rTransEntryDetail.intraned_balance = 0;
		}

	}

	databaseManager.saveData(rTransEntryDetail);

	//Check if completly received.
	var bCompleted = true;
	for (var i = 1; i <= rTransactionHeader.in_trans_entry_header_to_in_trans_entry_detail.getSize(); i++) {
		rTransEntryDetail = rTransactionHeader.in_trans_entry_header_to_in_trans_entry_detail.getRecord(i);
		if (rTransEntryDetail.intraned_balance > 0) {
			bCompleted = false;
			break;
		}
	}

	if (bCompleted) {
		rTransactionHeader.intraneh_complete = 1;
		databaseManager.saveData(rTransactionHeader);
	}
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/in_item>} rItem
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"56945C4D-8808-42E5-986F-EF4DA65BB871"}
 */
function doesItemUseExcludeInShipping(rItem) {
	if (utils.hasRecords(rItem.in_item_to_in_item_class) && rItem.in_item_to_in_item_class.itemclass_exclude_in_shipping) {
		return true;
	}
	else {
		return false;
	}
}

/**
 * Get default warehouse bin location
 *
 * <AUTHOR> Dol
 * @since Sep 26, 2018
 * @param {UUID} uWhseID - Warehouse UUID
 * @returns {JSRecord<db:/avanti/in_warehouse_location>} Warehouse Location Record
 * @public
 *
 * @properties={typeid:24,uuid:"9C4523C1-28AD-4000-BA4D-8AFBB909C747"}
 */
function getDefaultWarehouseBinLocation(uWhseID) {

	/**@type {JSRecord<db:/avanti/in_warehouse_location>} */
	var rWarehouseLocation;

	/**@type {JSRecord<db:/avanti/in_warehouse>} */
	var rWarehouse = scopes.avDB.getRec("in_warehouse", ["whse_id"], [uWhseID]);

	if (rWarehouse) {
		if (rWarehouse.whse_enable_bin_locations) {
			if (utils.hasRecords(rWarehouse.in_warehouse_to_in_warehouse_location$warehouse_default_location)) {
				rWarehouseLocation = rWarehouse.in_warehouse_to_in_warehouse_location$warehouse_default_location.getRecord(1);
			}
			else {
				//need to create one.
				rWarehouseLocation = rWarehouse.in_warehouse_to_in_warehouse_location$warehouse_default_location.getRecord(rWarehouse.in_warehouse_to_in_warehouse_location$warehouse_default_location.newRecord());
				rWarehouseLocation.whseloc_active = 1;
				databaseManager.saveData(rWarehouseLocation);
			}
		}
	}

	return rWarehouseLocation;
}

/**
 * @param intranstype_source
 * @param intranstype_trans_code
 *
 * @return
 * @properties={typeid:24,uuid:"565C26CE-898F-4C1C-87EB-1B7C27940850"}
 */
function getTransactionTypeUUID(intranstype_source, intranstype_trans_code) {
	var args = [intranstype_source, intranstype_trans_code, scopes.globals.org_id];
	var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, "SELECT distinct intranstype_id FROM in_transaction_type \
        WHERE intranstype_source=? AND intranstype_trans_code=? AND intranstype_system_record = 1 AND intranstype_active = 1 AND (intranstype_appkey_uuid IS NOT NULL) AND org_id =?"
		, args, 1);

	if (ds.getMaxRowIndex() > 0) {
		return ds.getValue(1, 1);
	}
	return null;
}

/**
 * BuildTempTransactionFile
 *
 * @param {JSRecord<db:/avanti/in_trans_entry_header>} rTransEntryHeader - The transaction entry header record to update
 * @param {Boolean} [bAutoGenerateInventoryLabel]
 * @param {Boolean} [bComingFromImport]
 *
 * @return
 * @properties={typeid:24,uuid:"1EC9D0EF-CD87-4C33-9D36-23D3E44A5198"}
 */
function buildTempTransactionTables(rTransEntryHeader, bAutoGenerateInventoryLabel, bComingFromImport) {
	if (!rTransEntryHeader instanceof JSRecord) {
		throw new Error('Invalid Record Type');
	}

	if (!rTransEntryHeader || !utils.hasRecords(rTransEntryHeader.in_trans_entry_header_to_in_transaction_type)) {
		return false;
	}

	var sTransCode = rTransEntryHeader.in_trans_entry_header_to_in_transaction_type.intranstype_trans_code;

	var _fsTmpTransHeader = buildTmpTransHeader(rTransEntryHeader);
	var _fsTmpTransDetail = buildTmpTransDetail(rTransEntryHeader, _fsTmpTransHeader, sTransCode);

	databaseManager.saveData();
	var bSuccess = false;

	if (sTransCode == TRANSACTION_TYPE.PendingReceipts) {
		bSuccess = globals.avInventory_UpdateTransaction_PendingReceipts(globals["UUIDtoString"](rTransEntryHeader.intraneh_id));
	}
	else {
		bSuccess = globals.avInventory_UpdateTransaction(globals["UUIDtoString"](rTransEntryHeader.intraneh_id), bComingFromImport, null, null, null, _fsTmpTransHeader, _fsTmpTransDetail);
	}

	if (bSuccess) {
		//Update Header Record
		rTransEntryHeader.intraneh_status_field = 'U';
		rTransEntryHeader.intraneh_status = translateStatusFilter('U');
		databaseManager.saveData();

		if ((sTransCode == TRANSACTION_TYPE.InventoryReceipt || sTransCode == TRANSACTION_TYPE.RollBinTransfer)  
				&& bAutoGenerateInventoryLabel) {
			scopes.avInv.autoGenerateInventoryLabel('INV', rTransEntryHeader.intraneh_id);
		}
	}
	else {
		// delete temp records.
		_fsTmpTransHeader.deleteRecord();
	}

	return bSuccess;
}

/**
 * Builds the TmpTransEntryDetail records, the caller must save Data after.
 * @param {JSRecord<db:/avanti/in_trans_entry_header>} rTransEntryHeader
 * @param {JSFoundSet<db:/avanti/tmp_item_trans_header>} _fsTmpTransHeader
 * @param {String} sTransCode
 *
 * @return {JSFoundSet<db:/avanti/tmp_item_trans_detail>}
 *
 * @properties={typeid:24,uuid:"BD77C57C-7FBE-45DF-8BF9-865C2B033573"}
 */
function buildTmpTransDetail(rTransEntryHeader, _fsTmpTransHeader, sTransCode) {
	/** @type {JSFoundSet<db:/avanti/tmp_item_trans_detail>} */
	var _fsTmpTransDetail = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'tmp_item_trans_detail');
	/** @type {JSRecord<db:/avanti/in_trans_entry_detail>} */
	var _rec;
	rTransEntryHeader.in_trans_entry_header_to_in_trans_entry_detail.sort("sequence_nr asc");
	
	for (var i = 1; i <= rTransEntryHeader.in_trans_entry_header_to_in_trans_entry_detail.getSize(); i++) {
		_rec = rTransEntryHeader.in_trans_entry_header_to_in_trans_entry_detail.getRecord(i);
		_fsTmpTransDetail.newRecord(false);

		// if roll receipt - mark roll as posted

		if ( (sTransCode == TRANSACTION_TYPE.RollReceipts || sTransCode == TRANSACTION_TYPE.RollConversion || sTransCode == TRANSACTION_TYPE.RollBinTransfer) && utils.hasRecords(_rec.in_trans_entry_detail_to_in_item_roll)) {

			_rec.in_trans_entry_detail_to_in_item_roll.initemroll_trans_posted = 1;
			_fsTmpTransDetail.initemroll_id = _rec.in_trans_entry_detail_to_in_item_roll.initemroll_id;
		}
		else if (sTransCode == TRANSACTION_TYPE.RollConversion && _rec.intraned_trans_qty < 0) {
			// parent roll we have to find another way to get roll id
			_fsTmpTransDetail.initemroll_id = rTransEntryHeader.in_trans_entry_header_to_in_roll_conversion.in_roll_conversion_to_in_item_roll.initemroll_id;
		}
		else if (sTransCode == TRANSACTION_TYPE.QuantityAdjustment) {
			_fsTmpTransDetail.initemroll_id = _rec.initemroll_id;
		}
		else if (sTransCode == TRANSACTION_TYPE.RollBinTransfer) {
			_fsTmpTransDetail.initemroll_id = _rec.initemroll_id;
		}

		_fsTmpTransDetail.itemtransd_id = _rec.intraned_id;
		_fsTmpTransDetail.itemtransh_id = _rec.intraneh_id;
		_fsTmpTransDetail.item_id = _rec.item_id;
		_fsTmpTransDetail.uom_id = _rec.uom_id;
		_fsTmpTransDetail.itemtransd_qty = _rec.intraned_trans_qty;
		_fsTmpTransDetail.custproj_desc = _rec.custproj_desc;

		_fsTmpTransDetail.itemtransd_base_cost_adj = _rec.in_trans_entry_detail_to_in_item_warehouse.itemwhse_avg_cost; // Store the current cost for calculating cost adjustments.

		if (_fsTmpTransHeader.tmp_item_trans_header_to_in_transaction_type.intranstype_adjustment_type == 'C') {
			_fsTmpTransDetail.itemtransd_cost = _rec.intraned_cost_amt - _fsTmpTransDetail.itemtransd_base_cost_adj; // Only want to adjust difference in cost.
			_fsTmpTransDetail.itemtransd_qty = _rec.in_trans_entry_detail_to_in_item_warehouse.itemwhse_onhand_qty; // grab the current on hand in case it has changed since the transaction was entered

			_rec.intraned_trans_qty = _fsTmpTransDetail.itemtransd_qty; // update the entry value.
			_rec.intraned_cost_amt = _fsTmpTransDetail.itemtransd_cost;
			_rec.intraned_extended_cost = _rec.intraned_trans_qty * _rec.intraned_cost_amt;
		}
		else {
			_fsTmpTransDetail.itemtransd_cost = _rec.intraned_cost_amt;
		}

		_fsTmpTransDetail.itemtransd_ext = _fsTmpTransDetail.itemtransd_qty * _fsTmpTransDetail.itemtransd_cost;
		_fsTmpTransDetail.whse_id = _rec.intraned_whse_id;
		_fsTmpTransDetail.itemtransd_whseloc_id = _rec.intraned_whseloc_id;
		_fsTmpTransDetail.itemwhseloc_id = _rec.intraned_itemwhseloc_id;
		_fsTmpTransDetail.itemtransd_from_whse_id = _rec.intraned_whse_id_transfer_from;
		_fsTmpTransDetail.itemtransd_whseloc_id_from = _rec.intraned_whseloc_id_from;
		_fsTmpTransDetail.itemtransd_from_itemwhseloc_id = _rec.intraned_itemwhseloc_id_from;
		_fsTmpTransDetail.itemtransd_cost_uom_id = _rec.intraned_cost_uom_id;
		_fsTmpTransDetail.itemtransd_cost_uom_factor = _rec.intraned_cost_uom_cost_factor;
		_fsTmpTransDetail.itemtransd_comment = _rec.intraned_comment;
		_fsTmpTransDetail.itemtransd_cost_entered = _rec.intraned_cost_entered;
		_fsTmpTransDetail.itemwhse_id = _rec.itemwhse_id;
		_fsTmpTransDetail.glacct_id = _rec.glacct_id;
		_fsTmpTransDetail.jcm_id = _rec.jcm_id;
		
		if(_rec.adjtype_id) {
		    _fsTmpTransDetail.adjtype_id = _rec.adjtype_id;
        }
		
		if (_fsTmpTransDetail.itemwhse_id) {

			if (_fsTmpTransDetail.itemtransd_whseloc_id != null && _fsTmpTransDetail.itemwhseloc_id == null) {
				_fsTmpTransDetail.itemwhseloc_id = _rec.in_trans_entry_detail_to_in_item_warehouse_location.itemwhseloc_id;
			}

			if (_fsTmpTransDetail.itemtransd_whseloc_id_from != null && _fsTmpTransDetail.itemtransd_from_itemwhseloc_id == null) {
				_fsTmpTransDetail.itemtransd_from_itemwhseloc_id = _rec.in_trans_entry_detail_to_in_item_warehouse_location$from.itemwhseloc_id;
			}
		}
	}
	
	return _fsTmpTransDetail;
}

/**
 * Creates a TmpTransEntryHeader records and returns the foundset
 * @param {JSRecord<db:/avanti/in_trans_entry_header>} rTransEntryHeader
 *
 *
 * @return
 * @properties={typeid:24,uuid:"1D42F8C8-642A-464F-83C8-9E26EE2B5912"}
 */
function buildTmpTransHeader(rTransEntryHeader) {
	/** @type {JSFoundSet<db:/avanti/tmp_item_trans_header>} */
	var _fsTmpTransHeader = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'tmp_item_trans_header');

	rTransEntryHeader.intraneh_postedby_empl_id = globals.avBase_employeeUUID;
	rTransEntryHeader.intraneh_posted_date = application.getTimeStamp();

	_fsTmpTransHeader.newRecord();
	_fsTmpTransHeader.itemtransh_id = rTransEntryHeader.intraneh_id;
	_fsTmpTransHeader.itemtransh_transaction_no = rTransEntryHeader.intraneh_transaction_no;
	_fsTmpTransHeader.intranstype_id = rTransEntryHeader.intranstype_id;
	_fsTmpTransHeader.itemtransh_reference = rTransEntryHeader.intraneh_reference;
	_fsTmpTransHeader.itemtransh_reference_pr = rTransEntryHeader.intraneh_reference_pr;
	_fsTmpTransHeader.itemtransh_reference_wto = rTransEntryHeader.intraneh_reference_wto;
	_fsTmpTransHeader.itemtransh_date = application.getTimeStamp();
	_fsTmpTransHeader.empl_id = rTransEntryHeader.intraneh_createdby_empl_id;

	return _fsTmpTransHeader;
}

/**
 * @param {String} statusFilter
 *
 * @return
 * @properties={typeid:24,uuid:"DD43836E-DEB7-4956-9E2E-5C456045CD50"}
 */
function translateStatusFilter(statusFilter) {
	var ret = null;

	if (statusFilter == 'O') {
		ret = 0;
	}
	else if (statusFilter == 'U') {
		ret = 1;
	}
	if (statusFilter == 'P') {
		ret = 2;
	}

	return ret;
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/in_item>} rKit
 * @param {String|UUID} sKitItemID
 *
 * @return {JSRecord<db:/avanti/in_item_bill_of_material>}
 *
 * @properties={typeid:24,uuid:"54A0E4E8-C651-435E-8866-DC751CDBE217"}
 */
function getBillOfMaterialItem(rKit, sKitItemID) {
	if (rKit.in_item_to_in_item_bill_of_material) {
		for (var i = 1; i <= rKit.in_item_to_in_item_bill_of_material.getSize(); i++) {
			var rBOM = rKit.in_item_to_in_item_bill_of_material.getRecord(i);

			if (rBOM.itembom_rawmat_item_id == sKitItemID) {
				return rBOM;
			}
		}
	}

	return null;
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/sa_order_revision_detail>} rBTOLineItem
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"0BFA0175-66D7-4201-A61E-A3E86BF19C9B"}
 */
function doesBTOKitLineItemHaveAnyItemsThatUseBins(rBTOLineItem) {
	if (rBTOLineItem.sa_order_revision_detail_to_sa_order_revision_detail$children) {
		for (var i = 1; i <= rBTOLineItem.sa_order_revision_detail_to_sa_order_revision_detail$children.getSize(); i++) {
			var rKitItem = rBTOLineItem.sa_order_revision_detail_to_sa_order_revision_detail$children.getRecord(i);

			if (!utils.hasRecords(rKitItem.sa_order_revision_detail_to_in_warehouse) || !utils.hasRecords(rKitItem.sa_order_revision_detail_to_in_item)) {
				continue;
			}
			var rWare = rKitItem.sa_order_revision_detail_to_in_warehouse.getRecord(1);
			var rItem = rKitItem.sa_order_revision_detail_to_in_item.getRecord(1);

			// only items where ware uses bin, item uses bin and item not a product or service item (not kept in inventory)
			if (rWare.whse_enable_bin_locations && !rItem.item_no_bin_location && rItem.itemtype_code != 'P' && rItem.itemtype_code != 'SE') {
				return true;
			}
		}
	}

	return false;
}

/**
 * @properties={typeid:24,uuid:"A94BE1F6-F27C-4389-8F8A-2B7CE71A3277"}
 */
function setOffsetTransTypeValueLists() {
	var vlRealValues = null;
	var vlDisplayValues = null;
	var sSql = "SELECT intranstype_id,intranstype_desc \
                FROM in_transaction_type \
                WHERE intranstype_include_valuelist = 1 \
                AND intranstype_trans_code NOT IN ('BTO', 'RBTO', 'PR')\
                AND intranstype_source = ? AND org_id = ? order by 2";

	var _args = new Array();
	_args[0] = 'IT';
	_args[1] = globals.org_id;

	var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, _args, -1);

	vlRealValues = ds.getColumnAsArray(1);
	vlDisplayValues = ds.getColumnAsArray(2);

	application.setValueListItems('avInventoryOffsetTransactionTypes', vlDisplayValues, vlRealValues);
}

/**
 * get item decimal places cost
 *
 * @param {UUID} uItemId
 * @returns {Number}
 * @public
 *
 * @properties={typeid:24,uuid:"B9AD749A-22BE-426A-A673-9F3817995B26"}
 */
function getItemDecimalPlacesCost(uItemId) {
	var nDecimalPlaces = 2;

	if (uItemId) {
		/** @type {JSRecord<db:/avanti/in_item>} */
		var rItem = scopes.avDB.getRec("in_item", ["item_id"], [uItemId]);

		if (rItem && utils.hasRecords(rItem.in_item_to_sys_unit_of_measure_stocking_uom)) {
			nDecimalPlaces = rItem.in_item_to_sys_unit_of_measure_stocking_uom.uom_decimals_cost;
		}
	}

	return nDecimalPlaces;
}
/**
 * @param {String} whseCode
 * @return
 * @properties={typeid:24,uuid:"6982BAFC-D632-41EA-8299-385BAD8A78EE"}
 */
function getWhseId(whseCode) {
	if (!whseCode) {
		return null;
	}
	var sSql = "SELECT whse_id FROM in_warehouse WHERE org_id = ?";
	//Regex pattern for UUID
	var re = new RegExp("[A-Fa-f0-9]{8}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{12}");

	//If it matches the UUID pattern, we will assume it is a whse_id and we will check based on that
	if (re.test(whseCode)) {
		sSql += " AND whse_id = ?";
	}
	else {
		sSql += " AND whse_code = ?";
	}
	var args = [scopes.globals.org_id, whseCode];

	var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, 1);

	if (ds.getMaxRowIndex() > 0) {
		return ds.getValue(1, 1);
	}
	return null;
}

/**
 * @param nAvailableQty
 * @param nConvFactor
 * @param nDecimalPlaces
 *
 * @return
 * @properties={typeid:24,uuid:"A917083C-81E0-4766-9D07-6A632A5603AE"}
 */
function getFlushableQtyBasedOnUom(nAvailableQty, nConvFactor, nDecimalPlaces) {
	if (nConvFactor == null || nConvFactor == 0) {
		nConvFactor = 1;
	}

	var _qty = nAvailableQty / nConvFactor;

	if (nDecimalPlaces != null) {
		if (nDecimalPlaces == 0) {
			if (_qty < 1) {
				return 0;
			}
			else {
				return Math.floor(_qty);
			}
		}
		var _multiplier = Math.pow(10, nDecimalPlaces);

		var nReturn = Math.round(_qty * _multiplier) / _multiplier;
	}

	return nReturn;
}

/**
 * @param {JSFoundset<db:/avanti/sa_order_revd_item>} fsRevDetailItem - The sa_order_revd_item record to update
 * 
 * @return {{nFlushed:Number, nSkipped:Number}}
 *
 * @properties={typeid:24,uuid:"A5D73343-E39B-4C8E-9C55-7ABD388A8923"}
 */
function flushBackorder(fsRevDetailItem) {
	var oReturn = {
		nFlushed: 0,
		nSkipped: 0
	};
	var nResult = 0;
	var nSkipped = 0;
	/***@type {Array<{String,Number}>}*/
	var mQtyWhseMap = [];
	/***@type {Array<{String,Number}>}*/
	var mQtyWhseProjectMap = [];
	/***@type {Number}*/
	var nAvailableQty = 0.00;

	for (var i = 1; i <= fsRevDetailItem.getSize(); i++) {
		var rRevDetailItem = fsRevDetailItem.getRecord(i);

		if (!rRevDetailItem.ordrevditem_selected) {
			continue;
		}

		var bUseProjectInventory = scopes.avInv.doesItemUseProjectInventory(rRevDetailItem.item_id);
		var uOrderID = rRevDetailItem.sa_order_revd_item_to_sa_order_revision_header.ordh_id;
		var sProject = bUseProjectInventory ? scopes.avSales.getOrderProject(null, uOrderID) : null;
		var sKey = null;

		if (sProject) {
			sKey = rRevDetailItem.ordrevditem_whse_id + rRevDetailItem.item_id + sProject;

			if (mQtyWhseProjectMap[sKey] != null) {
				nAvailableQty = mQtyWhseProjectMap[sKey];
			}
			else {
				//We need to check for warehouse flushable qty before flushing again, in case something changed by some other user
				if (utils.hasRecords(rRevDetailItem.sa_order_revd_item_to_in_item_warehouse)) {
					nAvailableQty = getFlushQtyForWhseProject(rRevDetailItem.ordrevditem_whse_id, rRevDetailItem.item_id, sProject);
				}
			}
		}
		else {
			sKey = rRevDetailItem.ordrevditem_whse_id + rRevDetailItem.item_id;

			if (mQtyWhseMap[sKey] != null) {
				nAvailableQty = mQtyWhseMap[sKey];
			}
			// if bUseProjectInventory on then need to call getFlushQtyForWhseProject without project to get non-project qty
			else if (bUseProjectInventory) {
				nAvailableQty = getFlushQtyForWhseProject(rRevDetailItem.ordrevditem_whse_id, rRevDetailItem.item_id);
			}
			else {
				//We need to check for warehouse flushable qty before flushing again, in case something changed by some other user
				if (utils.hasRecords(rRevDetailItem.sa_order_revd_item_to_in_item_warehouse)) {
					var rItemWhse = rRevDetailItem.sa_order_revd_item_to_in_item_warehouse.getRecord(1);
					nAvailableQty = rItemWhse.itemwhse_onhand_qty - rItemWhse.itemwhse_reserved_qty - rItemWhse.itemwhse_unavailable_qty;
				}
			}
		}

		if (nAvailableQty < rRevDetailItem.ordrevditem_flushqty_uomstk) {
			nSkipped++;
			continue;

		}
		try {
			scopes.avDB.waitAndAcquireLock(fsRevDetailItem, i, scopes.avDB.FOUNDSET_LOCKS.SaOrderRevdItemLock, 'flushBackorder_' + rRevDetailItem.ordrevditem_id.toString());


			if (sProject) {
				mQtyWhseProjectMap[sKey] = nAvailableQty - rRevDetailItem.ordrevditem_flushqty_uomstk;
			}
			else {
				mQtyWhseMap[sKey] = nAvailableQty - rRevDetailItem.ordrevditem_flushqty_uomstk;
			}

			rRevDetailItem.ordrevditem_backorder_qty -= rRevDetailItem.ordrevditem_flush_qty;
			rRevDetailItem.ordrevditem_reserved_qty += rRevDetailItem.ordrevditem_flush_qty;
			
			databaseManager.saveData(rRevDetailItem);
			databaseManager.releaseAllLocks(scopes.avDB.FOUNDSET_LOCKS.SaOrderRevdItemLock);
		} catch (e) {
			databaseManager.releaseAllLocks(scopes.avDB.FOUNDSET_LOCKS.SaOrderRevdItemLock);
			throw e;
		}
		

		if (utils.hasRecords(rRevDetailItem.sa_order_revd_item_to_sa_order_revision_detail) 
				&& rRevDetailItem.item_id == rRevDetailItem.sa_order_revd_item_to_sa_order_revision_detail.item_id) {
			var rLineItem = rRevDetailItem.sa_order_revd_item_to_sa_order_revision_detail.getRecord(1);
			
			try {
				scopes.avDB.waitAndAcquireLock(rLineItem.foundset, 1, scopes.avDB.FOUNDSET_LOCKS.SaOrderRevDetailLock, 'flushBackorder_'+rLineItem.ordrevd_id.toString());	

				rLineItem.ordrevd_qty_backord -= rRevDetailItem.ordrevditem_flush_qty;
				rLineItem.ordrevd_qty_reserved += rRevDetailItem.ordrevditem_flush_qty;
	            
				// sl-28632 - reserved qty should never be > order qty 
				if (rLineItem.ordrevd_qty_reserved > rLineItem.ordrevd_qty_ordered) {
					rLineItem.ordrevd_qty_reserved = rLineItem.ordrevd_qty_ordered;
				}
				databaseManager.saveData(rLineItem);
		        databaseManager.releaseAllLocks(scopes.avDB.FOUNDSET_LOCKS.SaOrderRevDetailLock);
				if (rRevDetailItem.sa_order_revd_item_to_in_item.itemtype_code == 'F') {
					updateFifoReservedForProject(rRevDetailItem.item_id, rRevDetailItem.ordrevditem_whse_id, sProject, rRevDetailItem.ordrevditem_flush_qty);
				}
			} catch (e) {
		        databaseManager.releaseAllLocks(scopes.avDB.FOUNDSET_LOCKS.SaOrderRevDetailLock);
		        throw e;
			}
			

			var fsShipDets = getShipDetsForCurrentRevision(rLineItem);
			
			if (fsShipDets && utils.hasRecords(fsShipDets)) {
				var nFlushQty = rRevDetailItem.ordrevditem_flush_qty;
				
				fsShipDets.sort("shipd_qty_backord asc");				
				
				for (var s = 1; s <= fsShipDets.getSize(); s++) {
					var rShipDetail = fsShipDets.getRecord(s);
					
					if (rShipDetail.shipd_qty_backord > 0) {
						var nThisFlushQty;
						
						if (rShipDetail.shipd_qty_backord < nFlushQty) {
							nThisFlushQty = rShipDetail.shipd_qty_backord;
						}
						else {
							nThisFlushQty = nFlushQty;
						}
						
						//because now sales order has some reserved but it will not show that when creating shipment
						rShipDetail.shipd_qty_shipped += nThisFlushQty;

						//Need to increase the original as well as that is used when cancelling shipment to set the reserved on line item on sales order
						rShipDetail.shipd_orig_qty_shipped += nThisFlushQty;

						// sl-17284 - reduce ship det bo qty too
						rShipDetail.shipd_qty_backord -= nThisFlushQty;
						rShipDetail.shipd_orig_qty_backord -= nThisFlushQty;
						
						nFlushQty -= nThisFlushQty;
						
						if (nFlushQty <= 0) {
							break;
						}
					}
				}
			}
		}

		nResult++;

		if (rRevDetailItem.sa_order_revd_item_to_sa_order_revision_header.ordrevh_order_status == 'Released') {
			ResetPlannedPurchasingBackorder(rRevDetailItem);
		}
	}

	databaseManager.saveData();

	oReturn.nFlushed = nResult;
	oReturn.nSkipped = nSkipped;

	return oReturn;
}

/**
 * @param {JSFoundset<db:/avanti/sa_order_revd_item>} fsRevDetailItem - The sa_order_revd_item record to update
 * 
 * @return {{nFlushed:Number, nSkipped:Number}}
 *
 * @properties={typeid:24,uuid:"01FECB82-**************-728A1FD7A9A4"}
 */
function flushReserved(fsRevDetailItem) {
	var oReturn = {
		nFlushed: 0,
		nSkipped: 0
	};
	var nResult = 0;
	var nSkipped = 0;
	/***@type {Array<{String,Number}>}*/
	var mQtyWhseMap = [];
	/***@type {Array<{String,Number}>}*/
	var mQtyWhseProjectMap = [];
	/***@type {Number}*/
	var nAvailableQty = 0.00;

	for (var i = 1; i <= fsRevDetailItem.getSize(); i++) {
		var rRevDetailItem = fsRevDetailItem.getRecord(i);

		if (!rRevDetailItem.ordrevditem_selected) {
			continue;
		}

		var bUseProjectInventory = scopes.avInv.doesItemUseProjectInventory(rRevDetailItem.item_id);
		var uOrderID = rRevDetailItem.sa_order_revd_item_to_sa_order_revision_header.ordh_id;
		var sProject = bUseProjectInventory ? scopes.avSales.getOrderProject(null, uOrderID) : null;
		var sKey = null;

		if (sProject) {
			sKey = rRevDetailItem.ordrevditem_whse_id + rRevDetailItem.item_id + sProject;

			if (mQtyWhseProjectMap[sKey] != null) {
				nAvailableQty = mQtyWhseProjectMap[sKey];
			}
			else {
				//We need to check for warehouse flushable qty before flushing again, in case something changed by some other user
				if (utils.hasRecords(rRevDetailItem.sa_order_revd_item_to_in_item_warehouse)) {
					nAvailableQty = getFlushQtyForWhseProject(rRevDetailItem.ordrevditem_whse_id, rRevDetailItem.item_id, sProject);
				}
			}
		}
		else {
			sKey = rRevDetailItem.ordrevditem_whse_id + rRevDetailItem.item_id;

			if (mQtyWhseMap[sKey] != null) {
				nAvailableQty = mQtyWhseMap[sKey];
			}
			// if bUseProjectInventory on then need to call getFlushQtyForWhseProject without project to get non-project qty
			else if (bUseProjectInventory) {
				nAvailableQty = getFlushQtyForWhseProject(rRevDetailItem.ordrevditem_whse_id, rRevDetailItem.item_id);
			}
			else {
				//We need to check for warehouse flushable qty before flushing again, in case something changed by some other user
				if (utils.hasRecords(rRevDetailItem.sa_order_revd_item_to_in_item_warehouse)) {
					var rItemWhse = rRevDetailItem.sa_order_revd_item_to_in_item_warehouse.getRecord(1);
					nAvailableQty = rItemWhse.itemwhse_onhand_qty - rItemWhse.itemwhse_reserved_qty - rItemWhse.itemwhse_unavailable_qty;
				}
			}
		}

		if (nAvailableQty > rRevDetailItem.ordrevditem_flushqty_uomstk) {
			nSkipped++;
			continue;

		}

		if (sProject) {
			mQtyWhseProjectMap[sKey] = nAvailableQty - rRevDetailItem.ordrevditem_flushqty_uomstk;
		}
		else {
			mQtyWhseMap[sKey] = nAvailableQty - rRevDetailItem.ordrevditem_flushqty_uomstk;
		}
		try {
			scopes.avDB.waitAndAcquireLock(fsRevDetailItem, i, scopes.avDB.FOUNDSET_LOCKS.SaOrderRevdItemLock, 'flushReserved_' + rRevDetailItem.ordrevditem_id.toString());

			rRevDetailItem.ordrevditem_reserved_qty += rRevDetailItem.ordrevditem_flush_qty;
			rRevDetailItem.ordrevditem_backorder_qty -= rRevDetailItem.ordrevditem_flush_qty;
			
			databaseManager.saveData(rRevDetailItem);
			databaseManager.releaseAllLocks(scopes.avDB.FOUNDSET_LOCKS.SaOrderRevdItemLock);
		} catch (e) {
			databaseManager.releaseAllLocks(scopes.avDB.FOUNDSET_LOCKS.SaOrderRevdItemLock);
			throw e;
		}
		

		if (utils.hasRecords(rRevDetailItem.sa_order_revd_item_to_sa_order_revision_detail) 
				&& rRevDetailItem.item_id == rRevDetailItem.sa_order_revd_item_to_sa_order_revision_detail.item_id) {
			var rLineItem = rRevDetailItem.sa_order_revd_item_to_sa_order_revision_detail.getRecord(1);
			try {
				scopes.avDB.waitAndAcquireLock(rLineItem.foundset, 1, scopes.avDB.FOUNDSET_LOCKS.SaOrderRevDetailLock, 'flushReserved_'+rLineItem.ordrevd_id.toString());	

				rLineItem.ordrevd_qty_reserved += rRevDetailItem.ordrevditem_flush_qty;
				rLineItem.ordrevd_qty_backord -= rRevDetailItem.ordrevditem_flush_qty;
	            
				// sl-28632 - reserved qty should never be > order qty 
				if (rLineItem.ordrevd_qty_reserved > rLineItem.ordrevd_qty_ordered) {
					rLineItem.ordrevd_qty_reserved = rLineItem.ordrevd_qty_ordered;
				}
				
				databaseManager.saveData(rLineItem);
		        databaseManager.releaseAllLocks(scopes.avDB.FOUNDSET_LOCKS.SaOrderRevDetailLock);
				if (rRevDetailItem.sa_order_revd_item_to_in_item.itemtype_code == 'F') {
					updateFifoReservedForProject(rRevDetailItem.item_id, rRevDetailItem.ordrevditem_whse_id, sProject, rRevDetailItem.ordrevditem_flush_qty);
				}
			} catch (e) {
		        databaseManager.releaseAllLocks(scopes.avDB.FOUNDSET_LOCKS.SaOrderRevDetailLock);
				throw e;
			}
			

			var fsShipDets = getShipDetsForCurrentRevision(rLineItem);
			
			if (fsShipDets && utils.hasRecords(fsShipDets)) {
				var nFlushQty = rRevDetailItem.ordrevditem_flush_qty;
				var nFlushQtyABS = Math.abs(nFlushQty);
				
				fsShipDets.sort("shipd_qty_shipped asc");				
				
				for (var s = 1; s <= fsShipDets.getSize(); s++) {
					var rShipDetail = fsShipDets.getRecord(s);
					
					if (rShipDetail.shipd_qty_shipped > 0) {
						var nThisFlushQty;
						
						if (rShipDetail.shipd_qty_shipped < nFlushQtyABS) {
							nThisFlushQty = rShipDetail.shipd_qty_shipped;
						}
						else {
							nThisFlushQty = nFlushQtyABS;
						}
						
						//because now sales order has some reserved but it will not show that when creating shipment
						rShipDetail.shipd_qty_shipped -= nThisFlushQty;

						//Need to increase the original as well as that is used when cancelling shipment to set the reserved on line item on sales order
						rShipDetail.shipd_orig_qty_shipped -= nThisFlushQty;

						// sl-17284 - reduce ship det bo qty too
						rShipDetail.shipd_qty_backord += nThisFlushQty;
						rShipDetail.shipd_orig_qty_backord += nThisFlushQty;
						
						nFlushQtyABS -= nThisFlushQty;
						
						if (nFlushQtyABS <= 0) {
							break;
						}
					}
				}
			}
		}

		nResult++;

		if (rRevDetailItem.sa_order_revd_item_to_sa_order_revision_header.ordrevh_order_status == 'Released') {
			ResetPlannedPurchasingBackorder(rRevDetailItem);
		}
	}

	databaseManager.saveData();

	oReturn.nFlushed = nResult;
	oReturn.nSkipped = nSkipped;

	return oReturn;
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sa_order_revision_detail>} rLineItem
 * 
 * @return {JSFoundSet<db:/avanti/sa_ship_detail>}
 *
 * @properties={typeid:24,uuid:"C675233D-AC41-4CBB-9A21-612842D2D11E"}
 */
function getShipDetsForCurrentRevision(rLineItem) {
	/** @type {JSFoundSet<db:/avanti/sa_ship_detail>} */
	var fsShipDets = null;
	
	if (utils.hasRecords(rLineItem.sa_order_revision_detail_to_sa_ship_detail)) {
		var aShipsProcessed = [];
		var aGoodShipDets = [];

		for (var i = 1; i <= rLineItem.sa_order_revision_detail_to_sa_ship_detail.getSize(); i++) {
			var rSD = rLineItem.sa_order_revision_detail_to_sa_ship_detail.getRecord(i);
			var rShip = rSD.sa_ship_detail_to_sa_ship.getRecord(1);

			if (aShipsProcessed.indexOf(rShip.ship_id) == -1) {
				aShipsProcessed.push(rShip.ship_id);

				for (var j = 1; j <= rShip.sa_ship_to_sa_ship_detail$ship_revision.getSize(); j++) {
					var rSD2 = rShip.sa_ship_to_sa_ship_detail$ship_revision.getRecord(j);

					if (rSD2.ordrevd_id == rLineItem.ordrevd_id && rSD2.ship_revision == rShip.ship_revision) {
						aGoodShipDets.push(rSD2.shipd_id);
					}
				}
			}
		}
		
		if (aGoodShipDets.length) {
			var sGoodShipDets = scopes.avText.arrayToString(aGoodShipDets, ",", "'");
			
			fsShipDets = scopes.avDB.getFSFromSQL("SELECT SD.shipd_id FROM sa_ship_detail SD WHERE sd.shipd_id IN (" + sGoodShipDets + ")", "sa_ship_detail");
		}
	}
	
	return fsShipDets;
}

/**
 * @public
 *
 * @param {String|UUID} uWhseID
 * @param {UUID} uItemID
 * @param {String} [sProject] - if sProject is missing then it gets no-proj qty
 *
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"5C94A9F7-408C-43F6-B522-50A65C1C2C5B"}
 */
function getFlushQtyForWhseProject(uWhseID, uItemID, sProject) {
	/**@type {Number} */
	var nQty = null;

	if (uWhseID && uItemID) {
		var sSQL = "SELECT SUM(IIF(F.fifo_qty_remaining - F.fifo_qty_reserved - ISNULL(F.fifo_qty_reserved_project, 0) < 0, 0, F.fifo_qty_remaining - F.fifo_qty_reserved - ISNULL(F.fifo_qty_reserved_project, 0))) \
					FROM in_item_fifo F \
					INNER JOIN in_item_warehouse IW ON F.itemwhse_id = IW.itemwhse_id \
					WHERE \
						F.org_id = ? \
						AND F.item_id = ? \
						AND IW.whse_id = ?";
		var aArgs = [globals.org_id, uItemID.toString(), uWhseID.toString()];

		if (sProject) {
			sSQL += " AND F.custproj_desc = ? ";
			aArgs.push(sProject);
		}
		else {
			sSQL += " AND F.custproj_desc IS NULL ";
		}

		nQty = scopes.avDB.SQLQuery(sSQL, null, aArgs);
	}

	return nQty;
}

/**
 * Correct Planned Purchasing Backorder
 *
 * @param {JSRecord<db:/avanti/sa_order_revd_item>} rRevDetailItem - The sa_order_revd_item record to update
 *
 * @properties={typeid:24,uuid:"797A03D1-3438-4017-8E29-BF6B8C6C0182"}
 */
function ResetPlannedPurchasingBackorder(rRevDetailItem) {
	if (rRevDetailItem) {
		if (utils.hasRecords(rRevDetailItem.sa_order_revd_item_to_po_planned_master)) {
			if (rRevDetailItem.sa_order_revd_item_to_po_planned_master.poplanmaster_on_po == 0 && rRevDetailItem.sa_order_revd_item_to_po_planned_master.poplanmaster_on_pp == 0 && rRevDetailItem.sa_order_revd_item_to_po_planned_master.poplanmaster_qty != rRevDetailItem.ordrevditem_backorder_qty) {
				rRevDetailItem.sa_order_revd_item_to_po_planned_master.poplanmaster_qty = rRevDetailItem.ordrevditem_backorder_qty;

				if (rRevDetailItem.sa_order_revd_item_to_po_planned_master.poplanmaster_qty <= 0) {
					// GD - Mar 17, 2020: SL-18584 - Capture event in devLog(); trying to see how items on poplanmasters for orders are being inadvertently deleted
					scopes.avUtils.sDevLogFunctionSource = "avInv.js: ResetPlannedPurchasingBackorder";
					rRevDetailItem.sa_order_revd_item_to_po_planned_master.poplanmaster_deleted = 1; // Mark as deleted.
					rRevDetailItem.sa_order_revd_item_to_po_planned_master.modified_by_id = globals.avBase_employeeUUID;
					rRevDetailItem.sa_order_revd_item_to_po_planned_master.modified_date = application.getServerTimeStamp();
					scopes.avUtils.sDevLogFunctionSource = null;
				}
			}
		}
		else {
			//create the planned purchase?  Not at this time.
			if (rRevDetailItem.ordrevditem_backorder_qty > 0) {
				//write an audit log entry instead of creating a record for purchasing as we cannot be sure the customer hasn't purchased the item
				if (utils.hasRecords(rRevDetailItem.sa_order_revd_item_to_sa_order_revision_header) && rRevDetailItem.sa_order_revd_item_to_sa_order_revision_header.ordrevh_revision == 0 && rRevDetailItem.sa_order_revd_item_to_sa_order_revision_header.ordrevh_order_status == "Released") {
					var oAudit = {
						serverName: globals.avBase_dbase_avanti,
						tableName: "",
						fieldName: "",
						action: "1",
						source: "Quantity Rebuild - New Backorder",
						primaryKey: "",
						oldData: "",
						newData: "",
						documentType: "",
						documentNumber: "",
						org_id: "",
						programName: "Item Quantity Rebuild"
					};

					oAudit.tableName = "sa_order_revd_item"
					oAudit.primaryKey = rRevDetailItem.ordrevditem_id;
					oAudit.fieldName = "ordrevditem_backorder_qty";
					oAudit.oldData = null;
					oAudit.newData = rRevDetailItem.ordrevditem_backorder_qty;
					oAudit.org_id = rRevDetailItem.org_id;
					oAudit.documentType = "ORD"
					oAudit.documentNumber = rRevDetailItem.ordh_document_num;
					scopes.avDB.writeAuditLog(oAudit);
				}
			}
		}
	}
}

/**
 * @param bClear
 * @param {UUID} [emplIdTobeCleared]
 *
 * @properties={typeid:24,uuid:"EC42893F-B57D-4CC6-9E2A-F53A0BA3A1C9"}
 */
function updateUserIdOnRevdItem(bClear, emplIdTobeCleared) {

	/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	var oSQLu = new Object();

	var uSessionID = application.getUUID();

	var uUserId = null;

	if (!bClear) {
		uUserId = globals.avBase_employeeUUID;
	}
	var sWhere = " WHERE ordrevditem_id in (" + sOrdRevdItemIds + ")";
	oSQLu.args = [uUserId, globals.org_id, uSessionID.toString(), globals.avBase_employeeUUID];

	if (bClear) {
		sWhere = " WHERE user_id = ?";
		if (emplIdTobeCleared) {
			oSQLu.args.push(emplIdTobeCleared);
		}
		else {
			oSQLu.args.push(globals.avBase_employeeUUID);
		}
	}
	else if (!sOrdRevdItemIds || sOrdRevdItemIds.length == 0) {
		return;
	}

	oSQLu.sql = "UPDATE sa_order_revd_item SET user_id = ? \
                OUTPUT ?, 3 'servoy_broadcast_order', \
                'UPDATE_ACTION', INSERTED.ordrevditem_id, ?, \
                'sa_order_revd_item', ?, GetDate() 'created_date' \
                INTO sys_servoy_broadcast";
	oSQLu.sql += sWhere;
	oSQLu.table = "sa_order_revd_item";

	if (globals["avUtilities_sqlRaw"](oSQLu)) {
		scopes.avUtils.broadcastDataChanges(uSessionID.toString(), 'sa_order_revd_item');
	}
	if (bClear) {
		sOrdRevdItemIds = null;
	}
}

/**
 * @param {Boolean} bAlert
 * @param {UUID} [itemId]
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"6D0ACC13-6177-4655-B699-6F5ECD4F3C65"}
 */
function checkForOtherBOFlushUsers(bAlert, itemId) {

	/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	var oSQL = new Object();

	oSQL.sql = "SELECT DISTINCT ISNULL(empl_code + ' - ' + empl_full_name,''), ISNULL(sys_employee.user_id,0), owner_id, CASE WHEN  sys_employee.empl_id IS NULL THEN '' else sys_employee.empl_id END \
                FROM sa_order_revd_item \
                INNER JOIN sys_employee ON sys_employee.empl_id = sa_order_revd_item.user_id \
                INNER JOIN sys_organization ON sys_organization.org_id = sys_employee.org_id \
                WHERE sys_employee.org_id = ? AND sys_employee.empl_id <> ? ";

	oSQL.args = [globals.org_id, globals.avBase_employeeUUID];

	if (itemId) {
		oSQL.sql += " AND sa_order_revd_item.item_id = ?";
		oSQL.args.push(itemId.toString());
	}

	/***@type {JSDataSet<db:/avanti/sys_employee>} */
	var dsData = globals['avUtilities_sqlDataset'](oSQL);

	if (dsData && dsData.getMaxRowIndex() > 0) {

		//Show users who have records.
		var sMsg;
		if (itemId) {
			sMsg = scopes.avText.getDlgMsg('usersUsingBackorderFlush_Items') + '\n\n';
		}
		else {
			sMsg = scopes.avText.getDlgMsg('usersUsingBackorderFlush') + '\n\n';
		}
		var sEmps = null;
		var bEmpActive = false;
		//get the list of active users
		var clients = plugins.UserManager.getClients();
		for (var iE = 1; iE <= dsData.getMaxRowIndex(); iE++) {
			/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
			var oSQLF = new Object();

			oSQLF.sql = "SELECT user_name \
                         FROM sec_user \
                         WHERE owner_id = ? AND user_id = ? ";
			oSQLF.args = [dsData.getValue(iE, 3), dsData.getValue(iE, 2)];
			oSQLF.server = globals.avBase_dbase_framework;

			/***@type {JSDataSet<db:/svy_framework/sec_user>} */
			var dsDataF = globals['avUtilities_sqlDataset'](oSQLF);
			for (var iU = 1; iU <= dsDataF.getMaxRowIndex(); iU++) {
				for (var i = 0; i < clients.length; i++) {
					var client = clients[i];
					if (client.userName == dsDataF.getValue(iU, 1)) {
						bEmpActive = true;
						sEmps += dsData.getValue(iE, 1) + "\n";
						break;
					}
				}
			}

			if (!bEmpActive) {
				updateUserIdOnRevdItem(true, dsData.getValue(iE, 4));
			}
		}

		if (sEmps) {
			if (bAlert) {
				sMsg += sEmps;
				globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.backorderFlush'), sMsg, i18n.getI18NMessage("avanti.dialog.ok"));
			}
			return true;
		}
		else {
			return false;
		}
	}
	else {
		return false;
	}
}

/**
 * @param {UUID} sItemWhseID
 *
 * @return
 * @properties={typeid:24,uuid:"E634D011-572C-4852-BFC5-6A32D00E4226"}
 */
function getNextItemWarehouseLocationSequenceNr(sItemWhseID) {
	var sSQL = "SELECT MAX(sequence_nr) \
                FROM in_item_warehouse_location \
                WHERE \
                    org_id = ? \
                    AND itemwhse_id = ?";
	var aArgs = [globals.org_id, sItemWhseID.toString()];
	var nMaxSeqNr = scopes.avDB.SQLQuery(sSQL, null, aArgs);

	if (!nMaxSeqNr) {
		nMaxSeqNr = 0;
	}

	return nMaxSeqNr + 1;
}

/**
 * @param {UUID} sItemId
 * @return {JSFoundSet<db:/avanti/in_item>}
 *
 * @properties={typeid:24,uuid:"D4D9AF72-4AD4-4AAF-8749-A3B9405490F2"}
 */
function findItem(sItemId) {
	var sSQL = "SELECT item_id FROM in_Item WHERE org_id = ? AND item_id = ?";
	var aArgs = [globals.org_id, sItemId.toString()];
	/** @type {JSFoundSet<db:/avanti/in_item>} */
	var fs_item = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item');
	fs_item.loadRecords(sSQL, aArgs);
	return fs_item;
}

/**
 * @param {UUID} sItemId
 * @param {UUID} sItemLocId
 * @return {JSFoundSet<db:/avanti/in_item_warehouse_location>}
 *
 * @properties={typeid:24,uuid:"7943D3AA-E121-4B3D-B260-AE337A9A2320"}
 */
function findItemWhseLoc(sItemId, sItemLocId) {
	var sSQL = "SELECT itemwhseloc_id FROM in_item_warehouse_location WHERE org_id = ?";
	var aArgs = [globals.org_id];
	if (sItemId) {
		sSQL += " AND item_id = ?";
		aArgs.push(sItemId.toString());
	}
	if (sItemLocId) {
		sSQL += " AND itemwhseloc_id = ?";
		aArgs.push(sItemLocId.toString());
	}
	/** @type {JSFoundSet<db:/avanti/in_item_warehouse_location>} */
	var fsItemWhseLoc = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_warehouse_location');
	fsItemWhseLoc.loadRecords(sSQL, aArgs);
	return fsItemWhseLoc;
}

/**
 * @public
 *
 * @param {UUID} uItemID
 *
 * @return {String}
 *
 * @properties={typeid:24,uuid:"8172C541-E28B-4B43-948F-FEE9DB9FBA31"}
 */
function getItemClassTypeCode(uItemID) {
	var sSQL = "SELECT ict.itemclasstype_code \
				FROM in_item i \
				INNER JOIN in_item_class ic ON i.itemclass_id = ic.itemclass_id \
				INNER JOIN app_item_class_type ict ON ict.itemclasstype_id = ic.itemclass_type \
				WHERE \
					i.org_id = ? \
					AND i.item_id = ?";
	var aArgs = [globals.org_id, uItemID.toString()];

	/**@type {String} */
	var sItemClassTypeCode = scopes.avDB.SQLQuery(sSQL, null, aArgs);

	return sItemClassTypeCode;
}

/**
 * @public
 *
 * @param {UUID} uItemID
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"CADBB257-2FAA-4C1B-B65E-E737B0F0D4BE"}
 */
function isSheet(uItemID) {
	return getItemClassTypeCode(uItemID) == "PAPER";
}

/**
 * @public
 *
 * @param {UUID} uItemBinID
 *
 * @return {UUID}
 *
 * @properties={typeid:24,uuid:"EF9E2E8A-8212-4C9A-9EFB-967ECD8063C9"}
 */
function getBinIDFfromItemBinID(uItemBinID) {
	/**@type {UUID} */
	var uBinID = null;

	if (uItemBinID) {
		var sSQL = "SELECT whseloc_id \
					FROM in_item_warehouse_location \
					WHERE \
						org_id = ? \
						AND itemwhseloc_id = ?";
		var aArgs = [globals.org_id, uItemBinID.toString()];

		uBinID = scopes.avDB.SQLQuery(sSQL, null, aArgs);
	}

	return uBinID;
}

/**
 * @public
 *
 * @param {UUID} uItemWhseID
 * @param {String} [sProject] - if missing then it returns qty for non-project inventory
 *
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"8CF85817-4B26-48A0-9A9F-6BDAD38B52BE"}
 */
function getItemWarehouseProjectQtyAvailable(uItemWhseID, sProject) {
	var nQtyAvail = 0;

	if (uItemWhseID) {
		var sSQL = "SELECT SUM(fifo_qty_remaining - ISNULL(fifo_qty_reserved, 0) - ISNULL(fifo_qty_reserved_project, 0)) \
					FROM in_item_fifo f \
					WHERE \
						f.org_id = ? \
						AND f.itemwhse_id = ? ";
		var aArgs = [globals.org_id, uItemWhseID.toString()];

		if (sProject) {
			sSQL += " AND f.custproj_desc = ? ";
			aArgs.push(sProject);
		}
		else {
			sSQL += " AND f.custproj_desc IS NULL ";
		}

		nQtyAvail = scopes.avDB.SQLQuery(sSQL, null, aArgs);

		if (nQtyAvail == null || nQtyAvail < 0) {
			nQtyAvail = 0;
		}
	}

	return nQtyAvail;
}

/**@public
 *
 *
 * @param {UUID} uItemID
 * @param {String} sProject
 * @param {UUID} [uItemWhseID]
 *
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"92E2E7F0-BAF0-4EBA-B5F6-A2AA1234E592"}
 */
function getItemProjectOHQ(uItemID, sProject, uItemWhseID) {
	var nOHQ = 0;

	if (sProject && uItemID) {
		var sSQL = "SELECT SUM(fifo_qty_remaining) \
					FROM in_item_fifo f \
					WHERE \
						f.org_id = ? \
						AND f.custproj_desc = ? \
						AND f.item_id = ? ";
		var aArgs = [globals.org_id, sProject, uItemID.toString()];

		if (uItemWhseID) {
			sSQL += " AND f.itemwhse_id = ?";
			aArgs.push(uItemWhseID.toString());
		}

		nOHQ = scopes.avDB.SQLQuery(sSQL, null, aArgs);

		if (nOHQ == null) {
			nOHQ = 0;
		}
	}

	return nOHQ;
}

/**
 * @public
 *
 * @param {UUID} uItemWhseBinID
 * @param {String} [sProject] - if missing then it returns qty for non-project inventory
 *
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"7C6B259D-5E31-422E-8B3C-0C53CB6DB399"}
 */
function getItemWarehouseBinProjectQtyAvailable(uItemWhseBinID, sProject) {
	var nQtyAvail = 0;

	if (uItemWhseBinID) {
		var sSQL = "SELECT SUM(fifo_qty_remaining - ISNULL(fifo_qty_reserved, 0) - ISNULL(fifo_qty_reserved_project, 0)) \
					FROM in_item_fifo f \
					WHERE \
						f.org_id = ? \
						AND f.itemwhseloc_id = ?";
		var aArgs = [globals.org_id, uItemWhseBinID.toString()];

		if (sProject) {
			sSQL += " AND f.custproj_desc = ? ";
			aArgs.push(sProject);
		}
		else {
			sSQL += " AND f.custproj_desc IS NULL ";
		}

		nQtyAvail = scopes.avDB.SQLQuery(sSQL, null, aArgs);

		if (nQtyAvail == null || nQtyAvail < 0) {
			nQtyAvail = 0;
		}
	}

	return nQtyAvail;
}

/**
 * @public
 *
 * @param {UUID} uItemID
 * @param {UUID} uBinID
 *
 * @return {UUID}
 *
 * @properties={typeid:24,uuid:"BFE2E39C-4C10-43ED-B343-108B006BFD20"}
 */
function getItemBinIDFfromItemAndBinID(uItemID, uBinID) {
	/**@type {UUID} */
	var uItemBinID = null;

	if (uItemID && uBinID) {
		var sSQL = "SELECT itemwhseloc_id \
					FROM in_item_warehouse_location \
					WHERE \
						org_id = ? \
						AND item_id = ? \
						AND whseloc_id = ?";
		var aArgs = [globals.org_id, uItemID.toString(), uBinID.toString()];

		uItemBinID = scopes.avDB.SQLQuery(sSQL, null, aArgs);
	}

	return uItemBinID;
}

/**
 * Return the inventory adjustment control account for the specified warehouse
 *
 * @param {JSRecord<db:/avanti/in_warehouse>} rWarehouse
 * @returns {UUID} account UUID
 * @public
 *
 * @properties={typeid:24,uuid:"2C0127A6-6278-4A7F-B859-8997A64BA5EE"}
 */
function getInventoryAdjustmentControlAccount(rWarehouse) {
	if (rWarehouse && utils.hasRecords(rWarehouse.in_warehouse_to_sys_plant) && utils.hasRecords(rWarehouse.in_warehouse_to_sys_plant.sys_plant_to_sys_division)) {
		var rPlant = rWarehouse.in_warehouse_to_sys_plant.getRecord(1);
		var rDiv = rPlant.sys_plant_to_sys_division.getRecord(1);

		var sGLAccount = globals.avGL_getControlAccount('INVENTORY ADJUSTMENT', _to_sys_organization.sys_organization_to_sys_currency.curr_id);
		var oAccount = scopes.avAccounting.getGLAccount(sGLAccount, rDiv.div_id, rPlant.plant_id, rWarehouse.whse_id, null, null, null)
		return oAccount.glacct_id;
	}
	else {
		return null;
	}
}

/**
 * @public
 *
 * @param {UUID} uItemID
 * @param {UUID|String} uWareID
 * @param {String} sProject
 * @param {Number} nQty
 * @param {UUID} [uItemBinID]
 * @param {JSFoundSet<db:/avanti/in_item_fifo>} [fsPreviousFifo] - Optional existing FIFO foundset to reuse for cases when we call this function once to reduce and second time to update
 * @return {JSFoundSet<db:/avanti/in_item_fifo>} - Returns the FIFO foundset used for the update
 *
 * @properties={typeid:24,uuid:"F74E673B-311E-4D1B-91E3-7E5687153A31"}
 */
function updateFifoReservedForProject(uItemID, uWareID, sProject, nQty, uItemBinID, fsPreviousFifo) {
    if (uItemID && uWareID && (nQty > 0 || nQty < 0)) {
        var nQtyToReserve = 0;
        var nQtyToRestore = 0;
        
        /**@type {JSFoundset<db:/avanti/in_item_fifo>} */
        var fsFifo = fsPreviousFifo;
        
        if (!fsFifo) {
            var sSQL = "SELECT fifo_id \
                        FROM in_item_fifo f \
                        INNER JOIN in_item_warehouse iw ON iw.item_id = f.item_id \
                        WHERE \
                            f.org_id = ? \
                            AND f.item_id = ? \
                            AND iw.whse_id = ? ";
            var aArgs = [globals.org_id, uItemID.toString(), uWareID.toString()];
            
            if (sProject) {
                sSQL += " AND f.custproj_desc = ?";
                aArgs.push(sProject);
            }
            else {
                sSQL += " AND f.custproj_desc IS NULL";
            }
            
            if (uItemBinID) {
                sSQL += " AND itemwhseloc_id = ?";
                aArgs.push(uItemBinID.toString());
            }

            if (nQty > 0) {
                sSQL += " AND (fifo_qty_remaining - fifo_qty_reserved - ISNULL(fifo_qty_reserved_project, 0)) > 0";
                nQtyToReserve = nQty;
            }
            else {
                sSQL += " AND ISNULL(fifo_qty_reserved_project, 0) > 0";
                nQtyToRestore = nQty * -1;
            }

            fsFifo = scopes.avDB.getFSFromSQL(sSQL, "in_item_fifo", aArgs);
        } else {
            // If using previous foundset, set the quantities
            if (nQty > 0) {
                nQtyToReserve = nQty;
            } else {
                nQtyToRestore = nQty * -1;
            }
        }

        if (utils.hasRecords(fsFifo)) {
            fsFifo.sort('fifo_date asc, fifo_qty_remaining asc');

            for (var i = 1; i <= fsFifo.getSize(); i++) {
                var rFifo = fsFifo.getRecord(i);

                // RESERVING QTY
                if (nQtyToReserve) {
                    var nQtyAvail = rFifo.fifo_qty_remaining - rFifo.fifo_qty_reserved - rFifo.fifo_qty_reserved_project;
                    var nQtyReservedThisFifo = 0;

                    if (nQtyAvail >= nQtyToReserve) {
                        nQtyReservedThisFifo = nQtyToReserve;
                        nQtyToReserve = 0;
                    }
                    else {
                        nQtyReservedThisFifo = nQtyAvail;
                        nQtyToReserve -= nQtyAvail;
                    }

                    rFifo.fifo_qty_reserved_project += nQtyReservedThisFifo;

                    databaseManager.saveData(rFifo);

					// have to update using sql too because when called from onRecordUpdate_orderRevisionDetailItem() the db save isnt working right away. it calls it twice, eventually the save from
					// the first call works, but since this func uses sql, the 2nd call doesnt see the change from the first call because the save didnt work. update using sql, so the 2nd call
					// sees changes from first call.
//                    scopes.avDB.RunSQL("UPDATE in_item_fifo SET fifo_qty_reserved_project = ? WHERE org_id = ? AND fifo_id = ?", null, [rFifo.fifo_qty_reserved_project, globals.org_id, rFifo.fifo_id.toString()]);

                    if (nQtyToReserve == 0) {
                        break;
                    }
                }
                // RESTORING QTY
                else if (nQtyToRestore) {
                    var nQtyReservedForProject = rFifo.fifo_qty_reserved_project;
                    var nQtyRestoredThisFifo = 0;

                    if (nQtyReservedForProject >= nQtyToRestore) {
                        nQtyRestoredThisFifo = nQtyToRestore;
                        nQtyToRestore = 0;
                    }
                    else {
                        nQtyRestoredThisFifo = nQtyReservedForProject;
                        nQtyToRestore -= nQtyReservedForProject;
                    }

                    rFifo.fifo_qty_reserved_project -= nQtyRestoredThisFifo;

                    databaseManager.saveData(rFifo);

					// have to update using sql too because when called from onRecordUpdate_orderRevisionDetailItem() the db save isnt working right away. it calls it twice, eventually the save from
					// the first call works, but since this func uses sql, the 2nd call doesnt see the change from the first call because the save didnt work. update using sql, so the 2nd call
					// sees changes from first call.
//                    scopes.avDB.RunSQL("UPDATE in_item_fifo SET fifo_qty_reserved_project = ? WHERE org_id = ? AND fifo_id = ?", null, [rFifo.fifo_qty_reserved_project, globals.org_id, rFifo.fifo_id.toString()]);

                    if (nQtyToRestore == 0) {
                        break;
                    }
                }
            }
        }
        
        return fsFifo; // Return the foundset for potential reuse
    }
    return null;
}

/**
 * @public
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"163AD988-3801-4D14-AB0B-EAF7B7A05916"}
 */
function isProjectInventoryOn() {
	return (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.UseProjectInventory) == 1);
}

/**
 * @public
 *
 * @param {UUID} uItemID
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"1CD36C2F-BB15-472C-B75C-6D5CB8868971"}
 */
function doesItemUseProjectInventory(uItemID) {
	var bDoesItemHaveProjectInventory = false;

	if (uItemID && isProjectInventoryOn()) {
		var sSQL = "SELECT COUNT(f.fifo_id) \
					FROM in_item_fifo f \
					INNER JOIN in_item i ON i.item_id = f.item_id \
					WHERE \
						f.org_id = ? \
						AND f.item_id = ? \
						AND i.itemtype_code = 'F' \
						AND f.custproj_desc IS NOT NULL";
		var aArgs = [globals.org_id, uItemID.toString()];

		bDoesItemHaveProjectInventory = (scopes.avDB.SQLQuery(sSQL, null, aArgs) > 0);
	}

	return bDoesItemHaveProjectInventory;
}

/**
 * @public
 *
 * @param {UUID} uItemID
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"F3DCEF87-F782-45E5-ABF7-1A5C853D11D9"}
 */
function canItemUseProjectInventory(uItemID) {
	if (uItemID && getItemTypeCode(uItemID) == scopes.avUtils.ITEM_TYPE.FinishedGood && isProjectInventoryOn()) {
		return true;
	}
	else {
		return false;
	}
}

/**
 * @public
 *
 * @param {UUID} uItemID
 *
 * @return {String}
 *
 * @properties={typeid:24,uuid:"CFA67553-4272-4435-A61A-31C0E3E15E63"}
 */
function getItemTypeCode(uItemID) {
	var sItemTypeCode = null;

	if (uItemID) {
		var sSQL = "SELECT itemtype_code FROM in_item WHERE org_id = ? AND item_id = ?";
		var aArgs = [globals.org_id, uItemID.toString()];
		
		sItemTypeCode = scopes.avDB.SQLQuery(sSQL, null, aArgs)
	}

	return sItemTypeCode;
}

/**
 * @public
 *
 * @param {UUID} uItemID
 *
 * @return {JSFoundset<db:/avanti/in_item_fifo>}
 *
 * @properties={typeid:24,uuid:"2EDD559A-63F4-41E5-ACCC-824C3BE1CECF"}
 */
function getItemNonProjectInventoryFifo(uItemID) {
	/**@type {JSFoundset<db:/avanti/in_item_fifo>} */
	var fsFifo = null;

	if (uItemID) {
		var sSQL = "SELECT fifo_id \
					FROM in_item_fifo \
					WHERE \
						org_id = ? \
						AND item_id = ? \
						AND custproj_desc IS NULL";
		var aArgs = [globals.org_id, uItemID.toString()];

		fsFifo = scopes.avDB.getFSFromSQL(sSQL, "in_item_fifo", aArgs);
	}

	return fsFifo;
}

/**
 * @public
 *
 * @param {UUID} uItemID
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"36CDCACF-6AAD-455F-8881-1554B120B46E"}
 */
function doesItemHaveNonProjectItemProjectRecs(uItemID) {
	var bDoesItemHaveProjectInventory = false;

	if (uItemID) {
		var sSQL = "SELECT COUNT(*) \
					FROM in_item_project \
					WHERE \
						org_id = ? \
						AND item_id = ? \
						AND custproj_desc IS NULL";
		var aArgs = [globals.org_id, uItemID.toString()];

		bDoesItemHaveProjectInventory = (scopes.avDB.SQLQuery(sSQL, null, aArgs) > 0);
	}

	return bDoesItemHaveProjectInventory;
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/in_item_fifo>} rFifo
 * @param {Number} nQtyRemaining -  this the amount of change when rFifo is being updated (rather than inserted)
 * @param {Number} nQtyReserved -  this the amount of change when rFifo is being updated (rather than inserted)
 * @param {Number} nQtyLineItemReserved -  this the amount of change when rFifo is being updated (rather than inserted)
 *
 * @properties={typeid:24,uuid:"35D68D21-82C2-436A-93A4-FE86F2AA2C02"}
 */
function updateItemProject(rFifo, nQtyRemaining, nQtyReserved, nQtyLineItemReserved) {
	/**
	 * sl-21033 - this function was using sql to retrieve the item_project recs to update, but this caused a problem in inventory transactions. if you have multiple trans details for
	 * the same item/warehouse/project, and the item/warehouse item_project rec is created when processing the first detail, when we get to the 2nd detail the sql cant find it because
	 * it hasnt been saved to the database, even thos we are explicitly saving it. i think because we are in a db transaction.
	 * I tried using a foundset find to get the rec, but that didnt find it either.
	 * I ended up changing it to use getRec. i set bCreateIfDoesntExist to true, with means getrec creates the rec, then adds it to its cache. then when processing the next trans
	 * detail the getrec call retrieves the record from the cache. that means i could get rid of the createNewItemProjectRecord() here as well, as the rec is being created in getrec now.
	 * but i left it just in case getrec didnt return a rec for some reason.
	 */
	
	if (nQtyRemaining == null) {
		nQtyRemaining = rFifo.fifo_qty_remaining;
	}
	if (nQtyReserved == null) {
		nQtyReserved = rFifo.fifo_qty_reserved;
	}
	if (nQtyLineItemReserved == null) {
		nQtyLineItemReserved = rFifo.fifo_qty_reserved_project;
	}
	
	// PROJECT - ITEM LEVEL
	/*** @type {JSRecord<db:/avanti/in_item_project>} */
	var rItemProject_itemLevel = scopes.avDB.getRec("in_item_project", ["item_id", "custproj_desc", "itemwhse_id", "itemwhseloc_id"], 
		[rFifo.item_id, rFifo.custproj_desc, null, null],
		null, true);

	if (rItemProject_itemLevel) {
		updateItemProjectRecord(rItemProject_itemLevel);
	}
	else {
		createNewItemProjectRecord(null, null);
	}

	// PROJECT - ITEM WAREHOUSE LEVEL
	/*** @type {JSRecord<db:/avanti/in_item_project>} */
	var rItemProject_itemWareLevel = scopes.avDB.getRec("in_item_project", ["item_id", "custproj_desc", "itemwhse_id", "itemwhseloc_id"], 
		[rFifo.item_id, rFifo.custproj_desc, rFifo.itemwhse_id, null],
		null, true);

	if (rItemProject_itemWareLevel) {
		updateItemProjectRecord(rItemProject_itemWareLevel);
	}
	else {
		createNewItemProjectRecord(rFifo.itemwhse_id, null);
	}

	// PROJECT - ITEM WAREHOUSE BIN LEVEL
	if (rFifo.itemwhseloc_id) {
		/*** @type {JSRecord<db:/avanti/in_item_project>} */
		var rItemProject_itemWareBinLevel = scopes.avDB.getRec("in_item_project", ["item_id", "custproj_desc", "itemwhse_id", "itemwhseloc_id"], 
			[rFifo.item_id, rFifo.custproj_desc, rFifo.itemwhse_id, rFifo.itemwhseloc_id],
			null, true);

		if (rItemProject_itemWareBinLevel) {
			updateItemProjectRecord(rItemProject_itemWareBinLevel)
		}
		else {
			createNewItemProjectRecord(rFifo.itemwhse_id, rFifo.itemwhseloc_id);
		}
	}

	function createNewItemProjectRecord(uWareID, uBinID) {
		/*** @type {JSRecord<db:/avanti/in_item_project>} */
		var rItemProject = scopes.avDB.newRecord("in_item_project");

		rItemProject.org_id = rFifo.org_id;
		rItemProject.item_id = rFifo.item_id;
		rItemProject.custproj_desc = rFifo.custproj_desc;
		rItemProject.ip_qty_on_hand = nQtyRemaining;
		rItemProject.ip_qty_reserved = nQtyReserved;
		rItemProject.ip_qty_line_item_reserved = nQtyLineItemReserved;
		rItemProject.itemwhse_id = uWareID;
		rItemProject.itemwhseloc_id = uBinID;

		rFifo.ip_id = rItemProject.ip_id;

		databaseManager.saveData(rItemProject);
		databaseManager.saveData(rFifo);
	}

	function updateItemProjectRecord(rItemProject) {
		rItemProject.ip_qty_on_hand += nQtyRemaining;
		rItemProject.ip_qty_reserved += nQtyReserved;
		rItemProject.ip_qty_line_item_reserved += nQtyLineItemReserved;

		rFifo.ip_id = rItemProject.ip_id;

		databaseManager.saveData(rItemProject);
		databaseManager.saveData(rFifo);
	}
}

/**
 * @public
 *
 * @param {UUID} uItemID
 * @param {UUID} uWhseID - warehouse id
 * @param {UUID} uItemBinID - item ware loc id (not ware loc id)
 * @param {String} sProject
 *
 * @return {UUID}
 *
 * @properties={typeid:24,uuid:"92FEAAAE-6992-4305-BFAD-20FDE4175814"}
 */
function getItemProjectIDForItemWareBinProject(uItemID, uWhseID, uItemBinID, sProject) {
	/**@type {UUID} */
	var uProjectID = null;

	if (uItemID && uWhseID && uItemBinID) {
		var sSQL = "SELECT ip_id \
					FROM in_item_project iip \
					INNER JOIN in_item_warehouse iw ON iip.itemwhse_id = iw.itemwhse_id \
					WHERE \
						iip.org_id = ? \
						AND iip.item_id = ? \
						AND iw.whse_id = ? \
						AND iip.itemwhseloc_id = ? ";
		var aArgs = [globals.org_id, uItemID.toString(), uWhseID.toString(), uItemBinID.toString()];

		if (sProject) {
			sSQL += "AND iip.custproj_desc = ?";
			aArgs.push(sProject);
		}
		else {
			sSQL += "AND custproj_desc IS NULL";
		}

		uProjectID = scopes.avDB.SQLQuery(sSQL, null, aArgs);
	}

	return uProjectID;
}

/**
 * @public 
 * 
 * @param {UUID|String} uItemID
 * @param {UUID|String} [uItemWhseID]
 * @param {UUID|String} [uItemBinID]
 * @param {String} [sProject]
 * 
 * @return {JSRecord<db:/avanti/in_item_project>}
 *
 * @properties={typeid:24,uuid:"B6D3166A-BB02-4A58-B047-2E9EC420211F"}
 */
function getItemProject(uItemID, uItemWhseID, uItemBinID, sProject) {
	/**@type {JSRecord<db:/avanti/in_item_project>} */
	var rItemProject = null;

	if (uItemID) {
		var sSQL = "SELECT ip_id \
					FROM in_item_project \
					WHERE \
						org_id = ? \
						AND item_id = ? ";
		var aArgs = [globals.org_id, uItemID.toString()];

		if (uItemWhseID) {
			sSQL += " AND itemwhse_id = ? ";
			aArgs.push(uItemWhseID.toString());
		}
		else {
			sSQL += " AND itemwhse_id IS NULL ";
		}

		if (uItemBinID) {
			sSQL += " AND itemwhseloc_id = ? ";
			aArgs.push(uItemBinID.toString());
		}
		else {
			sSQL += " AND itemwhseloc_id IS NULL ";
		}

		if (sProject) {
			sSQL += " AND custproj_desc = ? ";
			aArgs.push(sProject);
		}
		else {
			sSQL += " AND custproj_desc IS NULL ";
		}

		rItemProject = scopes.avDB.getRecFromSQL(sSQL, "in_item_project", aArgs);
	}

	return rItemProject;
}

/**
 * @public
 *
 * @param {UUID} uItemBinID - item ware loc id (not ware loc id)
 * @param {String} sProject
 *
 * @return {JSRecord<db:/avanti/in_item_project>}
 *
 * @properties={typeid:24,uuid:"74B30798-2968-48F2-A991-C7E3C9C6F1CA"}
 */
function getItemProjectUsingItemBinProject(uItemBinID, sProject) {
	/**@type {JSRecord<db:/avanti/in_item_project>} */
	var rItemProject = null;

	if (uItemBinID) {
		var sSQL = "SELECT ip_id \
					FROM in_item_project \
					WHERE \
						org_id = ? \
						AND itemwhseloc_id = ? ";
		var aArgs = [globals.org_id, uItemBinID.toString()];

		if (sProject) {
			sSQL += "AND custproj_desc = ?";
			aArgs.push(sProject);
		}
		else {
			sSQL += "AND custproj_desc IS NULL";
		}

		rItemProject = scopes.avDB.getRecFromSQL(sSQL, "in_item_project", aArgs);
	}

	return rItemProject;
}

/**
 * @public
 *
 * @param {UUID} uItemBinID - item ware loc id (not ware loc id)
 * @param {String} sProject
 *
 * @return {UUID}
 *
 * @properties={typeid:24,uuid:"0F0626A5-7213-4417-A2D0-505C38DD2BB8"}
 */
function getItemProjectIDUsingItemBinProject(uItemBinID, sProject) {
	/**@type {UUID} */
	var uItemProjectID = null;

	if (uItemBinID) {
		var sSQL = "SELECT ip_id \
					FROM in_item_project \
					WHERE \
						org_id = ? \
						AND itemwhseloc_id = ? ";
		var aArgs = [globals.org_id, uItemBinID.toString()];

		if (sProject) {
			sSQL += "AND custproj_desc = ?";
			aArgs.push(sProject);
		}
		else {
			sSQL += "AND custproj_desc IS NULL";
		}

		uItemProjectID = scopes.avDB.SQLQuery(sSQL, null, aArgs);
	}

	return uItemProjectID;
}

/**
 * @public
 *
 * @param {UUID} uItemBinID
 * @param {String} sProject
 *
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"E1D1D984-B276-4AFD-B8E4-BF3D6ECD11A5"}
 */
function getItemBinProjectOHQ(uItemBinID, sProject) {
	var nOHQ = 0;

	if (uItemBinID) {
		var sSQL = "SELECT ip_qty_on_hand \
					FROM in_item_project \
					WHERE \
						org_id = ? \
						AND itemwhseloc_id = ? ";
		var aArgs = [globals.org_id, uItemBinID.toString()];

		if (sProject) {
			sSQL += "AND custproj_desc = ?";
			aArgs.push(sProject);
		}
		else {
			sSQL += "AND custproj_desc IS NULL";
		}

		nOHQ = scopes.avDB.SQLQuery(sSQL, null, aArgs);
	}

	return nOHQ;
}

/**
 * Get the default offset g/l account
 *
 * @param {JSRecord<db:/avanti/in_trans_entry_header>} rTransEntryHeader
 * @param {UUID} uItemId
 *
 * @return
 * @properties={typeid:24,uuid:"2690D077-EDD6-4FC2-9BB0-E4BB8C2C6F5E"}
 */
function getDefaultOffsetGLA(rTransEntryHeader, uItemId) {
	if (rTransEntryHeader && uItemId) {
		var whse_id = rTransEntryHeader.intraneh_whse_id;
		var intranstype_id = rTransEntryHeader.intranstype_id;
		var sSql = "SELECT iwogi.in_whse_itemclass_gl_offset \
                    FROM in_whse_offset_gl_itemclass iwogi\
                    INNER JOIN in_warehouse_gl_itemclass iwgi\
                     ON iwogi.whseitemclass_id = iwgi.whseitemclass_id\
                    INNER JOIN in_item it \
                     ON it.itemclass_id = iwgi.itemclass_id\
                    WHERE iwgi.whse_id = ? AND it.item_id = ? \
                    AND iwogi.intranstype_id = ? AND iwogi.org_id = ?";
		var args = [whse_id.toString(), uItemId.toString(), intranstype_id.toString(), scopes.globals.org_id];
		var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, 1);

		if (ds && ds.getMaxRowIndex() > 0) {
			return ds.getValue(1, 1);
		}
	}

	return null;
}

/**
 * @public
 *
 * @param {String} sItemCode
 * @param {String} sProject
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"16F6F5A1-2DF9-4F78-A2A3-BEE7E4382333"}
 */
function doesItemUseThisProject(sItemCode, sProject) {
	var bDoesItemUseThisProject = false;

	if (sItemCode && sProject && isProjectInventoryOn()) {
		var sSQL = "SELECT COUNT(*) \
					FROM in_item_fifo f \
					INNER JOIN in_item i on f.item_id = i.item_id \
					WHERE \
						f.org_id = ? \
						AND i.item_code = ? \
						AND f.custproj_desc = ?";
		var aArgs = [globals.org_id, sItemCode, sProject];

		bDoesItemUseThisProject = (scopes.avDB.SQLQuery(sSQL, null, aArgs) > 0);
	}

	return bDoesItemUseThisProject;
}

/**
 * @public
 *
 * @param {String} sProject
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"B5FE6CA3-DB74-4E22-A653-6E471F29CB7B"}
 */
function isProjectValid(sProject) {
	var bIsProjectValid = false;

	if (sProject) {
		var sSQL = "SELECT COUNT(*) \
					FROM sa_customer_project \
					WHERE \
						org_id = ? \
						AND custproj_desc = ?";
		var aArgs = [globals.org_id, sProject];

		bIsProjectValid = (scopes.avDB.SQLQuery(sSQL, null, aArgs) > 0);
	}

	return bIsProjectValid;
}

/**
 * Round Item Quantity
 * @param {JSRecord<db:/avanti/in_item>} rItem
 * @param {Number} nQuantity
 *
 * @return {Number} Rounded Quantity
 *
 * @properties={typeid:24,uuid:"18BB772B-55E3-4F05-8AF5-BCA4267AAEBF"}
 */
function roundItemQuantity(rItem, nQuantity) {

	if (rItem) {
		return globals["avUtilities_roundNumber"](nQuantity, rItem.item_decimal_places);

	}
	else {
		return nQuantity;
	}
}

/**
 * @public
 *
 * @param {String} sItemCode
 *
 * @return {Array<String>}
 *
 * @properties={typeid:24,uuid:"D7EDD690-6B88-4A11-B306-D024105293B0"}
 */
function getItemProjects(sItemCode) {
	var aProjects = [];

	if (sItemCode) {
		var sSQL = "SELECT DISTINCT F.custproj_desc \
					FROM in_item_fifo F \
					INNER JOIN in_item I ON F.item_id = I.item_id \
					WHERE \
						F.org_id = ? \
						AND I.item_code = ? \
						AND F.custproj_desc IS NOT NULL \
					ORDER BY 1";
		var aArgs = [globals.org_id, sItemCode];

		var dsProjects = scopes.avDB.getDataset(sSQL, aArgs);

		if (dsProjects && dsProjects.getMaxRowIndex() > 0) {
			aProjects = dsProjects.getColumnAsArray(1);
		}
	}

	return aProjects;
}

/**
 * Get the expected in quantity of the finished good item on a production order
 * @param {UUID} uItemId
 * @param {UUID} uOrderDetailId
 *
 * @return nExpectedInQty
 *
 * @properties={typeid:24,uuid:"0FDF7145-4730-43EA-95C9-A8358B3A3131"}
 */
function getItemExpectedInQuantity(uItemId, uOrderDetailId) {
	if (uItemId && uOrderDetailId) {
		var sSql = "SELECT detail_balance \
                    FROM _v_in_item_expected_in_details \
                    WHERE org_id = ? AND item_id = ? AND detail_id = ? \
                   ";
		var args = [scopes.globals.org_id, uItemId.toString(), uOrderDetailId.toString()];
		var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, 1);

		if (ds && ds.getMaxRowIndex() > 0) {
			return ds.getValue(1, 1);
		}
	}
	else {
		return 0;
	}
}

/**
 * Logic removed from avCalcs_detail_setCostSummary->addItem and put in this function so it can be called from other places
 * 
 * ####################--This function should be called within locked blocks if record is being updated!!!#####################
 * 
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sa_order_revd_item>} rRevDItem
 * 
 * @properties={typeid:24,uuid:"7B9EEAFF-F258-4B7C-84E2-EB12EA2363E9"}
 */
function recalcRevDItemReservedAndBOQtys(rRevDItem) {
    var rDetail = rRevDItem.sa_order_revd_item_to_sa_order_revision_detail.getRecord(1);
	var rOrdRev = rDetail.sa_order_revision_detail_to_sa_order_revision_header.getRecord(1);
    var bIsEst = rOrdRev.sa_order_revision_header_to_sa_order.ordh_document_type == scopes.avUtils.DOCUMENT_TYPE.Estimate; 
    var sParentItemTypeCode = null;
    var nReservedQty, nReservedQtyEst, nShippedQty, nBackorderQty;
    var bFulfillmentLine = scopes.avDetail.isFulfillmentLine(rDetail);
    var rItem = rRevDItem.sa_order_revd_item_to_in_item.getRecord(1);
    
    if (utils.hasRecords(rDetail.sa_order_revision_detail_to_sa_order_revision_detail$child_to_parent) 
        && utils.hasRecords(rDetail.sa_order_revision_detail_to_sa_order_revision_detail$child_to_parent.sa_order_revision_detail_to_in_item)) {
        sParentItemTypeCode = rDetail.sa_order_revision_detail_to_sa_order_revision_detail$child_to_parent.sa_order_revision_detail_to_in_item.itemtype_code;         
    }
    
    // sl-9763 - calc nReservedQty for est too - but write it to diff col - to be used if a reservation is created
    if (!bIsEst && !sParentItemTypeCode && utils.hasRecords(rDetail.sa_order_revision_detail_to_in_item) 
        && (rDetail.sa_order_revision_detail_to_in_item.itemtype_code == scopes.avUtils.ITEM_TYPE.AssembledKit 
        || rDetail.sa_order_revision_detail_to_in_item.itemtype_code == scopes.avUtils.ITEM_TYPE.FinishedGood 
        || rDetail.sa_order_revision_detail_to_in_item.itemtype_code == scopes.avUtils.ITEM_TYPE.NonStock 
        || rDetail.sa_order_revision_detail_to_in_item.itemtype_code == scopes.avUtils.ITEM_TYPE.Stock)) {
            
        nReservedQty = rDetail.ordrevd_qty_reserved;
    }
    else{
        var nCurrentCommittment = 0.00;
        var iQtyAvailable = 0.00;
        
        if (utils.hasRecords(rRevDItem.sa_order_revd_item_to_in_committment)) {
            nCurrentCommittment = rRevDItem.sa_order_revd_item_to_in_committment.cur_qty_committed;
        }
            
		iQtyAvailable = globals['avCalcs_item_getItemAvailability'](rRevDItem.item_id, rRevDItem.ordrevditem_est_uom_id, rDetail.whse_id, null, nCurrentCommittment);
    
        if (rRevDItem.sa_order_revd_item_to_in_item.itemtype_code == scopes.avUtils.ITEM_TYPE.Service) {
            iQtyAvailable = rRevDItem.ordrevditem_qty; //Never backorder a service item
        }
        
        var nQtyRemaining = 0;
        var bCompletedOrCancelledProdLine = false;

		if (bFulfillmentLine) {
			nQtyRemaining = rRevDItem.ordrevditem_qty - rDetail.ordrevd_qty_shipped - rDetail.ordrevd_prod_receipt_qty;
		}
        // sl-18987b - the nQtyRemaining formula above only works for pick lines - below is for prod lines
		else {
			// if job complete or order cancelled then nReservedQty must be zero
			if (rOrdRev.ordrevh_order_status == scopes.avUtils.ORDER_STATUS.Cancelled) {
				nQtyRemaining = 0;
				bCompletedOrCancelledProdLine = true;
			}
			else if (utils.hasRecords(rDetail.sa_order_revision_detail_to_prod_job) && rDetail.sa_order_revision_detail_to_prod_job.jobstat_id == scopes.avUtils.JOB_STATUS.Completed) {
				nQtyRemaining = 0;
				bCompletedOrCancelledProdLine = true;
			}
			// otherwise nReservedQty will be ordrevditem_qty (or iQtyAvailable), as we dont keep track of revditem ship qty for prod lines, but we do need to consider the 
			// committed reserved qty may already have been reduced by material entries
			else if (utils.hasRecords(rDetail.sa_order_revision_detail_to_prod_job)) {
				nQtyRemaining = rRevDItem.ordrevditem_qty - scopes.avShopFloor.getJobItemMatQtyUsed(rDetail.job_id, rRevDItem.item_id);
			}
			else {
				nQtyRemaining = rRevDItem.ordrevditem_qty;
			}
		}
        
        if (iQtyAvailable >= nQtyRemaining) {
            nReservedQty = nQtyRemaining;
        }
        else {
            nReservedQty = iQtyAvailable;
        }
        
        // sl-17780 - only update ordrevd_qty_reserved for non-product inventory lines. for products or non-inventory
        // lines we come here to process raw material used on the section. ordrevd_qty_reserved and ordrevd_qty_backord
        // are already set correctly for these lines elsewhere.
        //If the parent id is a BTO kit, then update the reserved and backorder for the child records.
        if (sParentItemTypeCode == scopes.avUtils.ITEM_TYPE.BuildToOrderKit
                && utils.hasRecords(rDetail.sa_order_revision_detail_to_in_item)
                && rDetail.sa_order_revision_detail_to_in_item.itemtype_code != scopes.avUtils.ITEM_TYPE.Product) {

            rDetail.ordrevd_qty_reserved = nReservedQty;
            rDetail.ordrevd_qty_backord = rDetail.ordrevd_qty_ordered - rDetail.ordrevd_qty_reserved - rDetail.ordrevd_qty_shipped;
            
            if (rDetail.ordrevd_qty_backord < 0) {
                rDetail.ordrevd_qty_backord = 0;
            }
        }
    }

    if (bIsEst) {
        nReservedQtyEst = nReservedQty;
        nReservedQty = 0;
    }

    // SL-18987 - cant use rDetail.ordrevd_qty_shipped for raw material on prod line
	if (bFulfillmentLine) {
        nShippedQty = rDetail.ordrevd_qty_shipped;
	}
	else {
		nShippedQty = 0;
	}
    
	if (bCompletedOrCancelledProdLine) {
		nBackorderQty = 0;
	}
	else if (bFulfillmentLine) {
		nBackorderQty = rRevDItem.ordrevditem_qty - nShippedQty - nReservedQty;
	}
	// if there are mat entries then nQtyRemaining takes this into consideration
	else {
		nBackorderQty = nQtyRemaining - nReservedQty;
	}
    
	//Rounding back order quantity to decimal units to prevent purchasing decimal quantities
	if (nBackorderQty > 0) {
		nBackorderQty = scopes.avInv.roundItemQuantity(rItem,nBackorderQty)
	}
	
	if (nReservedQty > 0) {
		nReservedQty = scopes.avInv.roundItemQuantity(rItem,nReservedQty)
	}
	
	if (nReservedQtyEst > 0) {
		nReservedQtyEst = scopes.avInv.roundItemQuantity(rItem,nReservedQtyEst)
	}
	
    if (nBackorderQty < 0) {
        nBackorderQty = 0;
    }
    if (nReservedQty < 0) {
    	nReservedQty = 0;
    }
    if (nReservedQtyEst < 0) {
    	nReservedQtyEst = 0;
    }

    rRevDItem.ordrevditem_backorder_qty = nBackorderQty;   
    rRevDItem.ordrevditem_reserved_qty = nReservedQty;
    rRevDItem.ordrevditem_reserved_qty_est = nReservedQtyEst;
}

/**
 * <AUTHOR> Dotzlaw
 * @param {JSRecord<db:/avanti/sa_task_worktype_section> | JSRecord<db:/avanti/sa_order_revision_detail_section>} rSection
 * @param {String|UUID} sTaskID - The ID for the press task being used
 * @param {String} sForm - the calling form name
 * @returns {JSRecord<db:/avanti/sa_task_worktype_section> | JSRecord<db:/avanti/sa_order_revision_detail_section>} 
 * @public
 *
 * @properties={typeid:24,uuid:"2D5BCB6E-03F4-4833-B220-BD61DB38F988"}
 * @AllowToRunInFind
 */
function setGummerDie(rSection, sTaskID, sForm) {
    
    var bDefaultDie = setVL_dieGummer(sTaskID),
        sRel = (sForm.search("sa_order_revision_detail_section") > -1) ? "sa_order_revision_detail_section_to_in_item$die_window_f" : "sa_task_worktype_section_to_in_item$diewindowf";
    
    forms[sForm].elements.fldGummerDie.enabled = true;
    forms[sForm].elements.btnLookupGummerDie.visible = true;
    
    if (rSection.item_id_die_window_f
            && (!rSection.item_id_die_gummer || bDefaultDie)
            && utils.hasRecords(rSection[sRel])
            && utils.hasRecords(rSection[sRel].in_item_to_in_item_die)
            && rSection[sRel].in_item_to_in_item_die.itemdie_id_gummer) {
       
        rSection.item_id_die_gummer = rSection[sRel].in_item_to_in_item_die.itemdie_id_gummer;
        
        if (bDefaultDie) {
            
            forms[sForm].elements.fldGummerDie.enabled = false;
            forms[sForm].elements.btnLookupGummerDie.visible = false;
        }
    }
    else if (rSection.item_id_die_window_f) {
        
        forms[sForm].elements.fldGummerDie.enabled = true;
        forms[sForm].elements.btnLookupGummerDie.visible = true;
    }
    
    return rSection;
}

/**
 * @param {String|UUID} sTaskID - The ID for the press task being used
 * @return {Boolean} True if default die should be used (no dies available to select from)
 * @properties={typeid:24,uuid:"74790079-996B-4B3F-B873-8DBE6817D190"}
 */
function setVL_dieGummer(sTaskID) {

    /***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
    var oSQL = { },
        /***@type {JSDataSet} */
        dsData,
        aReturn = [],
        aDisplay = [],
        bAllDies = false;

    if (sTaskID) {

        scopes.globals.avBase_selectedTaskID = sTaskID;

        if (utils.hasRecords(_to_sa_task$avbase_selectedtaskid)) {

            if (_to_sa_task$avbase_selectedtaskid.sa_task_to_sa_task_standard.taskstd_access_all_dies == 1) {

                bAllDies = true;
            }
        }

        // First get the die item classes used by the press
        oSQL.sql = "SELECT i.item_code + ': ' + i.item_desc1 AS DES, \
            i.item_id, i.itemclass_id AS CL \
            FROM in_item i \
            INNER JOIN in_item_die d ON i.item_id = d.item_id \
            INNER JOIN in_item_class c ON i.itemclass_id = c.itemclass_id";

        if (bAllDies) {

            oSQL.sql = scopes.avDB.safeSQL(oSQL.sql, "i.item_status = ? AND c.itemclass_die_type = ?", "in_item", null, "i");
            oSQL.args = ["A", dieGummer];

        }
        else {

            oSQL.sql = scopes.avDB.safeSQL(oSQL.sql, "i.item_status = ? AND c.itemclass_die_type = ? AND i.itemclass_id IN (SELECT DISTINCT(d.itemclass_id) FROM sa_task_class_die d WHERE d.task_id = ?) ", "in_item", null, "i");
            oSQL.args = ["A", dieGummer, sTaskID.toString()];
        }
        oSQL.sql += " ORDER BY CL, DES";

        dsData = scopes.globals["avUtilities_sqlDataset"](oSQL);

        if (dsData) {

            aDisplay = dsData.getColumnAsArray(1);
            aReturn = dsData.getColumnAsArray(2);
        }
    }
    
    application.setValueListItems("avItemDiesGummer_custom", aDisplay, aReturn);
    
    // Only if there are die selections available, then allow the gummer to be editable
    if (aDisplay.length > 0) {
        
        return false;
    }
    else {

        return true;
    }
}

/**
 * @public  
 * 
 * @param {UUID|String} uItemID
 * @param {UUID|String} uWareID
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"15518D41-8519-43C5-BCA3-C460D252717B"}
 */
function doesItemWarehouseUseBins(uItemID, uWareID) {
	var bDoesItemWarehouseUseBins = false;
	
	if (uItemID && uWareID) {
		/** @type {JSRecord<db:/avanti/in_warehouse>} */
		var rWarehouse = scopes.avDB.getRec("in_warehouse", ["whse_id"], [uWareID.toString()]);
		
		if (rWarehouse.whse_enable_bin_locations) {
			/** @type {JSRecord<db:/avanti/in_item>} */
			var rItem = scopes.avDB.getRec("in_item", ["item_id"], [uItemID.toString()]);
			
			if (!rItem.item_no_bin_location) {
				bDoesItemWarehouseUseBins = true;
			}
		}
	}
	
	return bDoesItemWarehouseUseBins;
}

/**
 * @public  
 * 
 * @param {UUID|String} uItemID
 * @param {UUID|String} [uWareID]
 * 
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"01C87120-D6F8-418B-9BFB-055D0AEFB796"}
 */
function getExpectedInTotalFromDetails(uItemID, uWareID) {
	var nTot = 0;
	
	if (uItemID) {
		var sSQL = "SELECT SUM(detail_balance) \
					FROM _v_in_item_expected_in_details \
					WHERE \
						org_id = ? \
						AND item_id = ?";
		var aArgs = [globals.org_id, uItemID.toString()]; 
		
		if (uWareID) {
			sSQL += " AND whse_id = ?";
			aArgs.push(uWareID.toString());
		}
		
		nTot = scopes.avDB.SQLQuery(sSQL, null, aArgs);
	}
	
	return nTot;
}

/**
 * @public  
 * 
 * @param {UUID|String} uItemID
 * @param {UUID|String} [uWareID]
 * 
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"9A144095-8E75-49F3-9C19-E9BF70125D17"}
 */
function getReservedTotalFromDetails(uItemID, uWareID) {
	var nTot = 0;
	
	if (uItemID) {
		var sSQL = "SELECT SUM(di.ordrevditem_reserved_qty) \
					FROM sa_order_revd_item di \
					INNER JOIN sa_order_revision_detail d ON di.ordrevd_id = d.ordrevd_id \
					INNER JOIN sa_order_revision_header h ON d.ordrevh_id = h.ordrevh_id \
					INNER JOIN sa_order o ON o.ordh_id = h.ordh_id \
					WHERE \
						di.org_id = ? \
						AND di.item_id = ? \
						AND di.ordrevditem_reserved_qty > 0 \
						AND h.ordrevh_revision = 0 \
						AND h.ordrevh_order_status != 'Cancelled' \
						AND o.ordh_document_type = 'ORD'";
		var aArgs = [globals.org_id, uItemID.toString()]; 
		
		if (uWareID) {
			sSQL += " AND d.whse_id = ?";
			aArgs.push(uWareID.toString());
		}
		
		nTot = scopes.avDB.SQLQuery(sSQL, null, aArgs);
	}
	
	return nTot;
}

/**
 * @properties={typeid:24,uuid:"D666E8AA-14EB-40C8-90E5-774779896F34"}
 */
function load_aAllWarehouseIDs() {
	globals.aAllWarehouseIDs = [];

	if (_to_in_warehouse$active) {
		for (var i = 1; i <= _to_in_warehouse$active.getSize(); i++) {
			var rWare = _to_in_warehouse$active.getRecord(i);
			globals.aAllWarehouseIDs.push(rWare.whse_id);
		}
	}
}

/**
 * @param {String} sProject
 * @param {UUID|String} uItemID
 * @param {UUID|String} uItemWareID
 * @param {UUID|String} uItemWareLocID
 *
 * @return
 * @properties={typeid:24,uuid:"64596F1C-E443-4EFC-ABC8-622215F7359C"}
 */
function recalcProjectOHQ (sProject, uItemID, uItemWareID, uItemWareLocID) {
	var nNewOHQ = 0;
	
	if (uItemID) {
		/**@type {JSRecord<db:/avanti/in_item>} */
		var rItem = scopes.avDB.getRec("in_item", ['item_id'], [uItemID]);
		
		if (rItem) {
			var nOpeningBalance = 0;
			var nTransactionBalance = 0;
			
			if (uItemWareLocID) {
				/**@type {JSRecord<db:/avanti/in_item_warehouse_location>} */
				var rItemWareLoc = scopes.avDB.getRecFromSQL("SELECT itemwhseloc_id FROM in_item_warehouse_location WHERE itemwhseloc_id = ?", "in_item_warehouse_location", [uItemWareLocID]);

				if (rItemWareLoc) {
					nOpeningBalance = rItemWareLoc.itemwhseloc_openingbal_qty;
					nTransactionBalance = getTransactionBalance(rItem, null, rItemWareLoc.whseloc_id, true, sProject);
				}
			}
			else if (uItemWareID) {
				/**@type {JSRecord<db:/avanti/in_item_warehouse>} */
				var rItemWare = scopes.avDB.getRecFromSQL("SELECT itemwhse_id FROM in_item_warehouse WHERE itemwhse_id = ?", "in_item_warehouse", [uItemWareID]);
				
				if (rItemWare) {
					nOpeningBalance = rItemWare.itemwhse_openingbal_qty;
					nTransactionBalance = getTransactionBalance(rItem, rItemWare.whse_id, null, true, sProject);
				}
			}
			else if (uItemID) {
				nTransactionBalance = getTransactionBalance(rItem, null, null, true, sProject);
			}
			
			if (nOpeningBalance || nTransactionBalance) {
				if (!nOpeningBalance) {
					nOpeningBalance = 0;
				}
				if (!nTransactionBalance) {
					nTransactionBalance = 0;
				}
				
				nNewOHQ = globals["avUtilities_roundNumber"](nOpeningBalance + nTransactionBalance, rItem.item_decimal_places);
			}
		}
	}
	
	return nNewOHQ;
}

/**
 * Get Transaction Total
 * 
 * I copied this over from utils_dev_dtl.js so it could be used from different places in the system
 *
 * @param {JSRecord<db:/avanti/in_item>} rItem - The Item Record
 * @param {UUID} [sWhse] - The warehouse id
 * @param {UUID} [uWhseLocID]
 * @param {Boolean} [bProjectInventory]
 * @param {String} [sProject]
 *
 * @returns {Number} Transaction Balance
 *
 * @properties={typeid:24,uuid:"2777BE61-D4C1-4785-8D29-5B52E0E17F2F"}
 */
function getTransactionBalance (rItem, sWhse, uWhseLocID, bProjectInventory, sProject) {
	var nTransQty = 0.00;
	var iDecimals = 0;
	
	if (rItem) {
		iDecimals = (rItem.item_decimal_places ? rItem.item_decimal_places : 0);
		
		//Use SQL For Optimal Speed
		/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
		var oSQL = { },
			/***@type {JSDataSet} ***/
			dsData;
		oSQL.sql = "SELECT SUM(ROUND(CAST(itemtransd_qty AS DECIMAL(38, 8)), " + iDecimals + ")) AS trans_total \
					FROM dbo.in_item_trans_detail TD \
					INNER JOIN dbo.in_item_trans_header TH ON TD.itemtransh_id = TH.itemtransh_id \
					INNER JOIN dbo.in_transaction_type TT ON TH.intranstype_id = TT.intranstype_id \
					WHERE  \
						(TD.item_id = ?) \
						AND (TT.intranstype_adjustment_type = 'Q') ";

		if (sWhse != null) {
			oSQL.sql += " AND (TD.whse_id = '" + sWhse + "')";
		}

		if (uWhseLocID) {
			oSQL.sql += " AND TD.itemtransd_whseloc_id = '" + uWhseLocID + "'";
		}
		
		if (bProjectInventory) {
			if (sProject) {
				oSQL.sql += " AND TD.custproj_desc = '" + sProject + "'";
			}
			else {
				oSQL.sql += " AND TD.custproj_desc IS NULL";
			}
		}

		oSQL.args = [rItem.item_id.toString()];
		dsData = globals["avUtilities_sqlDataset"](oSQL);

		if (dsData && dsData.getMaxRowIndex() == 1) {
			nTransQty = globals["avUtilities_roundNumber"](dsData.getValue(1, 1), rItem.item_decimal_places);
		}
	}
	
	return nTransQty;
}

/**
 * This sets Unavailable and Unusable Qtys at the itemBin, itemWarehouse and item levels
 * 
 * @public 
 * 
 * @param {JSRecord<db:/avanti/in_item>} rItem
 *
 * @properties={typeid:24,uuid:"01EBF33B-629D-481F-A88A-D543997EC38F"}
 */
function setUnavailableAndUnusableQtys(rItem) {
	var nUnavailableTot = 0;
	var nUnusableTot = 0;
	
	if (rItem.in_item_to_in_item_warehouse) {
		for (var w = 1; w <= rItem.in_item_to_in_item_warehouse.getSize(); w++) {
			var rWarehouse = rItem.in_item_to_in_item_warehouse.getRecord(w);
			var nBinUnavailableTot = 0;
			var nBinUnusableTot = 0;

			if (rWarehouse.in_item_warehouse_to_in_item_warehouse_location) {
				for (var b = 1; b <= rWarehouse.in_item_warehouse_to_in_item_warehouse_location.getSize(); b++) {
					var rBin = rWarehouse.in_item_warehouse_to_in_item_warehouse_location.getRecord(b);

					if (utils.hasRecords(rBin.in_item_warehouse_location_to_in_warehouse_location)) {
					    if (rBin.in_item_warehouse_location_to_in_warehouse_location.whseloc_unavailable) {
					    	rBin.itemwhseloc_unavailible_qty = rBin.itemwhseloc_onhand_qty;
					    	nBinUnavailableTot += rBin.itemwhseloc_unavailible_qty;
					    	databaseManager.saveData(rBin);
					    }
					    if (rBin.in_item_warehouse_location_to_in_warehouse_location.whseloc_unusable) {
					    	rBin.itemwhseloc_unusable_qty = rBin.itemwhseloc_onhand_qty;
					    	nBinUnusableTot += rBin.itemwhseloc_unusable_qty;
					    	databaseManager.saveData(rBin);
					    }
					}
				}
			}
			
			if (rWarehouse.in_item_warehouse_to_in_warehouse.whse_unavailable) {
				rWarehouse.itemwhse_unavailable_qty = rWarehouse.itemwhse_onhand_qty;
			}
			else {
				rWarehouse.itemwhse_unavailable_qty = nBinUnavailableTot;
			}
			
			if (rWarehouse.in_item_warehouse_to_in_warehouse.whse_unusable) {
				rWarehouse.itemwhse_unusable_qty = rWarehouse.itemwhse_onhand_qty;
			}
			else {
				rWarehouse.itemwhse_unusable_qty = nBinUnusableTot;
			}
			
	    	databaseManager.saveData(rWarehouse);
			
			nUnavailableTot += rWarehouse.itemwhse_unavailable_qty; 
			nUnusableTot += rWarehouse.itemwhse_unusable_qty; 
		}
	}
	
	rItem.item_unavailable_qty = nUnavailableTot; 
	rItem.item_unusable_qty = nUnusableTot; 
	
	databaseManager.saveData(rItem);
}

/**
 * @public 
 * 
 * @param {UUID|String} [uItemID]
 * 
 * @return {Number} -  the number of items fixed
 *
 * @properties={typeid:24,uuid:"9220ED58-D0B8-4B5A-B6CC-12587DB5EB9C"}
 */
function fixItemWarehouseBinOnHandQtysUsingTransactions(uItemID) {
	if (!uItemID) {
		forms["utils_dev_dtl"].elements.lblProcessing.text = "Starting Fix Item/Warehouse/Bin On Hand Qtys";
		forms["utils_dev_dtl"].elements.lblProcessing.visible = true;
		globals["updateUI"]();
	}

	var sItemID = uItemID ? uItemID.toString() : null;
	var nNumItemsFixed = 0;
	
	try {
		deleteTransMissingBin(sItemID);
		fixTransMissingBin(sItemID);
		fixItemBinOnWrongItemWare(sItemID);
		fixTransItemBinDeleted(sItemID);
		fixTransWareDoesntMatchTransBinWare(sItemID);
		
		nNumItemsFixed = fixItemOHQs(sItemID);
	}
	catch (ex) {
		application.output("fixItemWarehouseBinOnHandQtysUsingTransactions error: " + ex.message);
		application.output("fixItemWarehouseBinOnHandQtysUsingTransactions stack: " + ex.stack);
	}
	
	if (!uItemID) {
		forms["utils_dev_dtl"].elements.lblProcessing.text = "";
		forms["utils_dev_dtl"].elements.lblProcessing.visible = false;
		globals["updateUI"]();
	}
	
	return nNumItemsFixed;
}

/**
 * @param {String} [sItemID]
 *
 * @properties={typeid:24,uuid:"320FD1EE-696F-439F-9FD5-8DD0B717B33D"}
 */
function fixTransMissingBin(sItemID) {
	if (!sItemID) {
		forms["utils_dev_dtl"].elements.lblProcessing.text = "fixTransMissingBin";
		globals["updateUI"]();
		application.sleep(100);
	}
	
	//  transaction missing itemtransd_whseloc_id
	var sSQL = "SELECT itemtransd_id \
				FROM in_item_trans_detail td \
				INNER JOIN in_item_warehouse_location iwl ON td.itemwhseloc_id = iwl.itemwhseloc_id \
				INNER JOIN in_warehouse_location wl ON iwl.whseloc_id = wl.whseloc_id \
				INNER JOIN in_item_trans_header th ON td.itemtransh_id = th.itemtransh_id \
				INNER JOIN in_transaction_type tt ON th.intranstype_id = tt.intranstype_id \
				INNER JOIN in_item i ON td.item_id = i.item_id \
				WHERE \
					td.org_id = ? \
					AND td.itemtransd_whseloc_id IS NULL \
					AND i.itemtype_code NOT IN ('ZZ', 'SE', 'P') \
					AND tt.intranstype_adjustment_type = 'Q' ";
	var aArgs = [globals.org_id];
	
	if (sItemID) {
		sSQL += " AND td.item_id = ? ";
		aArgs.push(sItemID);
	}
	
	/***@type {JSFoundset<db:/avanti/in_item_trans_detail>} */
	var fsTrans = scopes.avDB.getFSFromSQL(sSQL, "in_item_trans_detail", aArgs);
	
	if (utils.hasRecords(fsTrans)) {
		for (var t=1; t <= fsTrans.getSize(); t++) {
			var rTrans = fsTrans.getRecord(t);
			rTrans.itemtransd_whseloc_id = rTrans.in_item_trans_detail_to_in_item_warehouse_location.whseloc_id;
		}
		
		databaseManager.saveData(fsTrans);
	}
}

/**
 * @param {String} [sItemID]
 *
 * @properties={typeid:24,uuid:"27CF7DAB-BA10-447B-B284-4773E225F139"}
 */
function deleteTransMissingBin(sItemID) {
	if (!sItemID) {
		forms["utils_dev_dtl"].elements.lblProcessing.text = "deleteTransMissingBin";
		globals["updateUI"]();
		application.sleep(100);
	}
	
	//  transaction for deleted item bin rec, and no trans bin to use. cant fix these recs - have to delete. ccorp had 3 of these rec for Cancel Stock Receipts
	var sSQL = "SELECT td.itemtransd_id \
				FROM in_item_trans_detail td \
				INNER JOIN in_item_trans_header th ON td.itemtransh_id = th.itemtransh_id \
				INNER JOIN in_transaction_type tt ON th.intranstype_id = tt.intranstype_id \
				INNER JOIN in_item i ON td.item_id = i.item_id \
				INNER JOIN in_warehouse tdw ON td.whse_id = tdw.whse_id \
				LEFT JOIN in_item_warehouse_location iwl ON td.itemwhseloc_id = iwl.itemwhseloc_id \
				WHERE \
					td.org_id = ? \
					AND td.itemtransd_whseloc_id IS NULL \
					AND iwl.itemwhseloc_id IS NULL \
					AND tdw.whse_enable_bin_locations = 1 \
					AND ISNULL(i.item_no_bin_location, 0) = 0 \
					AND i.itemtype_code NOT IN ('ZZ', 'SE', 'P') \
					AND tt.intranstype_adjustment_type = 'Q' ";
	var aArgs = [globals.org_id];
	
	if (sItemID) {
		sSQL += " AND td.item_id = ? ";
		aArgs.push(sItemID);
	}

	/***@type {JSFoundset<db:/avanti/in_item_trans_detail>} */
	var fsTrans = scopes.avDB.getFSFromSQL(sSQL, "in_item_trans_detail", [globals.org_id, sItemID]);
	
	if (utils.hasRecords(fsTrans)) {
		fsTrans.deleteAllRecords();
	}
}

/**
 * @param {String} [sItemID]
 *
 * @properties={typeid:24,uuid:"999408E5-B197-45BD-A716-FDB5109E9A2F"}
 */
function fixItemBinOnWrongItemWare(sItemID) {
	if (!sItemID) {
		forms["utils_dev_dtl"].elements.lblProcessing.text = "fixItemBinOnWrongItemWare";
		globals["updateUI"]();
		application.sleep(100);
	}
	
	// item bin rec attached to wrong item ware rec
	var sSQL = "SELECT iwl.itemwhseloc_id \
				FROM in_item_warehouse_location iwl \
				INNER JOIN in_item_warehouse iw ON iwl.itemwhse_id = iw.itemwhse_id \
				INNER JOIN in_warehouse_location wl ON iwl.whseloc_id = wl.whseloc_id \
				INNER JOIN in_item i ON iw.item_id = i.item_id \
				WHERE \
					iwl.org_id = ? \
					AND iw.whse_id != wl.whse_id \
					AND i.itemtype_code NOT IN ('ZZ', 'SE', 'P') ";
	var aArgs = [globals.org_id];
	
	if (sItemID) {
		sSQL += " AND iwl.item_id = ? ";
		aArgs.push(sItemID);
	}

	/***@type {JSFoundset<db:/avanti/in_item_warehouse_location>} */
	var fsItemBins = scopes.avDB.getFSFromSQL(sSQL, "in_item_warehouse_location", aArgs);
	
	if (utils.hasRecords(fsItemBins)) {
		var sRightWareSQL = "SELECT itemwhse_id FROM in_item_warehouse WHERE item_id = ? AND whse_id = ?";
		
		fsItemBins.getRecord(databaseManager.getFoundSetCount(fsItemBins));
		
		for (var i = fsItemBins.getSize(); i >= 1; i--) {
			var rItemBin = fsItemBins.getRecord(i);
			var uRightWareID = scopes.avDB.SQLQuery(sRightWareSQL, null, [rItemBin.item_id.toString(), rItemBin.in_item_warehouse_location_to_in_warehouse_location.whse_id.toString()]);
			
			if (uRightWareID) {
				if (sItemID) {
					rItemBin.itemwhse_id = application.getUUID(uRightWareID);
					databaseManager.saveData(rItemBin);
				}
				else {
					scopes.avDB.RunSQL("UPDATE in_item_warehouse_location SET itemwhse_id = ? WHERE itemwhseloc_id = ?", null, [uRightWareID, rItemBin.itemwhseloc_id.toString()]);
				}
			}
			else {
				if (sItemID) {
					fsItemBins.deleteRecord(i);
				}
				else {
					scopes.avDB.RunSQL("DELETE FROM in_item_warehouse_location WHERE itemwhseloc_id = ?", null, [rItemBin.itemwhseloc_id.toString()]);
				}
			}
		}
		
		if (!sItemID) {
			plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'in_item_warehouse_location');
			databaseManager.refreshRecordFromDatabase(fsItemBins, -1);
		}
	}
}

/**
 * @param {String} [sItemID]
 *
 * @properties={typeid:24,uuid:"A939424D-E8BB-4B13-8C2D-617DC6740B4B"}
 */
function fixTransItemBinDeleted(sItemID) {
	if (!sItemID) {
		forms["utils_dev_dtl"].elements.lblProcessing.text = "fixTransItemBinDeleted";
		globals["updateUI"]();
		application.sleep(100);
	}
	
	// transaction item doesnt match transaction bin item OR trans itemwhseloc_id refs deleted itemBin rec - get correct bin id
	var sSQL = "SELECT td.itemtransd_id \
				FROM in_item_trans_detail td \
				LEFT JOIN in_item_warehouse_location iwl ON td.itemwhseloc_id = iwl.itemwhseloc_id \
				INNER JOIN in_warehouse_location wl ON td.itemtransd_whseloc_id = wl.whseloc_id \
				INNER JOIN in_item_warehouse iw ON td.item_id = iw.item_id AND td.whse_id = iw.whse_id \
				INNER JOIN in_item_trans_header th ON td.itemtransh_id = th.itemtransh_id \
				INNER JOIN in_transaction_type tt ON th.intranstype_id = tt.intranstype_id \
				INNER JOIN in_item i ON td.item_id = i.item_id \
				WHERE \
					td.org_id = ? \
					AND (iwl.itemwhseloc_id IS NULL OR td.item_id != iwl.item_id) \
					AND i.itemtype_code NOT IN ('ZZ', 'SE', 'P') \
					AND tt.intranstype_adjustment_type = 'Q' ";
	var aArgs = [globals.org_id];
	
	if (sItemID) {
		sSQL += " AND (td.item_id = ? OR iwl.item_id = ?)";
		aArgs.push(sItemID);
		aArgs.push(sItemID);
	}
	
	/***@type {JSFoundset<db:/avanti/in_item_trans_detail>} */
	var fsTrans = scopes.avDB.getFSFromSQL(sSQL, "in_item_trans_detail", aArgs);
	var sRightBinSQL = "SELECT itemwhseloc_id FROM in_item_warehouse_location WHERE item_id = ? AND whseloc_id = ?";
	/***@type {JSFoundset<db:/avanti/in_item_warehouse_location>} */
	var rIWL;
	
	if (utils.hasRecords(fsTrans)) {
		fsTrans.getRecord(databaseManager.getFoundSetCount(fsTrans));
		
		for (var t = fsTrans.getSize(); t >= 1; t--) {
			var rTrans = fsTrans.getRecord(t);
			var uRightBinID = scopes.avDB.SQLQuery(sRightBinSQL, null, [rTrans.item_id.toString(), rTrans.itemtransd_whseloc_id.toString()]);
				
			if (uRightBinID) {
				rTrans.itemwhseloc_id = application.getUUID(uRightBinID);
			}
			else {
				var uWareID = scopes.avDB.SQLQuery("SELECT whse_id FROM in_warehouse_location WHERE whseloc_id = ?", null, [rTrans.itemtransd_whseloc_id.toString()]);
				var uItemWareID = scopes.avDB.SQLQuery("SELECT itemwhse_id FROM in_item_warehouse WHERE item_id = ? AND whse_id = ?", null, [rTrans.item_id.toString(), uWareID]); 

				if (uItemWareID) {
					rIWL = scopes.avDB.newRecord("in_item_warehouse_location");
					
					rIWL.item_id = rTrans.item_id;
					rIWL.whseloc_id = rTrans.itemtransd_whseloc_id;
					rIWL.itemwhse_id = uItemWareID;
					rIWL.itemwhseloc_active = 1;
					rIWL.whseloc_bin_location = rIWL.in_item_warehouse_location_to_in_warehouse_location.whseloc_bin_location;
					rIWL.sequence_nr = scopes.avDB.SQLQuery("SELECT MAX(sequence_nr) FROM in_item_warehouse_location WHERE itemwhse_id = ?", null, [rIWL.itemwhse_id.toString()]) + 1;
					rTrans.itemwhseloc_id = rIWL.itemwhseloc_id;
					
					databaseManager.saveData(rIWL);
				}
				// cant find the correct itemwhse_id to use, cant fix trans, have to delete it
				else {
					fsTrans.deleteRecord(t);
				}
			}
		}
		
		databaseManager.saveData(fsTrans);
	}
}

/**
 * @param {String} [sItemID]
 *
 * @properties={typeid:24,uuid:"5BADBE7F-**************-4430E051691F"}
 */
function fixTransWareDoesntMatchTransBinWare(sItemID) {
	if (!sItemID) {
		forms["utils_dev_dtl"].elements.lblProcessing.text = "fixTransWareDoesntMatchTransBinWare";
		globals["updateUI"]();
		application.sleep(100);
	}
	
	//  transaction whse_id doesnt match trans bin whse_id - use bin whse_id
	var sSQL = "SELECT itemtransd_id \
	 			FROM in_item_trans_detail td \
				INNER JOIN in_item_warehouse_location iwl ON td.itemwhseloc_id = iwl.itemwhseloc_id \
				INNER JOIN in_item_warehouse iw ON iwl.itemwhse_id = iw.itemwhse_id \
				INNER JOIN in_item_trans_header th ON td.itemtransh_id = th.itemtransh_id \
				INNER JOIN in_transaction_type tt ON th.intranstype_id = tt.intranstype_id \
				INNER JOIN in_item i ON td.item_id = i.item_id \
				WHERE \
					td.org_id = ? \
					AND td.whse_id != iw.whse_id \
					AND i.itemtype_code NOT IN ('ZZ', 'SE', 'P') \
					AND tt.intranstype_adjustment_type = 'Q' ";

	var aArgs = [globals.org_id];

	if (sItemID) {
		sSQL += " AND td.item_id = ? ";
		aArgs.push(sItemID);
	}

	/***@type {JSFoundset<db:/avanti/in_item_trans_detail>} */
	var fsTrans = scopes.avDB.getFSFromSQL(sSQL, "in_item_trans_detail", [globals.org_id, sItemID]);
	
	if (utils.hasRecords(fsTrans)) {
		for (var t=1; t <= fsTrans.getSize(); t++) {
			var rTrans = fsTrans.getRecord(t);
			rTrans.whse_id = rTrans.in_item_trans_detail_to_in_item_warehouse_location.in_item_warehouse_location_to_in_item_warehouse.whse_id;
		}
		
		databaseManager.saveData(fsTrans);
	}
}

/**
 * @param {String} [sItemID]
 *
 * @return
 * @properties={typeid:24,uuid:"EDB35A21-3A2C-44F2-9745-70FDC0DFA0FE"}
 */
function fixItemOHQs(sItemID) {
	var aArgs = [];

	// bin total doesnt match bin trans sum
	var sBinTransSQL = "SELECT DISTINCT i.item_id \
						FROM in_item_trans_detail td \
						INNER JOIN in_item_trans_header th ON td.itemtransh_id = th.itemtransh_id \
						INNER JOIN in_transaction_type tt on th.intranstype_id = tt.intranstype_id \
						INNER JOIN in_item_warehouse_location iwl ON td.itemwhseloc_id = iwl.itemwhseloc_id \
						INNER JOIN in_item_warehouse iw ON iwl.itemwhse_id = iw.itemwhse_id \
						INNER JOIN in_item i ON td.item_id = i.item_id \
						INNER JOIN in_warehouse w ON iw.whse_id = w.whse_id \
						WHERE i.org_id = ? AND tt.intranstype_adjustment_type = 'Q' AND i.itemtype_code NOT IN ('ZZ', 'SE', 'P') ";
	// ware total doesnt match ware trans sum
	var sWareTransSQL = "SELECT DISTINCT i.item_id \
						 FROM in_item_trans_detail td \
						 INNER JOIN in_item_trans_header th ON td.itemtransh_id = th.itemtransh_id \
						 INNER JOIN in_transaction_type tt on th.intranstype_id = tt.intranstype_id \
						 INNER JOIN in_item_warehouse iw ON td.item_id = iw.item_id AND td.whse_id = iw.whse_id \
						 INNER JOIN in_item i ON td.item_id = i.item_id \
						 INNER JOIN in_warehouse w ON iw.whse_id = w.whse_id \
						 WHERE i.org_id = ? AND tt.intranstype_adjustment_type = 'Q' AND i.itemtype_code NOT IN ('ZZ', 'SE', 'P') ";
	// ware total doesnt match bin sum
	var sWareBinSQL = "SELECT DISTINCT i.item_id \
					   FROM in_item_warehouse_location ib \
					   INNER JOIN in_item_warehouse iw ON ib.itemwhse_id = iw.itemwhse_id \
					   INNER JOIN in_item i ON ib.item_id = i.item_id \
					   INNER JOIN in_warehouse w ON iw.whse_id = w.whse_id \
					   WHERE i.org_id = ? AND w.whse_enable_bin_locations = 1 AND ISNULL(i.item_no_bin_location, 0) = 0 AND i.itemtype_code NOT IN ('ZZ', 'SE', 'P') ";
	// item total doesnt match ware sum
	var sItemWareSQL = "SELECT DISTINCT i.item_id \
						FROM in_item_warehouse iw \
						INNER JOIN in_item i ON iw.item_id = i.item_id \
						WHERE i.org_id = ? AND i.itemtype_code NOT IN ('ZZ', 'SE', 'P') ";
	
	if (sItemID) {
		sBinTransSQL += " AND i.item_id = ? ";
		sWareTransSQL += " AND i.item_id = ? ";
		sWareBinSQL += " AND i.item_id = ? ";
		sItemWareSQL += " AND i.item_id = ? ";
		
		aArgs = [globals.org_id, sItemID, globals.org_id, sItemID, globals.org_id, sItemID, globals.org_id, sItemID];
	}
	else {
		aArgs = [globals.org_id, globals.org_id, globals.org_id, globals.org_id];
	}
	
	// using ABS(x - y) instead of x != y because for some the nums were identical, but sql said they differed by a very small fraction for some reason
	sBinTransSQL += " GROUP BY i.item_id, iwl.whseloc_id, iwl.itemwhseloc_onhand_qty, iwl.itemwhseloc_openingbal_qty \
					  HAVING ABS(iwl.itemwhseloc_onhand_qty - iwl.itemwhseloc_openingbal_qty - SUM(td.itemtransd_qty)) >= 0.0001 ";
	sWareTransSQL += " GROUP BY i.item_id, w.whse_id, iw.itemwhse_onhand_qty, iw.itemwhse_openingbal_qty \
					   HAVING ABS(iw.itemwhse_onhand_qty - iw.itemwhse_openingbal_qty - SUM(td.itemtransd_qty)) >= 0.0001 ";
	sWareBinSQL += " GROUP BY i.item_id, w.whse_id, iw.itemwhse_onhand_qty, iw.itemwhse_openingbal_qty \
					 HAVING ABS(iw.itemwhse_onhand_qty - SUM(ib.itemwhseloc_onhand_qty)) >= 0.0001 ";
	sItemWareSQL += " GROUP BY i.item_id, i.item_onhand_qty \
					  HAVING ABS(i.item_onhand_qty - SUM(iw.itemwhse_onhand_qty)) >= 0.0001 ";	
	
	var sSQL = sBinTransSQL + " UNION " + sWareTransSQL + " UNION " + sWareBinSQL + " UNION " + sItemWareSQL;
	/***@type {JSFoundset<db:/avanti/in_item>} */
	var fsItem = scopes.avDB.getFSFromSQL(sSQL, "in_item", aArgs);
	var nNumItemsFixed = 0;
	
	if (utils.hasRecords(fsItem)) {
		var nNumItems = databaseManager.getFoundSetCount(fsItem);
		
		for (var i = 1; i <= fsItem.getSize(); i++) {
			var rItem = fsItem.getRecord(i);
			var nPct = scopes.avMath.roundNumber(i / nNumItems * 100, 2); 
			
			if (!sItemID) {
				forms["utils_dev_dtl"].elements.lblProcessing.text = "Updating Item: " + rItem.item_code + ". Complete: " + nPct + "%";
				globals["updateUI"]();
				application.sleep(100);
			}
			
			forms["utils_dev_dtl"].resetOnHandBasedOnTransactions(rItem);
		}
		
		nNumItemsFixed = fsItem.getSize();
	}
	
	return nNumItemsFixed;
}

/**
 * @public 
 * 
 * @param {UUID} uItemID
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"30EAB969-C169-47B3-9EC7-0B5E373ACBEA"}
 */
function isItemMissingFifoRecs(uItemID) {
	var bIsItemMissingFifoRecs = false;
	
	if (uItemID) {
		var sSQL = "SELECT COUNT(TD.itemtransd_id) \
					FROM in_item_trans_detail TD \
					INNER JOIN in_item I ON TD.item_id = I.item_id \
					INNER JOIN in_item_trans_header TH ON TD.itemtransh_id = TH.itemtransh_id \
					INNER JOIN in_transaction_type TT ON TH.intranstype_id = TT.intranstype_id \
					LEFT OUTER JOIN in_item_warehouse iw on td.item_id = iw.item_id AND td.whse_id = iw.whse_id \
					LEFT OUTER JOIN in_item_warehouse_location iwl on TD.itemwhseloc_id = iwl.itemwhseloc_id \
					LEFT OUTER JOIN (SELECT item_id, SUM(fifo_qty_remaining) [TOT] FROM in_item_fifo GROUP BY item_id) fifoItem ON TD.item_id = fifoItem.item_id \
					LEFT OUTER JOIN (SELECT itemwhse_id, SUM(fifo_qty_remaining) [TOT] FROM in_item_fifo GROUP BY itemwhse_id) fifoWare ON iw.itemwhse_id = fifoWare.itemwhse_id \
					LEFT OUTER JOIN (SELECT itemwhseloc_id, SUM(fifo_qty_remaining) [TOT] FROM in_item_fifo GROUP BY itemwhseloc_id) fifoBin ON TD.itemwhseloc_id = fifoBin.itemwhseloc_id \
					WHERE \
						I.itemtype_code <> ? \
						AND TT.intranstype_adjustment_type = ? \
						AND (TT.intranstype_effect_on_qty = '+' OR TD.itemtransd_qty > 0) \
						AND NOT EXISTS (SELECT itemtransd_id FROM in_item_fifo WHERE itemtransd_id = TD.itemtransd_id) \
						AND (ISNULL(I.item_onhand_qty, 0) != ISNULL(fifoItem.TOT, 0) \
							OR ISNULL(iw.itemwhse_onhand_qty, 0) != ISNULL(fifoWare.TOT, 0) \
							OR ISNULL(iwl.itemwhseloc_onhand_qty, 0) != ISNULL(fifoBin.TOT, 0)) \
						AND I.item_id = ?";
		
		bIsItemMissingFifoRecs = (scopes.avDB.SQLQuery(sSQL, null, [scopes.avUtils.ITEM_TYPE.OutsourcedService, ADJUSTMENT_TYPE.Quantity, uItemID.toString()]) > 0);
		
		sSQL="select count(fifor_id) from in_item_fifo_reserved where fifo_id in (select fifo_id from in_item_fifo where item_id= ?) and clc_source_doc_status = 'Shipped'";
		
		bIsItemMissingFifoRecs = bIsItemMissingFifoRecs || (scopes.avDB.SQLQuery(sSQL, null, [uItemID.toString()]) > 0);
		
	}
	
	return bIsItemMissingFifoRecs;
}

/**
 * @public 
 * 
 * @param {UUID} uItemID
 * @param {String} sCallingProgram
 *
 * @properties={typeid:24,uuid:"DD885E55-0D79-4EAB-BAAC-6EB191EB53D8"}
 */
function fixMissingFifoRecs(uItemID, sCallingProgram) {
	if (uItemID && isItemMissingFifoRecs(uItemID)) {
        scopes["avInventoryRebuilds"].rebuildFifoTables(uItemID, true);
		scopes.avUtils.devLog("SL-23545", sCallingProgram + ".fixMissingFifoRecs for item: " + uItemID, true);
	}	
}

/**
 * I extracted this code from avWirelessInventory.InventoryAdjustment() then modified
 * 
 * @public 
 * 
 * @param {UUID|String} uItemID
 * @param {UUID} uItemWhseID
 * @param {String} sLocation
 * @param {String} sProject
 * @param {Number} nQuantityOnHand
 * @param {Number} nQuantityBy
 * @param {String} sReferenceNumber
 * @param {String} sAdjustmentType
 * @param {String} sRollNumber
 * @param {UUID} uMaterialCostId
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"2CEE8565-AFBD-4CB6-A475-374BAE710D53"}
 */
function adjustInventory(uItemID, uItemWhseID, sLocation, sProject, nQuantityOnHand, nQuantityBy, sReferenceNumber, sAdjustmentType, sRollNumber, uMaterialCostId) {
	try {
        var item_uom_id = null;
        var glacct_id = null;
        var itemroll_id = null;
        var item_track_rolls = null;
        var item_itemwhse_avg_cost_TO = 0;
        var intranstype_id = null;
        
        uItemID = uItemID.toString();

        //get additional needed information for item
        var sSQL = "SELECT item.item_standard_uom_id, item.item_track_rolls \
		            FROM in_item AS item  \
		            WHERE item.item_id = ? AND item.org_id = ?";
        var aArgs = [uItemID, globals.org_id];
        var ds = scopes.avDB.getDataset(sSQL, aArgs);
      
        if (ds.getMaxRowIndex() == 0) {
            return false;
        }

        ds.rowIndex = 1;
        item_uom_id = ds['item_standard_uom_id'];
        item_track_rolls = ds['item_track_rolls'];

        //get additional needed information for item_glacct_id
        aArgs = [sAdjustmentType, globals.org_id];
        ds = scopes.avDB.getDataset("SELECT glacct_id FROM in_adjustment_types WHERE adjtype_code = ? AND org_id=?", aArgs);

        if (ds.getMaxRowIndex() > 0) {
            glacct_id = ds.getValue(1, 1);
        }

        if (item_track_rolls && sRollNumber) {
            aArgs = [uItemID, sRollNumber, scopes.globals.org_id];
            ds = scopes.avDB.getDataset("SELECT initemroll_id FROM in_item_roll WHERE item_id = ? AND initemroll_roll_number=? AND org_id = ?", aArgs);

            if (ds.getMaxRowIndex() == 0) {
                return false;
            }

            ds.rowIndex = 1;
            itemroll_id = ds['initemroll_id'];
        }

        if (item_track_rolls && !itemroll_id) {
            return false;
        }

        var intranstype_trans_code = 'QA';
        var locationBin_code_TO = sLocation;
        var intraneh_whse_id_TO = null;
        var intraned_whseloc_id_TO = null;
        var intraned_itemwhseloc_id_TO = null;
        var item_itemwhse_id_TO = null;        

        intranstype_id = scopes.avInv.getTransactionTypeUUID("IT", intranstype_trans_code);
        
		if (sLocation) {
			sSQL = "SELECT in_warehouse_location.whse_id, in_warehouse_location.whseloc_id, in_item_warehouse_location.itemwhseloc_id, in_item_warehouse.itemwhse_avg_cost, in_item_warehouse.itemwhse_id \
		            FROM in_warehouse_location \
		            LEFT JOIN in_item_warehouse_location ON in_item_warehouse_location.whseloc_id=in_warehouse_location.whseloc_id AND in_item_warehouse_location.item_id = ? \
		            INNER JOIN in_item_warehouse ON in_item_warehouse.whse_id = in_warehouse_location.whse_id AND in_item_warehouse.item_id = ? \
		            WHERE \
		            	in_warehouse_location.whseloc_bin_location = ? \
		            	AND in_warehouse_location.org_id = ?";
	        aArgs = [uItemID, uItemID, locationBin_code_TO, scopes.globals.org_id];
	        
	        ds = scopes.avDB.getDataset(sSQL, aArgs);
	        if (ds.getMaxRowIndex() == 0) {
	            return false;
	        }
	        ds.rowIndex = 1;
	        
	        intraneh_whse_id_TO = ds['whse_id'];
	        intraned_whseloc_id_TO = ds['whseloc_id'];
	        item_itemwhse_id_TO = ds['itemwhse_id'];
	        item_itemwhse_avg_cost_TO = ds['itemwhse_avg_cost'];

	        if (ds['itemwhseloc_id'] != null) {
	            intraned_itemwhseloc_id_TO = ds['itemwhseloc_id'];
	        }
	        // else - there is no record in in_item_warehouse_location for specified location.
	        // most likely Default location was specified which has never been used before => create a new record
	        else {
	            /** @type {JSFoundSet<db:/avanti/in_item_warehouse_location>} */
	            var fsItemWhseLocation = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_warehouse_location');
	            
	            fsItemWhseLocation.newRecord();
	            fsItemWhseLocation.itemwhse_id = item_itemwhse_id_TO;
	            fsItemWhseLocation.whseloc_id = intraned_whseloc_id_TO;
	            fsItemWhseLocation.itemwhseloc_onhand_qty = 0;
	            fsItemWhseLocation.itemwhseloc_unavailible_qty = 0;
	            fsItemWhseLocation.itemwhseloc_unusable_qty = 0;
	            fsItemWhseLocation.sequence_nr = scopes.avInv.getNextItemWarehouseLocationSequenceNr(fsItemWhseLocation.itemwhse_id);;
	            fsItemWhseLocation.item_id = uItemID;

	            databaseManager.saveData(fsItemWhseLocation);
	            intraned_itemwhseloc_id_TO = fsItemWhseLocation.itemwhseloc_id.toString();
	        }
		}
		else if (uItemWhseID) {
			sSQL = "SELECT whse_id, itemwhse_avg_cost FROM in_item_warehouse WHERE itemwhse_id = ?";
	        aArgs = [uItemWhseID.toString()];
	        
	        ds = scopes.avDB.getDataset(sSQL, aArgs);
	        if (ds.getMaxRowIndex() == 0) {
	            return false;
	        }
	        ds.rowIndex = 1;
	        
	        intraneh_whse_id_TO = ds['whse_id'];
	        intraned_whseloc_id_TO = null;
	        item_itemwhse_id_TO = uItemWhseID;
	        item_itemwhse_avg_cost_TO = ds['itemwhse_avg_cost'];
		}
		else {
            return false;
		}
        
        //If not g/l account by adjustment type, then use default logic.
        if (!glacct_id) {
            glacct_id = scopes.avAccounting.getDefaultOffsetGLAccount(uItemID, intraneh_whse_id_TO, intranstype_id);
        }

        //********************************** HEADER TABLE
        /** @type {JSFoundSet<db:/avanti/in_trans_entry_header>} */
        var fsTransEntryHeader = forms['in_trans_entry_header'].foundset;
        /** @type {JSRecord<db:/avanti/in_trans_entry_header>} */
        var rTransEntryHeader = fsTransEntryHeader.getRecord(fsTransEntryHeader.newRecord());
        rTransEntryHeader.org_id = scopes.globals.org_id;
        rTransEntryHeader.intranstype_id = intranstype_id;
        rTransEntryHeader.intraneh_reference = sReferenceNumber;
        rTransEntryHeader.intraneh_whse_id = intraneh_whse_id_TO;
        rTransEntryHeader.intraneh_createdby_empl_id = globals.avBase_employeeUUID;
        rTransEntryHeader.intraneh_status = 0;
        rTransEntryHeader.intraneh_posted_date = null;
        rTransEntryHeader.intraneh_transaction_no = globals.getNextItemTransactionNumber('IT');
        rTransEntryHeader.intraneh_whse_id_transfer_to = null;
        rTransEntryHeader.intraneh_status_field = 'O';
        rTransEntryHeader.itreg_id = null;
        rTransEntryHeader.intraneh_complete = null;
        rTransEntryHeader.intraneh_desc = null
        rTransEntryHeader.intraneh_expected_date = null;
        rTransEntryHeader.intraneh_reference_pr = null;

        //********************************** DETAIL TABLE
        var rTransEntryDetail = rTransEntryHeader.in_trans_entry_header_to_in_trans_entry_detail.getRecord(rTransEntryHeader.in_trans_entry_header_to_in_trans_entry_detail.newRecord());
        rTransEntryDetail.org_id = scopes.globals.org_id;
        rTransEntryDetail.item_id = uItemID;
        rTransEntryDetail.intraned_trans_qty = nQuantityBy;
        rTransEntryDetail.uom_id = item_uom_id;
        rTransEntryDetail.intraned_cost_amt = item_itemwhse_avg_cost_TO;
        rTransEntryDetail.intraned_cost_uom_id = item_uom_id;
        rTransEntryDetail.intraned_cost_uom_cost_factor = 1;
        rTransEntryDetail.intraned_comment = null;

        rTransEntryDetail.intraned_extended_cost = nQuantityBy * item_itemwhse_avg_cost_TO;
        rTransEntryDetail.sequence_nr = 1;
        rTransEntryDetail.intraned_whse_id_transfer_from = null;
        rTransEntryDetail.intraned_whse_id = intraneh_whse_id_TO;
        rTransEntryDetail.intraned_cost_entere = item_itemwhse_avg_cost_TO;
        rTransEntryDetail.intraned_itemwhseloc_id_from = null;
        rTransEntryDetail.intraned_itemwhseloc_id = intraned_itemwhseloc_id_TO;
        rTransEntryDetail.intraned_ui_bin_id = rTransEntryDetail.intraned_itemwhseloc_id;
        rTransEntryDetail.intraned_whseloc_id_from = null;
        rTransEntryDetail.intraned_whseloc_id = intraned_whseloc_id_TO;
        rTransEntryDetail.itemwhse_id = item_itemwhse_id_TO;

        rTransEntryDetail.glacct_id = glacct_id;
        rTransEntryDetail.rollconv_id = null;
        rTransEntryDetail.intraned_base_cost_adj = null;

        rTransEntryDetail.itemroll_id = itemroll_id;

        rTransEntryDetail.intraned_balance = null; // Tim's comment: This is only for Pending receipts
        rTransEntryDetail.intraned_received_to_date = null;
        rTransEntryDetail.jcm_id = uMaterialCostId;
        
        // sl-20590 - in_item_project recs were being created with an empty string custproj_desc
        sProject = scopes.avText.trim(sProject);
        
		if (sProject) {
	        rTransEntryDetail.custproj_desc = sProject;
		}
		else {
	        rTransEntryDetail.custproj_desc = null;
		}

        databaseManager.saveData();
        
        // sl-23545 - have to call adjustTransactionFIFO() to create in_item_fifo_reserved
        var sEffectOnQty = rTransEntryHeader.in_trans_entry_header_to_in_transaction_type.intranstype_effect_on_qty;
        adjustTransactionFIFO(rTransEntryHeader.in_trans_entry_header_to_in_trans_entry_detail, sEffectOnQty);
        return scopes.avInv.buildTempTransactionTables(rTransEntryHeader);
	}
	catch (ex) {
        return false;
	}
}

/**
 * Moved this here from avanti_inventory/globals.js because it wasnt accessible from everywhere there 
 * 
 * @public 
 * 
 * Adjusts the fifo records based on the transaction detail
 * @param {JSFoundset<db:/avanti/in_trans_entry_detail>} fsTransEntryDetails
 * @param {String} sEffectOnQty
 * @param {Boolean} [bMobile]
 *
 * @properties={typeid:24,uuid:"6D2D574B-19C0-4676-B40D-027EBEB58A02"}
 */
function adjustTransactionFIFO(fsTransEntryDetails, sEffectOnQty, bMobile) {
    for (var i = 1; i <= fsTransEntryDetails.getSize(); i++) {
        var rTransDetail = fsTransEntryDetails.getRecord(i);
        var rTransHeader = rTransDetail.in_trans_entry_detail_to_in_trans_entry_header.getRecord(1);
        var rTransType = rTransHeader.in_trans_entry_header_to_in_transaction_type.getRecord(1);
        
        if (sEffectOnQty == '-' || rTransDetail.intraned_trans_qty < 0 || rTransType.intranstype_trans_code == scopes.avInv.TRANSACTION_TYPE.RollBinTransfer) {

            /** @type {JSFoundSet<db:/avanti/in_item_fifo>} */
            var fsFifo;

            if (utils.hasRecords(rTransDetail.in_trans_entry_detail_to_in_item) 
                && rTransDetail.in_trans_entry_detail_to_in_item.itemtype_code != scopes.avUtils.ITEM_TYPE.Service    
                && rTransDetail.in_trans_entry_detail_to_in_item.itemtype_code != scopes.avUtils.ITEM_TYPE.Product  
                && rTransDetail.in_trans_entry_detail_to_in_item.item_no_bin_location != 1 
                && utils.hasRecords(rTransDetail.in_trans_entry_detail_to_in_warehouse) 
                && rTransDetail.in_trans_entry_detail_to_in_warehouse.whse_enable_bin_locations == 1) {

                /** @type {JSFoundSet<db:/avanti/in_item_warehouse_location>} */
                var fsInItemWhseLoc;

                if (rTransDetail.intraned_whseloc_id_from) {
                    fsInItemWhseLoc = rTransDetail.in_trans_entry_detail_to_in_item_warehouse_location$from;
                }
                else if (rTransDetail.intraned_whseloc_id) {
                    fsInItemWhseLoc = rTransDetail.in_trans_entry_detail_to_in_item_warehouse_location;
                }
         

                if (utils.hasRecords(fsInItemWhseLoc)) {
                    if (globals.nav.mode == 'edit' && utils.hasRecords(rTransDetail.in_trans_entry_detail_to_in_item_fifo_reserved)) {
                        scopes.avInv.deleteFifoReservedRecords(rTransDetail.in_trans_entry_detail_to_in_item_fifo_reserved);
                    }
                    
                    fsFifo = fsInItemWhseLoc.in_item_warehouse_location_to_in_item_fifo;
                    if (fsFifo && fsFifo.getSize() > 0) {
                        fsFifo.sort('fifo_date asc, fifo_qty_remaining asc');
                        var nTransQtyLeft = Math.abs(rTransDetail.intraned_trans_qty);
                        var bProjectInventory = scopes.avInv.doesItemUseProjectInventory(rTransDetail.item_id);
                        
                        scopes.avInv.reserveQtyForFifo(nTransQtyLeft, fsFifo, scopes.avUtils.FIFO_SOURCE_TYPE.ItemTransaction, rTransDetail.intraned_id, null, bProjectInventory, rTransDetail.custproj_desc, null, rTransDetail.initemroll_id);
                    }
                }
            }

        }
        else if (!bMobile && globals.nav.mode == 'edit' && utils.hasRecords(rTransDetail.in_trans_entry_detail_to_in_item_fifo_reserved)) {
        	scopes.avInv.deleteFifoReservedRecords(rTransDetail.in_trans_entry_detail_to_in_item_fifo_reserved);
        }
    }
}

/**
 * THIS WAS TAKEN FROM THE CODE THAT RUNS WHEN RIGHT CLICKING ON THE FIELD IN ITEM STATISTICS
 * 
 * @public 
 * 
 * @param {JSRecord<db:/avanti/in_item>} rItem
 * 
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"602145F8-DA36-4D70-8F74-11595255FD92"}
 */
function getItemTotalCommittedQty(rItem) {
	var nQty = 0;
	
	if (rItem) {
	    var sSQL = "SELECT SUM(ROUND(in_committment.cur_qty_committed, ?))\
					FROM sa_order_revision_header \
		    		INNER JOIN in_committment \
		    		INNER JOIN sa_order_revd_item ON in_committment.ordrevditem_id = sa_order_revd_item.ordrevditem_id \
					INNER JOIN sa_order_revision_detail ON sa_order_revd_item.ordrevd_id = sa_order_revision_detail.ordrevd_id \
						ON sa_order_revision_header.ordrevh_id = sa_order_revision_detail.ordrevh_id \
			        INNER JOIN sa_order ON sa_order_revision_header.ordh_id = sa_order.ordh_id \
		            WHERE in_committment.org_id = ? \
					   AND sa_order.ordh_document_type = 'ORD' \
					   AND in_committment.item_id = ? \
					   AND in_committment.cur_qty_committed > 0 \
					   AND sa_order_revision_header.ordrevh_revision = 0\
					   AND sa_order_revision_header.ordrevh_order_status NOT IN (?, ?, ?)";
		var aArgs = [rItem.item_decimal_places, globals.org_id, rItem.item_id.toString(), scopes.avUtils.ORDER_STATUS.Cancelled, scopes.avUtils.ORDER_STATUS.Completed, scopes.avUtils.ORDER_STATUS.Incomplete];
		
		nQty = scopes.avDB.SQLQuery(sSQL, null, aArgs);
		
		if (!nQty) {
			nQty = 0;
		}
	}
	
	return nQty;
}

/**
 * THIS WAS TAKEN FROM THE CODE THAT RUNS WHEN RIGHT CLICKING ON THE FIELD IN ITEM STATISTICS
 * 
 * @public 
 * 
 * @param {JSRecord<db:/avanti/in_item>} rItem
 * 
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"8C8CAEDB-6A4C-48B8-9C5A-24F9970526DD"}
 */
function getItemTotalReservedQty(rItem) {
	var nQty = 0;
	
	if (rItem) {
	    var sSameUOMSelect = "SELECT SUM(ROUND(ordrevditem_reserved_qty, ?))";
	    var sDiffUOMSelect = "SELECT ordrevditem_id";
	    var sSQL = " FROM sa_order_revd_item \
		    		INNER JOIN sa_order_revision_detail ON sa_order_revd_item.ordrevd_id = sa_order_revision_detail.ordrevd_id \
		    		INNER JOIN sa_order_revision_header ON sa_order_revision_detail.ordrevh_id = sa_order_revision_header.ordrevh_id \
		    		INNER JOIN sa_order ON sa_order.ordh_id = sa_order_revision_header.ordh_id \
	                WHERE \
	                   sa_order_revd_item.org_id = ? \
	                   AND sa_order_revd_item.item_id = ? \
	                   AND sa_order_revd_item.ordrevditem_reserved_qty > 0 \
	                   AND sa_order_revision_header.ordrevh_revision = 0 \
	                   AND sa_order.ordh_document_type = 'ORD' \
	                   AND sa_order_revision_header.ordrevh_order_status NOT IN (?, ?, ?) ";
	    var sSameUOMCondition = "AND ordrevditem_stock_uom_id = ordrevditem_est_uom_id";
	    var sDiffUOMCondition = "AND ordrevditem_stock_uom_id != ordrevditem_est_uom_id";
		var aArgs = [rItem.item_decimal_places, globals.org_id, rItem.item_id.toString(), scopes.avUtils.ORDER_STATUS.Cancelled, scopes.avUtils.ORDER_STATUS.Completed, scopes.avUtils.ORDER_STATUS.Incomplete];
		var aArgs2 = [globals.org_id, rItem.item_id.toString(), scopes.avUtils.ORDER_STATUS.Cancelled, scopes.avUtils.ORDER_STATUS.Completed, scopes.avUtils.ORDER_STATUS.Incomplete];
		
		nQty = scopes.avDB.SQLQuery(sSameUOMSelect + sSQL + sSameUOMCondition, null, aArgs);
		
		/***@type {JSFoundset<db:/avanti/sa_order_revd_item>} */
		var fsRevDItem = scopes.avDB.SQLQuery(sDiffUOMSelect + sSQL + sDiffUOMCondition, null, aArgs2);
		
		// if rRevDItem uses different est and stock uoms then we cant use sql to sum it we need to use calculated col ordrevditem_reservedqty_uomstk, which does conversion
		if (fsRevDItem) {
			for (var i = 1; i <= fsRevDItem.getSize(); i++) {
				nQty += scopes.avMath.roundNumber(fsRevDItem.getRecord(i).ordrevditem_reservedqty_uomstk, rItem.item_decimal_places);
			}
		}
		
		if (!nQty) {
			nQty = 0;
		}
	}
	
	return nQty;
}

/**
 * THIS WAS TAKEN FROM THE CODE THAT RUNS WHEN RIGHT CLICKING ON THE FIELD IN ITEM STATISTICS
 * 
 * @public 
 * 
 * @param {JSRecord<db:/avanti/in_item>} rItem
 * 
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"CC963321-AF0B-4D0B-840B-0BC6AA7BD506"}
 */
function getItemTotalBackorderQty(rItem) {
	var nQty = 0;
	
	if (rItem) {
	    var sSameUOMSelect = "SELECT SUM(ROUND(ordrevditem_backorder_qty, ?))";
	    var sDiffUOMSelect = "SELECT ordrevditem_id";
	    var sSQL = " FROM sa_order_revd_item \
		    	    INNER JOIN sa_order_revision_detail ON sa_order_revd_item.ordrevd_id = sa_order_revision_detail.ordrevd_id \
					INNER JOIN sa_order_revision_header ON sa_order_revision_detail.ordrevh_id = sa_order_revision_header.ordrevh_id \
					INNER JOIN sa_order ON sa_order_revision_header.ordh_id = sa_order.ordh_id \
	                WHERE \
	                   sa_order_revd_item.org_id = ? \
	                   AND sa_order.ordh_document_type = 'ORD' \
	                   AND sa_order_revd_item.item_id = ? \
	                   AND sa_order_revd_item.ordrevditem_backorder_qty > 0 \
	                   AND sa_order_revision_header.ordrevh_revision = 0 \
	                   AND sa_order_revision_header.ordrevh_order_status NOT IN (?, ?, ?) ";
	    var sSameUOMCondition = "AND ordrevditem_stock_uom_id = ordrevditem_est_uom_id";
	    var sDiffUOMCondition = "AND ordrevditem_stock_uom_id != ordrevditem_est_uom_id";
		var aArgs = [rItem.item_decimal_places, globals.org_id, rItem.item_id.toString(), scopes.avUtils.ORDER_STATUS.Cancelled, scopes.avUtils.ORDER_STATUS.Completed, scopes.avUtils.ORDER_STATUS.Incomplete];
		var aArgs2 = [globals.org_id, rItem.item_id.toString(), scopes.avUtils.ORDER_STATUS.Cancelled, scopes.avUtils.ORDER_STATUS.Completed, scopes.avUtils.ORDER_STATUS.Incomplete];
		
		nQty = scopes.avDB.SQLQuery(sSameUOMSelect + sSQL + sSameUOMCondition, null, aArgs);
		
		/***@type {JSFoundset<db:/avanti/sa_order_revd_item>} */
		var fsRevDItem = scopes.avDB.SQLQuery(sDiffUOMSelect + sSQL + sDiffUOMCondition, null, aArgs2);
		
		// if rRevDItem uses different est and stock uoms then we cant use sql to sum it we need to use calculated col ordrevditem_backordqty_uomstk, which does conversion
		if (fsRevDItem) {
			for (var i = 1; i <= fsRevDItem.getSize(); i++) {
				nQty += scopes.avMath.roundNumber(fsRevDItem.getRecord(i).ordrevditem_backordqty_uomstk, rItem.item_decimal_places);
			}
		}
		
		if (!nQty) {
			nQty = 0;
		}
	}
	
	return nQty;
}

/**
 * THIS WAS TAKEN FROM THE CODE THAT RUNS WHEN RIGHT CLICKING ON THE FIELD IN ITEM STATISTICS
 * 
 * @public 
 * 
 * @param {JSRecord<db:/avanti/in_item>} rItem
 * 
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"7E07FC7D-F5C3-4AD5-B9DA-265B4D684CE0"}
 */
function getItemTotalExpectedInQty(rItem) {
	var nQty = 0;
	
	if (rItem) {
	    var sSQL = "SELECT SUM(ROUND(detail_balance, ?)) \
	    			FROM _v_in_item_expected_in_details d \
	    			INNER JOIN in_warehouse w ON d.whse_id = w.whse_id \
	                WHERE \
	                	(d.org_id = ?) \
	                	AND (d.item_id = ?) ";
		var aArgs = [rItem.item_decimal_places, globals.org_id, rItem.item_id.toString()];
		
		nQty = scopes.avDB.SQLQuery(sSQL, null, aArgs);
		
		if (!nQty) {
			nQty = 0;
		}
	}
	
	return nQty;
}

/**
 * THIS RECALCS THE ITEM STATISTICS HEADER COLS FROM THE DATA THAT APPEARS WHEN RIGHT CLICKING ON THE FIELD
 * 
 * @public 
 * 
 * @param {JSRecord<db:/avanti/in_item>} rItem
 *
 * @properties={typeid:24,uuid:"25796CD6-718B-44B3-9E4D-6A25741D0A05"}
 */
function recalcItemStatistics(rItem) {
	if (rItem) {
		scopes.avUtils.stopWatchStart("recalcItemStatistics");
		
		rItem.item_committed_qty = getItemTotalCommittedQty(rItem);
		rItem.item_reserved_qty = getItemTotalReservedQty(rItem);
		rItem.item_backord_qty = getItemTotalBackorderQty(rItem);
		rItem.item_onpo_qty = getItemTotalExpectedInQty(rItem);
		
		databaseManager.saveData(rItem);
		
		scopes.avUtils.devOutputTime("recalcItemStatistics")
	}
}

/**
 * @public 
 * 
 * @param {UUID} uItemID
 *
 * @properties={typeid:24,uuid:"9C581EF6-5423-4170-B241-CF30739611E5"}
 */
function recalcItemProjects(uItemID) {
	if (uItemID) {
		/**@type {JSRecord<db:/avanti/in_item>} */
		var rItem = scopes.avDB.getRec("in_item", ["item_id"], [uItemID]);
		
		if (rItem && rItem.in_item_to_in_item_project) {
			for (var i = 1; i <= rItem.in_item_to_in_item_project.getSize(); i++) {
				var rItemProject = rItem.in_item_to_in_item_project.getRecord(i);
				
				recalcItemProject(rItemProject);
			}
		}
	}
}

/**
 * @public  
 * 
 * @param {JSRecord<db:/avanti/in_item_project>} rItemProject
 *
 * @properties={typeid:24,uuid:"1B7AD91F-C89C-4FE0-A770-9FA3154FD345"}
 */
function recalcItemProject(rItemProject) {
	if (rItemProject) {
		var sSQL = "SELECT SUM(td.itemtransd_qty) \
					FROM in_item_trans_detail td \
					INNER JOIN in_item_trans_header th ON td.itemtransh_id = th.itemtransh_id \
					INNER JOIN in_transaction_type tt ON th.intranstype_id = tt.intranstype_id \
					WHERE \
						td.item_id = ? \
						AND tt.intranstype_adjustment_type = 'Q' ";
		var aArgs = [rItemProject.item_id.toString()];

		if (rItemProject.custproj_desc) {
			sSQL += "AND custproj_desc = ? ";
			aArgs.push(rItemProject.custproj_desc);
		}
		else {
			sSQL += "AND custproj_desc IS NULL ";
		}
		
		// bin
		if (rItemProject.itemwhseloc_id) {
			sSQL += "AND td.itemwhseloc_id = ? ";
			aArgs.push(rItemProject.itemwhseloc_id.toString());
		}
		// warehouse
		else if (utils.hasRecords(rItemProject.in_item_project_to_in_item_warehouse)) {
			sSQL += "AND whse_id = ? ";
			aArgs.push(rItemProject.in_item_project_to_in_item_warehouse.whse_id.toString());
		}
		
		var nSumTransQty = scopes.avDB.SQLQuery(sSQL, null, aArgs);
		
		if (!nSumTransQty) {
			nSumTransQty = 0;
		}
		
		rItemProject.ip_qty_on_hand = nSumTransQty;
		
		databaseManager.saveData(rItemProject);
	}
}

/**
 * @public  
 * 
 * @param {UUID} [uItemID]
 *
 * @properties={typeid:24,uuid:"D5BFF37D-A575-4164-A49F-19AEF7B00329"}
 */
function fixItemProjectOnHandQtyFromTransactions(uItemID) {
	var uDebugItemID = null; // "37DCA6D2-8A46-4393-8501-A36386F165A2";
	
	if (application.isInDeveloper() && uDebugItemID && !uItemID) {
		uItemID = uDebugItemID;
	}
	
	// Fix Duplicate in_item_project Records
	if (uItemID) {
		var sItemCode = scopes.avDB.SQLQuery("SELECT item_code FROM in_item WHERE item_id = ?", null, [uItemID.toString()]);
		
		if (sItemCode) {
			scopes.avUpdates.updates_SL21688(globals.org_id, sItemCode, true);
		}
	}
	else {
		scopes.avUpdates.updates_SL21688(globals.org_id, null, true);
	}

	// delete project recs for which there are no trans
	deleteProjectRecsWithNoTrans();
	
	fixBins();
	fixWares();
	fixItems();
	
	// above funcs (fixBins, fixWares, fixItems) dont fix a problem where we have a item-project rec at the ware or item level, but none at the bin level. call recalcItemProjects to fix this prob
	if (uItemID) {
		recalcItemProjects(uItemID);
	}
	else {
	    var dsProjectItems = scopes.avDB.getDataset("SELECT DISTINCT item_id FROM in_item_project WHERE org_id = ?", [globals.org_id]);
	    
		if (dsProjectItems) {
			for (var i = 1; i <= dsProjectItems.getMaxRowIndex(); i++) {
				recalcItemProjects(dsProjectItems.getValue(i, 1));
			}
		}
	}
	
	if (!uItemID) {
		scopes.avText.showInfo("The utility has finished running. Please logout then back in to see the changes.", true);
	}
	
	function deleteProjectRecsWithNoTrans() {
		var sSQL = "SELECT p.ip_id \
					FROM in_item_project p \
					LEFT JOIN in_item_trans_detail td ON \
						p.item_id = td.item_id \
						AND p.itemwhseloc_id = td.itemwhseloc_id \
						AND ISNULL(p.custproj_desc, '') = ISNULL(td.custproj_desc, '') \
					WHERE \
						p.org_id = ? \
						AND p.itemwhseloc_id IS NOT NULL \
						AND td.itemtransd_id IS NULL ";
						
	    var aArgs = [globals.org_id];
	    
		if (uItemID) {
			sSQL += " AND p.item_id = ? ";
			aArgs.push(uItemID.toString());
		}
		
		/***@type {JSFoundSet<db:/avanti/in_item_project>} ***/
		var fs = scopes.avDB.getFSFromSQL(sSQL, "in_item_project", aArgs);
		
		if (utils.hasRecords(fs)) {
			fs.deleteAllRecords();
		}
	}
	
	function fixBins() {
	    var sqlTrans = "SELECT td.item_id, iwl.itemwhse_id, td.itemwhseloc_id, td.custproj_desc, p.ip_qty_on_hand [project qty], SUM(td.itemtransd_qty) [trans qty], p.ip_id \
						FROM in_item_trans_detail td \
						INNER JOIN in_item_trans_header th ON td.itemtransh_id = th.itemtransh_id \
						INNER JOIN in_transaction_type tt ON th.intranstype_id = tt.intranstype_id \
						INNER JOIN in_item i ON td.item_id = i.item_id \
						INNER JOIN in_item_warehouse_location iwl ON td.itemwhseloc_id = iwl.itemwhseloc_id \
						LEFT JOIN in_item_project p ON p.item_id = td.item_id AND p.itemwhse_id = iwl.itemwhse_id AND p.itemwhseloc_id = td.itemwhseloc_id \
							AND ISNULL(p.custproj_desc, '') = ISNULL(td.custproj_desc, '') \
						WHERE \
							td.org_id = ? \
							AND tt.intranstype_adjustment_type = 'Q' \
							AND td.itemwhseloc_id IS NOT NULL \
							AND i.itemtype_code = 'F' \
							AND td.item_id IN (SELECT item_id FROM in_item_fifo WHERE custproj_desc IS NOT NULL) ";
	    var aArgs = [globals.org_id];
	    
		if (uItemID) {
			sqlTrans += " AND td.item_id = ? ";
			aArgs.push(uItemID.toString());
		}
		
		sqlTrans += "GROUP BY td.item_id, iwl.itemwhse_id, td.itemwhseloc_id, td.custproj_desc, p.ip_qty_on_hand, p.ip_id \
					 HAVING ABS(ISNULL(p.ip_qty_on_hand, 0) - SUM(td.itemtransd_qty)) >= 0.01";
	    
	    var dsTrans = scopes.avDB.getDataset(sqlTrans, aArgs);
	    
		if (dsTrans) {
			for (var i = 1; i <= dsTrans.getMaxRowIndex(); i++) {
				var nTransQty = dsTrans.getValue(i, 6);			
				var uIPID = dsTrans.getValue(i, 7);			
				/***@type {JSRecord<db:/avanti/in_item_project>} ***/
				var rBin = null;
				
				if (uIPID) {
					rBin = scopes.avDB.getRecFromSQL("SELECT ip_id FROM in_item_project WHERE ip_id = ?", "in_item_project", [uIPID]);
				}
				
				// missing ip rec
				if (!rBin) {
					rBin = scopes.avDB.newRecord("in_item_project");
					
					rBin.item_id = dsTrans.getValue(i, 1);
					rBin.itemwhse_id = dsTrans.getValue(i, 2);
					rBin.itemwhseloc_id = dsTrans.getValue(i, 3);
					rBin.custproj_desc = dsTrans.getValue(i, 4);
				}
				
				rBin.ip_qty_on_hand = nTransQty;				
				databaseManager.saveData(rBin);
			}
		}
	}
	
	function fixWares() {
		var sqlWare = "SELECT w.item_id, w.itemwhse_id, w.custproj_desc, w.ip_id, w.ip_qty_on_hand [w qty], sum(b.ip_qty_on_hand) [b qty] \
					  FROM in_item_project b \
					  INNER JOIN in_item_project w ON w.itemwhse_id = b.itemwhse_id AND ISNULL(w.custproj_desc, '') = ISNULL(b.custproj_desc, '') \
					  WHERE \
						  b.org_id = ? \
						  AND b.itemwhseloc_id IS NOT NULL \
						  AND w.itemwhseloc_id IS NULL ";	    
	    var aArgs = [globals.org_id];
	    
		if (uItemID) {
			sqlWare += " AND b.item_id = ? ";
			aArgs.push(uItemID.toString());
		}
		
		sqlWare += "GROUP BY w.item_id, w.itemwhse_id, w.custproj_desc, w.ip_id, w.ip_qty_on_hand \
					HAVING ABS(ISNULL(w.ip_qty_on_hand, 0) - SUM(b.ip_qty_on_hand)) >= 0.01";
	    
	    var dsWare = scopes.avDB.getDataset(sqlWare, aArgs);
	    
		if (dsWare) {
			for (var i = 1; i <= dsWare.getMaxRowIndex(); i++) {
				var uIPID = dsWare.getValue(i, 4);			
				var nBinQty = dsWare.getValue(i, 6);			
				/***@type {JSRecord<db:/avanti/in_item_project>} ***/
				var rWare = null;
				
				if (uIPID) {
					rWare = scopes.avDB.getRecFromSQL("SELECT ip_id FROM in_item_project WHERE ip_id = ?", "in_item_project", [uIPID]);
				}
				
				// missing ip rec
				if (!rWare) {
					rWare = scopes.avDB.newRecord("in_item_project");
					
					rWare.item_id = dsWare.getValue(i, 1);
					rWare.itemwhse_id = dsWare.getValue(i, 2);
					rWare.custproj_desc = dsWare.getValue(i, 3);
				}
				
				rWare.ip_qty_on_hand = nBinQty;
				databaseManager.saveData(rWare);
			}
		}
	}
	
	function fixItems() {
		var sqlItem = "SELECT i.item_id, i.custproj_desc, i.ip_id, i.ip_qty_on_hand [i qty], sum(w.ip_qty_on_hand) [w qty] \
						FROM in_item_project w \
						INNER JOIN in_item_project i ON i.item_id = w.item_id AND ISNULL(i.custproj_desc, '') = ISNULL(w.custproj_desc, '') \
						WHERE \
							w.org_id = ? \
							AND w.itemwhse_id IS NOT NULL AND w.itemwhseloc_id IS NULL \
							AND i.itemwhse_id IS NULL AND i.itemwhseloc_id IS NULL ";		
	    var aArgs = [globals.org_id];
	    
		if (uItemID) {
			sqlItem += " AND w.item_id = ? ";
			aArgs.push(uItemID.toString());
		}
		
		sqlItem += "GROUP BY i.item_id, i.custproj_desc, i.ip_id, i.ip_qty_on_hand \
					HAVING ABS(ISNULL(i.ip_qty_on_hand, 0) - SUM(w.ip_qty_on_hand)) >= 0.01";
	    
		var dsItem = scopes.avDB.getDataset(sqlItem, aArgs);
		
		if (dsItem) {
			for (var i = 1; i <= dsItem.getMaxRowIndex(); i++) {
				var uIPID = dsItem.getValue(i, 3);			
				var nWareQty = dsItem.getValue(i, 5);			
				/***@type {JSRecord<db:/avanti/in_item_project>} ***/
				var rItem = null;
				
				if (uIPID) {
					rItem = scopes.avDB.getRecFromSQL("SELECT ip_id FROM in_item_project WHERE ip_id = ?", "in_item_project", [uIPID]);
				}
				
				// missing ip rec
				if (!rItem) {
					rItem = scopes.avDB.newRecord("in_item_project");
					
					rItem.item_id = dsItem.getValue(i, 1);
					rItem.custproj_desc = dsItem.getValue(i, 2);
				}
				
				rItem.ip_qty_on_hand = nWareQty;
				databaseManager.saveData(rItem);
			}
		}
	}
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sys_plant>} rPlant
 * @param {UUID|String} uWareID
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"2944B397-BAB5-4E1F-AE05-547229502F5A"}
 */
function doesWarehouseBelongToPlant(rPlant, uWareID) {
	var bDoesWarehouseBelongToPlant = false;
	
	if (rPlant && uWareID && rPlant.sys_plant_to_in_warehouse$plant_id_active_whse) {
		for (var i = 1; i <= rPlant.sys_plant_to_in_warehouse$plant_id_active_whse.getSize(); i++) {
			if (rPlant.sys_plant_to_in_warehouse$plant_id_active_whse.getRecord(i).whse_id == uWareID) {
				bDoesWarehouseBelongToPlant = true;
				break;
			}
		}
	}
	
	return bDoesWarehouseBelongToPlant;
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/in_item>} [rItem] - optionally run it for just this item - called when opening item commitment dlg 
 * 
 * this was adapted/expanded from updates_SL13828
 *
 * @properties={typeid:24,uuid:"4A53C348-B8A1-43E1-A520-F3A248C226C6"}
 */
function FixCommittmentIssues(rItem) {
    /** @type{JSDataSet}*/
    var dsItems = null;    
    var sSQL = null;
    var sItemCondition = rItem ? " AND item_id = ? " : "";
    var sItemConditionWithPrefix = rItem ? " AND c.item_id = ? " : "";
    var aArgs = rItem ? [globals.org_id, rItem.item_id.toString()] : [globals.org_id];
    var aArgs2 = rItem ? [globals.org_id, globals.org_id, rItem.item_id.toString()] : [globals.org_id, globals.org_id];

    // 1. delete commits with zero cur_qty_committed - they been used up - dont need them anymore
	if (rItem) {
		scopes.avDB.getFSFromSQL("SELECT in_committment_id FROM in_committment WHERE org_id = ? AND ISNULL(cur_qty_committed, 0) <= 0" + sItemCondition, "in_committment", aArgs, null, true);
	}
	else {
		scopes.avDB.RunSQL("DELETE FROM in_committment WHERE org_id = ? AND ISNULL(cur_qty_committed, 0) <= 0" + sItemCondition, null, aArgs);
	}

	
    // 2. delete commitments for jobs/orders that are completed or cancelled 
    sSQL = "SELECT c.item_id, c.cur_qty_committed, i.ordrevditem_whse_id \
            FROM in_committment c \
            INNER JOIN sa_order_revd_item i ON c.ordrevditem_id = i.ordrevditem_id \
            INNER JOIN sa_order_revision_detail d ON i.ordrevd_id = d.ordrevd_id \
            INNER JOIN sa_order_revision_header h ON d.ordrevh_id = h.ordrevh_id \
            LEFT OUTER JOIN prod_job j ON d.job_id = j.job_id \
            WHERE \
            	c.org_id = ? \
            	AND (j.jobstat_id = 'Completed' OR h.ordrevh_order_status IN ('Completed', 'Cancelled'))" + sItemConditionWithPrefix;
    
    dsItems = scopes.avDB.getDataset(sSQL, aArgs);

	if (dsItems && dsItems.getMaxRowIndex() > 0) {
		updateCommittmentTotals()
	    
		sSQL = " FROM in_committment \
	            WHERE org_id = ? AND ordrevditem_id IN \
	            ( \
	                SELECT ordrevditem_id \
	                FROM sa_order_revd_item i \
	                INNER JOIN sa_order_revision_detail d ON i.ordrevd_id = d.ordrevd_id \
	                INNER JOIN sa_order_revision_header h ON d.ordrevh_id = h.ordrevh_id \
	                LEFT OUTER JOIN prod_job j ON d.job_id = j.job_id \
	                WHERE \
	                	(j.jobstat_id = 'Completed' OR h.ordrevh_order_status IN ('Completed', 'Cancelled')) \
	            )" + sItemCondition;

		if (rItem) {
			scopes.avDB.getFSFromSQL("SELECT in_committment_id" + sSQL, "in_committment", aArgs, null, true);
		}
		else {
			scopes.avDB.RunSQL("DELETE" + sSQL, null, aArgs);
		}
	}

	
    // 3. delete orphan commit recs - no sa_order_revd_item
    sSQL = "SELECT c.item_id, c.cur_qty_committed, null \
	        FROM in_committment c \
	        WHERE \
               	c.org_id = ? \
	        	AND ordrevditem_id NOT IN (SELECT ordrevditem_id FROM sa_order_revd_item)" + sItemCondition;

    dsItems = scopes.avDB.getDataset(sSQL, aArgs);
	
	if (dsItems && dsItems.getMaxRowIndex() > 0) {
		updateCommittmentTotals()
		
		if (rItem) {
			scopes.avDB.getFSFromSQL("SELECT in_committment_id FROM in_committment WHERE org_id = ? AND ordrevditem_id NOT IN (SELECT ordrevditem_id FROM sa_order_revd_item)" + sItemCondition, "in_committment", aArgs, null, true);
		}
		else {
			scopes.avDB.RunSQL("DELETE FROM in_committment WHERE org_id = ? AND ordrevditem_id NOT IN (SELECT ordrevditem_id FROM sa_order_revd_item)" + sItemCondition, null, aArgs);
		}
	}

	
	// 4. this is only for thomas. they had a bunch of revditem recs that had multiple commits, for most of them 1 of the commits was 0, so they were deleted above
	// there were 8 that i had to go thru and decide with is the good commits and which the bad. the bad will be deleted here.
	if (!rItem) {
		var aBadCommits = ['9BF9F89C-B4A4-41DD-8FE3-961736AD6C23', '134883A7-F611-4E1A-8081-960639C95D4D', 'B7576094-8F1E-47F7-AE6E-EED6DE9870FF', '9697EC5E-EE70-4560-83B7-FC391D34A6B3', 
						   'E18C349C-EECA-4126-BBCE-0D552E044298', '6EAA2297-CE4E-4A0B-8B92-549EC08A41AB', '0C6896C1-D2E6-42B3-8E5E-B96F0C684A9E', '7AC3EE9E-8FE7-4D46-919D-ACAA02ED6D6C'];
	    sSQL = "SELECT c.item_id, c.cur_qty_committed, i.ordrevditem_whse_id \
		        FROM in_committment c \
		        INNER JOIN sa_order_revd_item i ON c.ordrevditem_id = i.ordrevditem_id \
		        WHERE \
		        	c.org_id = 'AA58CAA2-AAF5-42DA-8300-E6DA23D6813A' \
		        	AND c.in_committment_id in (?, ?, ?, ?, ?, ?, ?, ?)";
	
		dsItems = scopes.avDB.getDataset(sSQL, aBadCommits);
		
		if (dsItems && dsItems.getMaxRowIndex() > 0) {
			updateCommittmentTotals()
			
		    // delete bad commit recs
			scopes.avDB.RunSQL("DELETE FROM in_committment WHERE org_id = 'AA58CAA2-AAF5-42DA-8300-E6DA23D6813A' AND in_committment_id in (?, ?, ?, ?, ?, ?, ?, ?)", null, aBadCommits);
		}
	}

	
    // 5. set item_committed_qty to zero for all items that dont have any commits
	if (rItem) {
		scopes.avDB.updateFSWithSQLQuery("in_item", ["item_committed_qty"], [0], 
			"SELECT item_id FROM in_item WHERE org_id = ? AND item_id NOT IN (SELECT item_id FROM in_committment WHERE org_id = ?)" + sItemCondition, 
			aArgs2)
	}
	else {
		sSQL = "UPDATE in_item \
	            SET item_committed_qty = 0 \
	            WHERE \
	               	org_id = ? \
	            	AND item_id NOT IN (SELECT item_id FROM in_committment WHERE org_id = ?)" + sItemCondition;
	
		scopes.avDB.RunSQL(sSQL, null, aArgs2);
	}

	
    // 6. set itemwhse_committed_qty to zero for all items that dont have any commits
	if (rItem) {
		scopes.avDB.updateFSWithSQLQuery("in_item_warehouse", ["itemwhse_committed_qty"], [0], 
			"SELECT itemwhse_id FROM in_item_warehouse WHERE org_id = ? AND item_id NOT IN (SELECT item_id FROM in_committment WHERE org_id = ?)" + sItemCondition, 
			aArgs2)
	}
	else {
		sSQL = "UPDATE in_item_warehouse \
	            SET itemwhse_committed_qty = 0 \
	            WHERE \
	               	org_id = ? \
	            	AND item_id NOT IN (SELECT item_id FROM in_committment WHERE org_id = ?)" + sItemCondition;
	
		scopes.avDB.RunSQL(sSQL, null, aArgs2);
	}

	if (!rItem) {
		// 7. update any in_item.item_committed_qty where there is a mismatch with sum of commits
		sSQL = "UPDATE in_item \
				SET in_item.item_committed_qty = f.commitSum \
				FROM in_item i \
				INNER JOIN \
				( \
					SELECT i.item_id, i.item_committed_qty, SUM(c.cur_qty_committed) commitSum \
					FROM in_committment c \
					INNER JOIN in_item i on c.item_id = i.item_id \
					INNER JOIN sa_order_revd_item ri on c.ordrevditem_id = ri.ordrevditem_id \
					INNER JOIN sa_order_revision_detail ON ri.ordrevd_id = sa_order_revision_detail.ordrevd_id \
					INNER JOIN sa_order_revision_header ON sa_order_revision_detail.ordrevh_id = sa_order_revision_header.ordrevh_id \
					INNER JOIN sa_order ON sa_order.ordh_id = sa_order_revision_header.ordh_id \
					WHERE \
						sa_order_revision_header.ordrevh_revision = 0 \
						AND sa_order.ordh_document_type in ('ORD') \
						AND sa_order_revision_header.ordrevh_order_status not in ('Cancelled', 'Completed' , 'Incomplete') \
					GROUP BY i.item_id, i.item_committed_qty \
					HAVING ABS(i.item_committed_qty - SUM(c.cur_qty_committed)) >= 0.0001 \
				) f ON i.item_id = f.item_id \
				WHERE i.org_id = ?";

		scopes.avDB.RunSQL(sSQL, null, aArgs);
		
		scopes.avText.showInfo("Finished running 'Fix Committment Issues'. Please logout then back in to see changes.", true);
	}
	
	function updateCommittmentTotals() {
	    //  update item qty committed for all commits we are going to delete - just the ones with a non zero qty committed tho 
		var sUpdateItemSQL = "UPDATE in_item \
                              SET item_committed_qty = item_committed_qty - ? \
                              WHERE item_id = ?";
		var sUpdateWareSQL = "UPDATE in_item_warehouse \
				              SET itemwhse_committed_qty = itemwhse_committed_qty - ? \
					          WHERE item_id = ? AND whse_id = ?";

		for (var i = 1; i <= dsItems.getMaxRowIndex(); i++) {
			var sItemID = dsItems.getValue(i, 1);
			var nQtyCommitted = dsItems.getValue(i, 2);
			var sWareID = dsItems.getValue(i, 3);

			scopes.avDB.RunSQL(sUpdateItemSQL, null, [nQtyCommitted, sItemID.toString()]);
			
			if (sWareID) {
				scopes.avDB.RunSQL(sUpdateWareSQL, null, [nQtyCommitted, sItemID.toString(), sWareID.toString()]);
			}
		}
	}
}

/**
 * @param {UUID} glacct_id
 * @param {String} sTransType
 * @param {UUID} item_id
 * @param {UUID} intraned_whse_id
 * @param {JSRecord<db:/avanti/in_trans_entry_detail>} rTransEntryDetail
 * @return
 * @properties={typeid:24,uuid:"F0E059AC-CA83-4E53-9821-0CA7CDEC4404"}
 */
function setGLAccID(glacct_id, sTransType, item_id, intraned_whse_id, rTransEntryDetail) {
	if (glacct_id == null && sTransType != scopes.avInv.TRANSACTION_TYPE.BinTransfer 
		&& sTransType != scopes.avInv.TRANSACTION_TYPE.RollBinTransfer 
		&& sTransType != scopes.avInv.TRANSACTION_TYPE.PendingReceipts) {
		    if (scopes.globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.AllowDefaultOffsetAccount) == 1
	            && utils.hasRecords(rTransEntryDetail.in_trans_entry_detail_to_in_trans_entry_header)) {
		        glacct_id = scopes.avInv.getDefaultOffsetGLA(rTransEntryDetail.in_trans_entry_detail_to_in_trans_entry_header.getRecord(1), item_id);
		    }		    
		    // Set the GL account id based on the item, item warehouse item class or item class
			if (!glacct_id && utils.hasRecords(rTransEntryDetail.in_trans_entry_detail_to_in_item)) {
			    
			    /** @type {JSRecord<db:/avanti/in_warehouse_gl_itemclass>} */
		        var rItemWhseItemClass = scopes.avDB.getRec("in_warehouse_gl_itemclass", ["whse_id", "itemclass_id"], [intraned_whse_id, rTransEntryDetail.in_trans_entry_detail_to_in_item.itemclass_id]);
		        
		        if (rTransEntryDetail.in_trans_entry_detail_to_in_item.item_glacct_id_inventory_adj) {
		            glacct_id = rTransEntryDetail.in_trans_entry_detail_to_in_item.item_glacct_id_inventory_adj;
		        } else if (rItemWhseItemClass && rItemWhseItemClass.whseitemclass_gl_invadj) {
		            glacct_id = rItemWhseItemClass.whseitemclass_gl_invadj;
		        } else {
		            if (utils.hasRecords(rTransEntryDetail.in_trans_entry_detail_to_in_item.in_item_to_in_item_class)) {
		                if (rTransEntryDetail.in_trans_entry_detail_to_in_item.in_item_to_in_item_class.itemclass_gl_inventory_adj) {
		                    glacct_id = rTransEntryDetail.in_trans_entry_detail_to_in_item.in_item_to_in_item_class.itemclass_gl_inventory_adj;
		                }
		            }
		        }
		    }
			
		    //Use the control account if no other accounting is defined
	        if (!glacct_id && utils.hasRecords(rTransEntryDetail.in_trans_entry_detail_to_in_warehouse)) {
	            var rWarehouse = rTransEntryDetail.in_trans_entry_detail_to_in_warehouse.getRecord(1);
	            glacct_id = scopes.avInv.getInventoryAdjustmentControlAccount(rWarehouse);
	        }
	}
	return glacct_id;
}

/**
 * Apply negative transaction balances to FIFO.
 * 
 * @param {JSRecord<db:/avanti/in_item>} rItem
 * @param {String} sTransCode
 * @param {JSRecord<db:/avanti/in_item_trans_detail>} rItemTransDetail
 * @param {JSRecord<db:/avanti/in_item_warehouse>} [rItemWhse]
 * 
 * @public
 *
 * @properties={typeid:24,uuid:"0233FF5C-0E44-4A1A-8E9A-1A11B5A42350"}
 */
function applyNegativeTransactionsToFifo(rItem, sTransCode, rItemTransDetail, rItemWhse) {
	if (!rItem && !sTransCode && !rItemTransDetail) {
		return;
	}
	
	databaseManager.saveData();
	
	var nBalance = 0;
	var iDecimals = (rItem.item_decimal_places ? rItem.item_decimal_places : 0);
	
	if (!rItemWhse) {
		rItemWhse = scopes.avDB.getRec("in_item_warehouse",["item_id", "whse_id"],[rItemTransDetail.item_id, rItemTransDetail.whse_id]);
	}

	var oSQL = { };
	oSQL.table = "in_item_fifo";

	if (sTransCode == scopes.avInv.TRANSACTION_TYPE.CancelStockReceipt) {
		oSQL.args = [globals.org_id, rItemWhse.item_id.toString(), rItemWhse.itemwhse_id.toString(), (rItemTransDetail.itemwhseloc_id ? rItemTransDetail.itemwhseloc_id.toString() : rItemTransDetail.itemwhseloc_id), rItemTransDetail.porecd_id.toString()];
		oSQL.sql = "SELECT fifo_id\
            FROM in_item_fifo\
            INNER JOIN in_item_trans_detail ON in_item_fifo.itemtransd_id = in_item_trans_detail.itemtransd_id\
            INNER JOIN in_item_trans_header ON in_item_trans_detail.itemtransh_id = in_item_trans_header.itemtransh_id\
            INNER JOIN in_transaction_type ON in_item_trans_header.intranstype_id = in_transaction_type.intranstype_id\
            WHERE (in_item_fifo.org_id = ?) AND (in_item_fifo.item_id = ?) AND (in_item_fifo.itemwhse_id = ?) AND (in_item_fifo.itemwhseloc_id = ?) AND (in_item_fifo.fifo_qty_remaining <> 0)\
            AND (in_item_trans_detail.porecd_id = ?) AND (in_transaction_type.intranstype_trans_code = 'SR') \
            ";

		if (rItemTransDetail.initemroll_id) {
			oSQL.args.push(rItemTransDetail.initemroll_id.toString());
			oSQL.sql += " AND (in_item_fifo.rItemTransDetail.initemroll_id = ?)"
		}

		if (rItemTransDetail.custproj_desc) {
			oSQL.sql += " AND in_item_fifo.custproj_desc = ? ";
			oSQL.args.push(rItemTransDetail.custproj_desc);
		} else {
			oSQL.sql += " AND in_item_fifo.custproj_desc IS NULL ";
		}
	} else {
		oSQL.args = [globals.org_id, rItemWhse.item_id.toString(), rItemWhse.itemwhse_id.toString()];

		oSQL.sql = "SELECT fifo_id\
            FROM in_item_fifo\
            WHERE (org_id = ?) AND (item_id = ?) AND (itemwhse_id = ?) AND (fifo_qty_remaining <> 0)\
            ";

		if (rItemTransDetail.itemwhseloc_id) {
			oSQL.args.push(rItemTransDetail.itemwhseloc_id.toString());
			oSQL.sql += " AND (itemwhseloc_id = ?) "
		}

		if (rItemTransDetail.initemroll_id) {
			oSQL.args.push(rItemTransDetail.initemroll_id.toString());
			oSQL.sql += " AND (initemroll_id = ?)"
		}

		if (rItemTransDetail.custproj_desc) {
			oSQL.sql += " AND custproj_desc = ? ";
			oSQL.args.push(rItemTransDetail.custproj_desc);
		} else {
			oSQL.sql += " AND custproj_desc IS NULL ";
		}
	}
	var nShipped = 0;
	if (sTransCode == scopes.avInv.TRANSACTION_TYPE.Shipment) {
		nShipped = 1;
	}

	/**@type {JSFoundSet<db:/avanti/in_item_trans_detail>} */
	var fsItemTransDetailNegativeBalance = scopes.avInv.getItemTransactionsWithNegativeFifoBalance(rItem, rItemTransDetail);

	/***@type {JSFoundSet<db:/avanti/in_item_fifo>} ***/
	var fsFIFO = globals["avUtilities_sqlFoundset"](oSQL);

	if (fsFIFO && fsFIFO.getSize() > 0 && fsItemTransDetailNegativeBalance && fsItemTransDetailNegativeBalance.getSize() > 0) {
		fsFIFO.sort("fifo_date asc, fifo_qty_remaining asc");

		for (var td = 1; td <= fsItemTransDetailNegativeBalance.getSize(); td++) {
			var rItemTransDetailNegativeBalance = fsItemTransDetailNegativeBalance.getRecord(td);

			nBalance = globals["avUtilities_roundNumber"](rItemTransDetailNegativeBalance.itemtransd_fifo_balance, iDecimals);
			
			for (var ififo = 1; ififo <= fsFIFO.getSize(); ififo++) {
				var rFIFO = fsFIFO.getRecord(ififo);

				if (globals["avUtilities_roundNumber"](rFIFO.fifo_qty_remaining, iDecimals) == 0) {
					continue;
				}

				if (nBalance == 0) {
					break;
				}

				if (nBalance < 0 && rFIFO.fifo_qty_remaining > 0) {
					if (rFIFO.fifo_qty_remaining + nBalance >= 0) {
						rFIFO.fifo_qty_remaining += nBalance;
						scopes.avInv.createFifoDetRecord(rFIFO.fifo_id, rItemTransDetailNegativeBalance.itemtransd_id, globals["avUtilities_roundNumber"](nBalance, iDecimals), nShipped);
						rItemTransDetailNegativeBalance.itemtransd_fifo_balance += globals["avUtilities_roundNumber"](nBalance * -1, iDecimals)
						nBalance = 0;
					} else {
						nBalance += rFIFO.fifo_qty_remaining;
						scopes.avInv.createFifoDetRecord(rFIFO.fifo_id, rItemTransDetailNegativeBalance.itemtransd_id, globals["avUtilities_roundNumber"](rFIFO.fifo_qty_remaining * -1, iDecimals), nShipped);
						rItemTransDetailNegativeBalance.itemtransd_fifo_balance += globals["avUtilities_roundNumber"](rFIFO.fifo_qty_remaining, iDecimals);
						rFIFO.fifo_qty_remaining = 0;
					}
				}

				databaseManager.saveData();
			}
		}
	}

	databaseManager.saveData();
	
}

/**
 * Get Transaction s with a negative fifo balance
 * @param {JSRecord<db:/avanti/in_item>} rItem
 * @param {JSRecord<db:/avanti/in_item_trans_detail>} rItemTransDetail
 * 
 * @return {JSFoundSet<db:/avanti/in_item_trans_detail>}
 *
 * @properties={typeid:24,uuid:"8BB0E2BF-861F-4423-A6A1-F09889B7D6F1"}
 */
function getItemTransactionsWithNegativeFifoBalance(rItem, rItemTransDetail) {
	if (rItem && rItemTransDetail && utils.hasRecords(rItemTransDetail.in_item_trans_detail_to_in_item_trans_header)) {
		
		var rItemTransHeader = rItemTransDetail.in_item_trans_detail_to_in_item_trans_header.getRecord(1);
		
		/***@type {{sql:String,
		 *          args:Array,
		 *          server:String,
		 *          maxRows:Number,
		 *          table:String}}*/
		var oSQL = { };
		oSQL.args = [globals.org_id, rItem.item_id.toString(), rItemTransDetail.whse_id.toString(), rItemTransHeader.itemtransh_transaction_no];
		oSQL.table = "in_item_trans_detail";
		oSQL.sql = "SELECT  itd.itemtransd_id \
	                  FROM  in_item_trans_detail AS itd \
	                  INNER JOIN in_item_trans_header AS ith ON itd.itemtransh_id =  ith.itemtransh_id \
	                  WHERE (itd.org_id = ?) AND (item_id = ?) AND (whse_id = ?) AND (itemtransd_fifo_balance < 0) AND (ith.itemtransh_transaction_no <= ?)\
	                  ";
			
		if (rItemTransDetail.itemwhseloc_id) {
			oSQL.args.push(rItemTransDetail.itemwhseloc_id.toString());
			oSQL.sql += " AND (itemwhseloc_id = ?) "
		}

		if (rItemTransDetail.initemroll_id) {
			oSQL.args.push(rItemTransDetail.initemroll_id.toString());
			oSQL.sql += " AND (initemroll_id = ?)"
		}

		if (rItemTransDetail.custproj_desc) {
			oSQL.sql += " AND custproj_desc = ? ";
			oSQL.args.push(rItemTransDetail.custproj_desc);
		} else {
			oSQL.sql += " AND custproj_desc IS NULL ";
		}

		/**@type {JSFoundSet<db:/avanti/in_item_trans_detail>} */
		var fsTransDetail = globals["avUtilities_sqlFoundset"](oSQL);
		fsTransDetail.sort("in_item_trans_detail_to_in_item_trans_header.itemtransh_transaction_no asc, created_date asc, itemtransd_qty desc");
		return fsTransDetail;
	
	} else {
		return null;
	}
}

/**
 * Get DataSet of Transaction s with a negative fifo balance
 * @param {JSRecord<db:/avanti/in_item>} rItem
 * @param {JSRecord<db:/avanti/in_item_trans_detail>} rItemTransDetail
 * 
 * @return {JSDataSet}
 *
 * @properties={typeid:24,uuid:"80BF2C70-AE49-4AA4-B8A1-110E7AD02B79"}
 */
function getDataSetOfItemTransactionsWithNegativeFifoBalance(rItem, rItemTransDetail) {
    if (rItem && rItemTransDetail && utils.hasRecords(rItemTransDetail.in_item_trans_detail_to_in_item_trans_header)) {

        var rItemTransHeader = rItemTransDetail.in_item_trans_detail_to_in_item_trans_header.getRecord(1);

        /***@type {{sql:String,
         *          args:Array,
         *          server:String,
         *          maxRows:Number,
         *          table:String}}*/
        var oSQL = {};
        oSQL.args = [globals.org_id, rItem.item_id.toString(), rItemTransDetail.whse_id.toString(), rItemTransHeader.itemtransh_transaction_no];
        oSQL.table = "in_item_trans_detail";
        oSQL.sql = "SELECT  itd.itemtransd_id \
                      FROM  in_item_trans_detail AS itd \
                      INNER JOIN in_item_trans_header AS ith ON itd.itemtransh_id =  ith.itemtransh_id \
                      WHERE (itd.org_id = ?) AND (item_id = ?) AND (whse_id = ?) AND (itemtransd_fifo_balance < 0) AND (ith.itemtransh_transaction_no <= ?)\
                      ";

        if (rItemTransDetail.itemwhseloc_id) {
            oSQL.args.push(rItemTransDetail.itemwhseloc_id.toString());
            oSQL.sql += " AND (itemwhseloc_id = ?) "
        }

        if (rItemTransDetail.initemroll_id) {
            oSQL.args.push(rItemTransDetail.initemroll_id.toString());
            oSQL.sql += " AND (initemroll_id = ?)"
        }

        if (rItemTransDetail.custproj_desc) {
            oSQL.sql += " AND custproj_desc = ? ";
            oSQL.args.push(rItemTransDetail.custproj_desc);
        }
        else {
            oSQL.sql += " AND custproj_desc IS NULL ";
        }

        oSQL.sql += " ORDER BY ith.itemtransh_transaction_no asc, itd.created_date asc, itd.itemtransd_qty desc ";

        var dsTransDetail = globals["avUtilities_sqlDataset"](oSQL);

        return dsTransDetail;

    }
    else {
        return null;
    }
}


/**
 * Handles fifo quantity adjustments when a fulfillment pick is cancelled
 * 
 * @param {JSRecord<db:/avanti/in_item_trans_detail>} rTransDetail
 * @param {Number} iDecimals
 *
 * @properties={typeid:24,uuid:"DF95A533-486A-4EDB-8DA7-0FA4507A95EB"}
 */
function adjustFifoForFulfillmentPickCancellation(rTransDetail, iDecimals) {
	/** @type {JSFoundSet<db:/avanti/in_item_fifo_det>} */
	var fsFifoDet = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_fifo_det');

	var oSQL = new Object();

	var itemwhseloc_id = rTransDetail.itemwhseloc_id ? rTransDetail.itemwhseloc_id.toString() : null;

	oSQL.sql = "SELECT itfd.fifod_id  \
                FROM in_item_fifo_det AS itfd\
                INNER JOIN in_item_trans_detail AS itd ON itd.itemtransd_id = itfd.itemtransd_id \
                WHERE itfd.org_id = ? \
                AND itd.pickd_id = ? AND itfd.shipped = 1 AND itd.itemwhseloc_id = ?";

	oSQL.args = [globals.org_id, rTransDetail.pickd_id.toString(), itemwhseloc_id];

	fsFifoDet.loadRecords(oSQL.sql, oSQL.args);

	if (fsFifoDet && fsFifoDet.getSize() > 0) {
		var nAppliedBalance = rTransDetail.itemtransd_qty;
		var fdMax = databaseManager.getFoundSetCount(fsFifoDet);
		for (var i = 1; i <= fdMax; i++) {
			var rFifoDet = fsFifoDet.getRecord(i);
			if (!rFifoDet) {
				continue;
			}
			rFifoDet.shipped = 0;
			if (utils.hasRecords(rFifoDet.in_item_fifo_det_to_in_item_fifo)) {
				fsFifoDet.newRecord(false, true);
				fsFifoDet.fifo_id = rFifoDet.fifo_id;
				fsFifoDet.itemtransd_id = rTransDetail.itemtransd_id;
				fsFifoDet.fifod_qty = rFifoDet.fifod_qty * -1;
				nAppliedBalance -= fsFifoDet.fifod_qty;
				fsFifoDet.shipped = 0;
				rFifoDet.in_item_fifo_det_to_in_item_fifo.fifo_qty_remaining += fsFifoDet.fifod_qty;

				databaseManager.saveData(rFifoDet.in_item_fifo_det_to_in_item_fifo);
				databaseManager.saveData(fsFifoDet);

				rTransDetail.itemtransd_fifo_balance += globals["avUtilities_roundNumber"](rFifoDet.fifod_qty, iDecimals);
			}
			databaseManager.saveData(rFifoDet);
			
			// Create New FIFO for the unapplied cancel shipment balance
            if (nAppliedBalance > 0) {
            	scopes.avInv.createFifoRecord(rTransDetail, null, nAppliedBalance, null, null, rTransDetail.custproj_desc);
            	rTransDetail.itemtransd_fifo_balance += globals["avUtilities_roundNumber"](nAppliedBalance * -1, iDecimals);
                databaseManager.saveData();
            }
		}
	}
	else {
        //Create new FIFO if cannot locate shipment
        scopes.avInv.createFifoRecord(rTransDetail, null, null, null, null, rTransDetail.custproj_desc);
        rTransDetail.itemtransd_fifo_balance += globals["avUtilities_roundNumber"](rTransDetail.itemtransd_qty * -1, iDecimals);
        databaseManager.saveData();
    }
}

/**
 * Handles fifo quantity adjustments when return is processed
 * 
 * @param {JSRecord<db:/avanti/in_item_trans_detail>} rTransDetail
 * @param {Number} iDecimals
 *
 * @properties={typeid:24,uuid:"2ED0C263-5358-4038-A8FF-7F2E21AAF66C"}
 */
function adjustFifoForReturnToStock(rTransDetail, iDecimals) {
	/** @type {JSFoundSet<db:/avanti/in_item_fifo_det>} */
	var fsFifoDet = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_fifo_det');

	var oSQL = new Object();

	var itemwhseloc_id = rTransDetail.itemwhseloc_id ? rTransDetail.itemwhseloc_id.toString() : null;
	var ordrevd_id = null;

	if (utils.hasRecords(rTransDetail.in_item_trans_detail_to_sa_return_detail)) {
		ordrevd_id = rTransDetail.in_item_trans_detail_to_sa_return_detail.ordrevd_id;
	}

	oSQL.sql = "SELECT itfd.fifod_id  \
                FROM in_item_fifo_det AS itfd\
                INNER JOIN in_item_trans_detail AS itd ON itd.itemtransd_id = itfd.itemtransd_id \
                INNER JOIN in_item_trans_header AS ith ON itd.itemtransh_id = ith.itemtransh_id \
            	INNER JOIN in_transaction_type As tt ON ith.intranstype_id = tt.intranstype_id \
            	INNER JOIN sa_pack_detail AS pd ON itd.packd_id = pd.packd_id \
                WHERE itfd.org_id = ? AND pd.ordrevd_id = ? AND itd.itemwhseloc_id = ? ";

	oSQL.args = [globals.org_id, ordrevd_id.toString(), itemwhseloc_id];

	fsFifoDet.clear();
	fsFifoDet.loadRecords(oSQL.sql, oSQL.args);

	if (fsFifoDet && fsFifoDet.getSize() > 0) {
		var nAppliedBalance = rTransDetail.itemtransd_qty;
		var fdMax = databaseManager.getFoundSetCount(fsFifoDet);
		for (var i = 1; i <= fdMax; i++) {
			var rFifoDet = fsFifoDet.getRecord(i);
			if (!rFifoDet) {
				continue;
			}
			rFifoDet.shipped = 0;
			if (utils.hasRecords(rFifoDet.in_item_fifo_det_to_in_item_fifo)) {
				fsFifoDet.newRecord(false, true);
				fsFifoDet.fifo_id = rFifoDet.fifo_id;
				fsFifoDet.itemtransd_id = rTransDetail.itemtransd_id;
				fsFifoDet.fifod_qty = rTransDetail.itemtransd_qty;
				nAppliedBalance -= fsFifoDet.fifod_qty;
				fsFifoDet.shipped = 0;
				rFifoDet.in_item_fifo_det_to_in_item_fifo.fifo_qty_remaining += fsFifoDet.fifod_qty;

				databaseManager.saveData(rFifoDet.in_item_fifo_det_to_in_item_fifo);
				databaseManager.saveData(fsFifoDet);

				rTransDetail.itemtransd_fifo_balance -= globals["avUtilities_roundNumber"](fsFifoDet.fifod_qty, iDecimals);
			}
			databaseManager.saveData(rFifoDet);

			// Create New FIFO for the unapplied cancel shipment balance
			if (nAppliedBalance > 0) {
				scopes.avInv.createFifoRecord(rTransDetail, null, nAppliedBalance, null, null, rTransDetail.custproj_desc);
				rTransDetail.itemtransd_fifo_balance += globals["avUtilities_roundNumber"](nAppliedBalance * -1, iDecimals);
				databaseManager.saveData();
			}
		}
	} else {
	    // Do nothing.
	}
}


/**
 * Handles fifo quantity adjustments when a PO receipt is cancelled
 * @param {JSRecord<db:/avanti/in_item_trans_detail>} rTransDetail
 *
 *
 * @properties={typeid:24,uuid:"3D5D2B26-920A-4413-B808-1443E6FAF9CE"}
 */
function adjustFifoForCancelledReturnToStock(rTransDetail) {

	/** @type {JSFoundSet<db:/avanti/in_item_fifo>} */
	var fsFifo = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_fifo');
	var iDecimals = 0;
	
	if (utils.hasRecords(rTransDetail.in_item_trans_detail_to_in_item)) {
		iDecimals = (rTransDetail.in_item_trans_detail_to_in_item.item_decimal_places == null ? 0 : rTransDetail.in_item_trans_detail_to_in_item.item_decimal_places);
	}

	var oSQL = new Object();

	oSQL.sql = "SELECT fifo.fifo_id \
					FROM in_item_fifo AS fifo \
					INNER JOIN in_item_fifo_det AS fifod ON fifo.fifo_id = fifod.fifo_id \
					INNER JOIN in_item_trans_detail AS itd ON itd.itemtransd_id = fifod.itemtransd_id \
					INNER JOIN in_item_trans_header AS ith ON itd.itemtransh_id = ith.itemtransh_id \
					INNER JOIN in_transaction_type As tt ON ith.intranstype_id = tt.intranstype_id \
					WHERE fifo.org_id = ? \
					AND tt.intranstype_trans_code = 'RTS' \
					AND itd.retrd_id = ? \
			    ";

	oSQL.args = [globals.org_id, rTransDetail.retrd_id.toString()];

	fsFifo.clear();
	fsFifo.loadRecords(oSQL.sql, oSQL.args);
	if (fsFifo.getSize() < 1) {
		return;
	}
	//Only expect 1 record from the query
	var rFifo = fsFifo.getRecord(1);
	var sFifo_id = rFifo.fifo_id;

	var nDetQty = rTransDetail.itemtransd_qty;
	
	var nDiff = rFifo.fifo_qty_remaining + nDetQty;
	
	if (nDiff >= 0) {
		rFifo.fifo_qty_remaining = nDiff;
		createFifoDetRecord(sFifo_id, rTransDetail.itemtransd_id, nDetQty, 0);
		rTransDetail.itemtransd_fifo_balance += globals["avUtilities_roundNumber"](nDetQty * -1, iDecimals);
	}
	else if (rFifo.fifo_qty_remaining > 0) {
		createFifoDetRecord(sFifo_id, rTransDetail.itemtransd_id, rFifo.fifo_qty_remaining, 0);
		rTransDetail.itemtransd_fifo_balance += globals["avUtilities_roundNumber"](rFifo.fifo_qty_remaining, iDecimals);
		rFifo.fifo_qty_remaining = 0;
	}
}

/**
 * Handles fifo quantity adjustments of auto issued material when a Shipment is cancelled
 * @param {JSRecord<db:/avanti/in_item_trans_detail>} rTransDetail
 * @param {Number} iDecimals
 *
 * @properties={typeid:24,uuid:"7F888F93-C9C6-4AD3-AACB-7CA56A6502CB"}
 */
function adjustFifoForShipCancellationAutoIssueMaterials(rTransDetail, iDecimals) {
	/** @type {JSFoundSet<db:/avanti/in_item_fifo_det>} */
	var fsFifoDet = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_fifo_det');

	var oSQL = new Object();

	var itemwhseloc_id = rTransDetail.itemwhseloc_id ? rTransDetail.itemwhseloc_id.toString() : null;
	var jcm_id = rTransDetail.jcm_id ? rTransDetail.jcm_id.toString() : null;

	oSQL.sql = "SELECT itfd.fifod_id  \
                FROM in_item_fifo_det AS itfd\
                INNER JOIN in_item_trans_detail AS itd ON itd.itemtransd_id = itfd.itemtransd_id \
                WHERE itfd.org_id = ? \
                AND itd.jcm_id = ? AND itd.itemwhseloc_id = ?";

	oSQL.args = [globals.org_id, jcm_id, itemwhseloc_id];

	fsFifoDet.clear();
	
	if (jcm_id) {
		fsFifoDet.loadRecords(oSQL.sql, oSQL.args);
	}

	if (fsFifoDet && fsFifoDet.getSize() > 0) {
		var nAppliedBalance = rTransDetail.itemtransd_qty;
		var fdMax = databaseManager.getFoundSetCount(fsFifoDet);
		for (var i = 1; i <= fdMax; i++) {
			var rFifoDet = fsFifoDet.getRecord(i);
			if (!rFifoDet) {
				continue;
			}
			rFifoDet.shipped = 0;
			if (utils.hasRecords(rFifoDet.in_item_fifo_det_to_in_item_fifo)) {
				fsFifoDet.newRecord(false, true);
				fsFifoDet.fifo_id = rFifoDet.fifo_id;
				fsFifoDet.itemtransd_id = rTransDetail.itemtransd_id;
				fsFifoDet.fifod_qty = rFifoDet.fifod_qty * -1;
				nAppliedBalance -= fsFifoDet.fifod_qty;
				fsFifoDet.shipped = 0;
				rFifoDet.in_item_fifo_det_to_in_item_fifo.fifo_qty_remaining += fsFifoDet.fifod_qty;

				databaseManager.saveData(rFifoDet.in_item_fifo_det_to_in_item_fifo);
				databaseManager.saveData(fsFifoDet);

				rTransDetail.itemtransd_fifo_balance += globals["avUtilities_roundNumber"](rFifoDet.fifod_qty, iDecimals);
			}
			databaseManager.saveData(rFifoDet);
			
			// Create New FIFO for the unapplied cancel shipment balance
            if (nAppliedBalance > 0) {
            	scopes.avInv.createFifoRecord(rTransDetail, null, nAppliedBalance, null, null, rTransDetail.custproj_desc);
            	rTransDetail.itemtransd_fifo_balance += globals["avUtilities_roundNumber"](nAppliedBalance * -1, iDecimals);
                databaseManager.saveData();
            }
		}
	}
	else {
        //Create new FIFO if cannot locate shipment
        scopes.avInv.createFifoRecord(rTransDetail, null, null, null, null, rTransDetail.custproj_desc);
        rTransDetail.itemtransd_fifo_balance += globals["avUtilities_roundNumber"](rTransDetail.itemtransd_qty * -1, iDecimals);
        databaseManager.saveData();
    }
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/in_item_warehouse>} rItemWare
 * @param {JSRecord<db:/avanti/in_item_warehouse_location>} rItemBin
 * @param {Number} nAdjustQty
 * @param {String} sReference
 *
 * @properties={typeid:24,uuid:"4D17266B-AD11-44F7-82D4-6D479D96271D"}
 */
function createQtyAdjustmentTransaction(rItemWare, rItemBin, nAdjustQty, sReference){
	if ((rItemWare || rItemBin) && nAdjustQty && sReference) {
		var uQtyAdjustmentTypeID = getQtyAdjustmentType();
		
		if (!rItemWare && utils.hasRecords(rItemBin.in_item_warehouse_location_to_in_item_warehouse)) {
			rItemWare = rItemBin.in_item_warehouse_location_to_in_item_warehouse.getRecord(1);
		}
		
		if (uQtyAdjustmentTypeID && rItemWare && utils.hasRecords(rItemWare.in_item_warehouse_to_in_item)) {		
			/** @type {JSFoundSet<db:/avanti/tmp_item_trans_header>} */
			var fsTmpTransHeader = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'tmp_item_trans_header');
			/** @type {JSFoundSet<db:/avanti/tmp_item_trans_detail>} */
			var fsTmpTransDetail = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'tmp_item_trans_detail');
			var rItem = rItemWare.in_item_warehouse_to_in_item.getRecord(1);

			createTmpTransDetail(createTmpTransHeader());

			globals.avInventory_UpdateTransaction(fsTmpTransHeader.itemtransh_id, null, null, null, null, fsTmpTransHeader, fsTmpTransDetail);
		}
	}

	function getQtyAdjustmentType() {
		var sSQL = "SELECT I.intranstype_id \
					FROM in_transaction_type I \
					INNER JOIN app_in_transaction_type A ON A.intranstype_id = I.intranstype_appkey_uuid \
					WHERE \
						I.org_id = ? \
						AND A.intranstype_trans_code = ?";
		
		return scopes.avDB.SQLQuery(sSQL, null, [globals.org_id, scopes.avInv.TRANSACTION_TYPE.QuantityAdjustment]);
		
	}
	
	function createTmpTransHeader() {
		fsTmpTransHeader.newRecord();
		
		fsTmpTransHeader.itemtransh_transaction_no = globals.getNextItemTransactionNumber('IT');
		fsTmpTransHeader.intranstype_id = uQtyAdjustmentTypeID;
		fsTmpTransHeader.itemtransh_reference = sReference;
		fsTmpTransHeader.itemtransh_date = application.getTimeStamp();
		fsTmpTransHeader.empl_id = globals.avBase_employeeUUID;
		
		databaseManager.saveData(fsTmpTransHeader);

		return fsTmpTransHeader.itemtransh_id;
	}
	
	function createTmpTransDetail(uHeaderID) {
		fsTmpTransDetail.newRecord();

		fsTmpTransDetail.itemtransh_id = uHeaderID;
		fsTmpTransDetail.item_id = rItemWare.item_id;
		fsTmpTransDetail.itemtransd_qty = nAdjustQty;
		fsTmpTransDetail.itemwhse_id = rItemWare.itemwhse_id;
		fsTmpTransDetail.whse_id = rItemWare.whse_id;
		fsTmpTransDetail.itemwhseloc_id = rItemBin ? rItemBin.itemwhseloc_id : null;
		fsTmpTransDetail.itemtransd_whseloc_id = rItemBin ? rItemBin.whseloc_id : null;
		fsTmpTransDetail.itemtransd_comment = sReference;
		fsTmpTransDetail.uom_id = rItem.item_standard_uom_id;

		databaseManager.saveData(fsTmpTransDetail);
	}
}

/**
 * @public 
 * 
 * @param {UUID} uItemID
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"BA125EC2-4CC7-4C3C-AC78-395224A70413"}
 */
function isVirtualItem(uItemID) {
	if (uItemID) {
		return scopes.avDB.hasData("SELECT item_id FROM in_item WHERE item_id = ? AND item_is_virtual = 1", [uItemID.toString()])
	}
	else {
		return false;
	}
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/in_item>} rItem
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"C665E29A-BBCD-44E7-A4F4-72334D909A63"}
 */
function isPaper(rItem) {
	var aPaperTypes = [APP_ITEM_CLASS_TYPE.SheetPaper, APP_ITEM_CLASS_TYPE.RollPaper, APP_ITEM_CLASS_TYPE.Tabs, 
		APP_ITEM_CLASS_TYPE.Envelopes];
	
	if (rItem
			&& utils.hasRecords(rItem.in_item_to_in_item_class) 
			&& rItem.in_item_to_in_item_class.itemclass_type
			&& aPaperTypes.includes(rItem.in_item_to_in_item_class.itemclass_type)) {
		return true;
	}
	else {
		return false;
	}	
}

/**
 * This sql gets the item reserved qty/recs and is used by both in_item.item_reserved_qty() and in_reserved_dlg.getData() 
 * 
 * @public 
 * 
 * @param {UUID} uItemID
 * @param {UUID} [uWareID]
 * @param {Boolean} [bDetailDLG]
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"38E56A0A-FF41-4B2F-8FB0-9E5AEE3DE440"}
 */
function getItemReservedSQL(uItemID, uWareID, bDetailDLG) {
	var sSelect = bDetailDLG ? "SELECT ordrevditem_id " : "SELECT SUM(ordrevditem_reserved_qty) ";
	var sSQL = "FROM sa_order_revd_item di \
				INNER JOIN sa_order_revision_detail d ON di.ordrevd_id = d.ordrevd_id \
				INNER JOIN sa_order_revision_header h ON d.ordrevh_id = h.ordrevh_id \
				INNER JOIN sa_order o ON o.ordh_id = h.ordh_id \
				INNER JOIN in_item i ON i.item_id = di.item_id \
			    WHERE \
			       di.org_id = '" + globals.org_id + "' \
			       AND di.item_id = '" + uItemID + "' \
			       AND di.ordrevditem_reserved_qty > 0 \
			       AND h.ordrevh_revision = 0 \
			       AND o.ordh_document_type = 'ORD' \
				   AND h.ordrevh_order_status NOT IN ('Cancelled', 'Completed', 'Incomplete') \
				   AND i.itemtype_code NOT IN ('ZZ', 'SE', 'P')"
	
	if (uWareID) {
    	sSQL += " AND d.whse_id = '" + uWareID + "'";	
	}
	else if (bDetailDLG) {
	    if (globals.avInv_QtyDialogWarehouseUUID != null) {
	    	sSQL += " AND d.whse_id = '" + globals.avInv_QtyDialogWarehouseUUID + "'";	
	    }	    
		if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.DivPlantFilter)) {
			sSQL += " AND " + scopes.avDB.getSafeConditionsClause('d');
		}
	}
	
	return sSelect + sSQL;
}

/**
 * This sql gets the item reserved qty/recs and is used by both in_item.item_reserved_qty() and in_reserved_dlg.getData() 
 * 
 * @public 
 * 
 * @param {UUID} uItemID
 * @param {UUID} [uWareID]
 * @param {Boolean} [bDetailDLG]
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"9E07210E-296A-48D8-875A-99F3B25FB8DF"}
 */
function getItemBOSQL(uItemID, uWareID, bDetailDLG) {
	var sSelect = bDetailDLG ? "SELECT ordrevditem_id " : "SELECT SUM(ordrevditem_backorder_qty) ";
	var sSQL = "FROM sa_order_revd_item di \
				INNER JOIN sa_order_revision_detail d ON di.ordrevd_id = d.ordrevd_id \
				INNER JOIN sa_order_revision_header h ON d.ordrevh_id = h.ordrevh_id \
				INNER JOIN sa_order o ON o.ordh_id = h.ordh_id \
				INNER JOIN in_item i ON i.item_id = di.item_id \
			    WHERE \
			       di.org_id = '" + globals.org_id + "' \
			       AND di.item_id = '" + uItemID + "' \
			       AND di.ordrevditem_backorder_qty > 0 \
			       AND h.ordrevh_revision = 0 \
			       AND o.ordh_document_type = 'ORD' \
				   AND h.ordrevh_order_status NOT IN ('Cancelled', 'Incomplete') \
			       AND (h.ordrevh_order_status != 'Completed' OR ISNULL(i.item_is_virtual, 0) = 1) \
				   AND i.itemtype_code NOT IN ('ZZ', 'SE', 'P')";
	
	if (uWareID) {
    	sSQL += " AND d.whse_id = '" + uWareID + "'";	
	}
	else if (bDetailDLG) {
	    if (globals.avInv_QtyDialogWarehouseUUID != null) {
	    	sSQL += " AND d.whse_id = '" + globals.avInv_QtyDialogWarehouseUUID + "'";	
	    }	    
		if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.DivPlantFilter)) {
			sSQL += " AND " + scopes.avDB.getSafeConditionsClause('d');
		}
	}
	
	return sSelect + sSQL;
}

/**
 * This sql gets the item On PO qty/recs and is used by both item_onpo_qty() and in_onpurchaseorder_dlg.getData() 
 * 
 * @public 
 * 
 * @param {UUID} uItemID
 * @param {UUID} [uWareID]
 * @param {Boolean} [bDetailDLG]
 * @param {String} [sTransType]
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"C161E2A9-847E-49A3-82B8-195A68ED317F"}
 */
function getItemOnPOSQL(uItemID, uWareID, bDetailDLG, sTransType) {
	var sSelect = bDetailDLG ? "SELECT d.detail_id " : "SELECT SUM(detail_balance) ";
	var sSQL = "FROM _v_in_item_expected_in_details d \
    			INNER JOIN in_warehouse w ON d.whse_id = w.whse_id \
                WHERE \
                	d.org_id = '" + globals.org_id + "' \
                	AND d.item_id = '" + uItemID + "'";
	
	if (uWareID) {
    	sSQL += " AND d.whse_id = '" + uWareID + "'";	
	}
	else if (bDetailDLG) {
	    if (scopes.globals.avInv_QtyDialogWarehouseUUID != null) {
	    	sSQL += " AND d.whse_id = '" + scopes.globals.avInv_QtyDialogWarehouseUUID + "' ";
	    }
	    if (sTransType != null) {
	    	sSQL += " AND d.detail_type = '" + sTransType + "' ";
	    }
		if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.DivPlantFilter)) {
			sSQL += " AND " + scopes.avDB.getSafeConditionsClause('w');
		}
	}
	
	return sSelect + sSQL;
}

/**
 * This sql gets the item committed qty/recs and is used by both in_item.item_committed_qty() and in_committment_dlg.getData() 
 * 
 * @public 
 * 
 * @param {UUID} uItemID
 * @param {UUID} [uWareID]
 * @param {Boolean} [bDetailDLG]
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"CEDD23CF-7A24-41FA-9E85-B0F947DADB16"}
 */
function getItemCommittedSQL(uItemID, uWareID, bDetailDLG) {
	var sSelect = bDetailDLG ? "SELECT c.in_committment_id " : "SELECT SUM(c.cur_qty_committed) ";
	var sSQL = "FROM in_committment c \
	    		INNER JOIN sa_order_revd_item i ON i.ordrevditem_id = c.ordrevditem_id \
				INNER JOIN sa_order_revision_detail d ON d.ordrevd_id = i.ordrevd_id \
				INNER JOIN sa_order_revision_header h ON h.ordrevh_id = d.ordrevh_id \
		        INNER JOIN sa_order o ON o.ordh_id = h.ordh_id \
                WHERE \
                	c.org_id = '" + globals.org_id + "' \
				    AND o.ordh_document_type = 'ORD' \
				    AND c.item_id = '" + uItemID + "' \
				    AND c.cur_qty_committed > 0 \
				    AND h.ordrevh_revision = 0 \
				    AND h.ordrevh_order_status NOT IN ('Cancelled', 'Completed', 'Incomplete')";
	
	if (uWareID) {
    	sSQL += " AND d.whse_id = '" + uWareID + "'";	
	}
	else if (bDetailDLG) {
	    if (globals.avInv_QtyDialogWarehouseUUID != null) {
	    	sSQL += " AND d.whse_id = '" + globals.avInv_QtyDialogWarehouseUUID + "'";	
	    }	    
		if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.DivPlantFilter)) {
			sSQL += " AND " + scopes.avDB.getSafeConditionsClause('d');
		}
	}
	
	return sSelect + sSQL;
}

/**
 * @public 
 * 
 * @param {String} sOldValue
 * @param {String} sNewValue
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"2344E01E-01EA-4476-B175-648D8DACD63A"}
 */
function canItemTypeBeChangedWhenTransactions(sOldValue, sNewValue) {
	// only allow changing type of an item with transactions between stock and non-stock 
	var aValidTypes = [scopes.avUtils.ITEM_TYPE.Stock, scopes.avUtils.ITEM_TYPE.NonStock];
	
	return aValidTypes.includes(sOldValue) && aValidTypes.includes(sNewValue);
}


/**
 * get item decimal places quantity
 *
 * @param {UUID} uItemId
 * @returns {Number}
 * @public
 *
 * @properties={typeid:24,uuid:"1FA1EAAB-7FB2-4B2C-9B2A-E27F55E6D05E"}
 */
function getItemDecimalPlacesQuantity(uItemId) {
    var nDecimalPlaces = 0;

    if (uItemId) {
        /** @type {JSRecord<db:/avanti/in_item>} */
        var rItem = scopes.avDB.getRec("in_item", ["item_id"], [uItemId]);
        nDecimalPlaces = rItem.item_decimal_places;
    }

    return nDecimalPlaces;
}

/**
 * Get DataSet of Transaction s with a negative fifo balance
 * @param {JSRecord<db:/avanti/in_item>} rItem
 * @param {JSRecord<db:/avanti/in_item_trans_detail>} rItemTransDetail
 * 
 * @return {JSDataSet}
 *
 * @properties={typeid:24,uuid:"80BF2C70-AE49-4AA4-B8A1-110E7AD02B79"}
 */
function getDataSetOfItemTransactionsWithNegativeFifoBalance(rItem, rItemTransDetail) {
    if (rItem && rItemTransDetail && utils.hasRecords(rItemTransDetail.in_item_trans_detail_to_in_item_trans_header)) {

        var rItemTransHeader = rItemTransDetail.in_item_trans_detail_to_in_item_trans_header.getRecord(1);

        /***@type {{sql:String,
         *          args:Array,
         *          server:String,
         *          maxRows:Number,
         *          table:String}}*/
        var oSQL = {};
        oSQL.args = [globals.org_id, rItem.item_id.toString(), rItemTransDetail.whse_id.toString(), rItemTransHeader.itemtransh_transaction_no];
        oSQL.table = "in_item_trans_detail";
        oSQL.sql = "SELECT  itd.itemtransd_id \
                      FROM  in_item_trans_detail AS itd \
                      INNER JOIN in_item_trans_header AS ith ON itd.itemtransh_id =  ith.itemtransh_id \
                      WHERE (itd.org_id = ?) AND (item_id = ?) AND (whse_id = ?) AND (itemtransd_fifo_balance < 0) AND (ith.itemtransh_transaction_no <= ?)\
                      ";

        if (rItemTransDetail.itemwhseloc_id) {
            oSQL.args.push(rItemTransDetail.itemwhseloc_id.toString());
            oSQL.sql += " AND (itemwhseloc_id = ?) "
        }

        if (rItemTransDetail.initemroll_id) {
            oSQL.args.push(rItemTransDetail.initemroll_id.toString());
            oSQL.sql += " AND (initemroll_id = ?)"
        }

        if (rItemTransDetail.custproj_desc) {
            oSQL.sql += " AND custproj_desc = ? ";
            oSQL.args.push(rItemTransDetail.custproj_desc);
        }
        else {
            oSQL.sql += " AND custproj_desc IS NULL ";
        }

        oSQL.sql += " ORDER BY ith.itemtransh_transaction_no asc, itd.created_date asc, itd.itemtransd_qty desc ";

        var dsTransDetail = globals["avUtilities_sqlDataset"](oSQL);

        return dsTransDetail;

    }
    else {
        return null;
    }
}