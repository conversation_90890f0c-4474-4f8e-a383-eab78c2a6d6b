customProperties:"useCssPosition:true",
dataSource:"db:/avanti/sa_task_worktype_section",
extendsID:"67CFC51B-FCB5-406A-8E36-162B08C9F1AA",
items:[
{
cssPosition:"180,-1,-1,280,172,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"280",
right:"-1",
top:"180",
width:"172"
},
enabled:true,
labelFor:"fldImposition",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.imposition",
visible:true
},
name:"component_27F9A9F7",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"00B1BB4F-E3F0-42FE-9529-6A19AE1CDA01"
},
{
cssPosition:"104,-1,-1,10,220,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"104",
width:"220"
},
dataProviderID:"worktypesection_use_prev_sec",
enabled:true,
onDataChangeMethodID:"89318CC6-A71C-4D58-92E0-B2F6D0A67727",
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.useProductPreviousSection",
visible:true
},
name:"fldProductPreviousSection",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"0128FBEE-60D0-4C97-B9C2-7BFBB2EEF5FC"
},
{
cssPosition:"331,-1,-1,271,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"271",
right:"-1",
top:"331",
width:"140"
},
dataProviderID:"paperbrand_name",
editable:true,
enabled:true,
onDataChangeMethodID:"BFEA7656-B1EE-4792-8087-1578D1BE5C37",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"F108CB21-21BE-4008-825A-FFD7BA9906D3",
visible:true
},
name:"paperbrand_name",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"066569A5-EC62-426F-BCE0-7380E6F789DD"
},
{
cssPosition:"5,-1,-1,280,172,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"280",
right:"-1",
top:"5",
width:"172"
},
enabled:true,
styleClass:"bold label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.options",
visible:true
},
name:"component_68681C45",
styleClass:"bold label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"0729FF1B-C646-40F5-AF88-D43F775B404E"
},
{
cssPosition:"306,-1,-1,473,130,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"473",
right:"-1",
top:"306",
width:"130"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.paperSizeCaliper",
visible:true
},
name:"lblPaperCaliper",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"07DD43AE-6699-403F-B2CB-AFABB53928E1"
},
{
cssPosition:"104,-1,-1,475,148,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"475",
right:"-1",
top:"104",
width:"148"
},
enabled:true,
labelFor:"fldBleedTop",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.bleedsForWidth",
visible:true
},
name:"lblBleedsForWidth_139",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"0ED209D8-1123-4546-8E77-AFB4ABD097B1"
},
{
cssPosition:"230,-1,-1,739,174,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"739",
right:"-1",
top:"230",
width:"174"
},
dataProviderID:"multi_sig_create_folder_group",
enabled:true,
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.CreateFolderGroup",
visible:true
},
name:"multi_sig_create_folder_group",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"1B143415-771F-4B56-83DA-13FEA92F34C7"
},
{
cssPosition:"79,-1,-1,527,64,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"527",
right:"-1",
top:"79",
width:"64"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.copies",
visible:true
},
name:"lblBWCopies",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"1C10F70D-7EA6-4503-8A34-3118A62D067A"
},
{
cssPosition:"155,-1,-1,11,179,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"11",
right:"-1",
top:"155",
width:"179"
},
enabled:true,
labelFor:"worktypesection_press_sheet",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.sizeOffOfSheeter",
visible:true
},
name:"component_A13C04E8",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"1D0B0157-1503-4CA6-8873-62F4FB2E5DFE"
},
{
cssPosition:"155,-1,-1,195,75,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"195",
right:"-1",
top:"155",
width:"75"
},
dataProviderID:"worktypesection_press_sheet",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"worktypesection_press_sheet",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"1D8ECD68-8F71-411D-9DE2-014EB4BE5755"
},
{
cssPosition:"104,-1,-1,680,50,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"680",
right:"-1",
top:"104",
width:"50"
},
dataProviderID:"worktypesection_bleed_bottom",
editable:true,
enabled:true,
onDataChangeMethodID:"FA77AD65-7D64-49EA-91C4-015723312DB5",
selectOnEnter:false,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"fldBleedBottom",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"1E670117-E842-4C89-8857-BEAEB208C0B5"
},
{
cssPosition:"55,-1,-1,766,145,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"766",
right:"-1",
top:"55",
width:"145"
},
enabled:true,
labelFor:"fldPressVariable",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.machineVariable",
visible:true
},
name:"lblPressVariable",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"23EFC928-54EF-4F06-A41B-45188ABFEB9F"
},
{
cssPosition:"29,-1,-1,280,172,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"280",
right:"-1",
top:"29",
width:"172"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.typeOfWork",
visible:true
},
name:"lblTypeWork",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"24CE1AA1-7713-4657-A197-27DF113B6FBB"
},
{
cssPosition:"331,-1,-1,690,22,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"690",
right:"-1",
top:"331",
width:"22"
},
enabled:true,
onActionMethodID:"9B4B0E91-A37C-4340-913A-41233398C269",
styleClass:"listview_noborder label_bts text-center",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.lbl.clear",
visible:true
},
name:"btnClear_paper_color",
styleClass:"listview_noborder label_bts text-center",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"25F9F7A6-86E4-4E6D-935D-83310240FC76"
},
{
cssPosition:"104,-1,-1,232,221,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"232",
right:"-1",
top:"104",
width:"221"
},
dataProviderID:"sa_task_worktype_section_to_sa_task_worktype_section$prev_section.worktypesection_desc",
editable:false,
enabled:true,
selectOnEnter:false,
styleClass:"not_editable textbox_bts",
tabSeq:0,
visible:true
},
name:"fldPreviousSection",
styleClass:"not_editable textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"28D283B3-8F89-4921-B824-F622C8D81C50"
},
{
cssPosition:"256,-1,-1,10,192,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"256",
width:"192"
},
enabled:true,
styleClass:"bold label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.paper",
visible:true
},
name:"component_996C7C11",
styleClass:"bold label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"296EA635-ECAE-4D59-B8AC-49F78B5FE981"
},
{
cssPosition:"79,-1,-1,280,173,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"280",
right:"-1",
top:"79",
width:"173"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.#originalsBW",
visible:true
},
name:"lblBWOriginals",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"2A17E4CE-F1AD-48BB-BE58-D009931AD951"
},
{
cssPosition:"54,-1,-1,527,64,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"527",
right:"-1",
top:"54",
width:"64"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.copies",
visible:true
},
name:"lblColorCopies",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"2E7EB83C-9F2E-4617-8E6B-645EAEDE234A"
},
{
cssPosition:"356,-1,-1,207,258,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"207",
right:"-1",
top:"356",
width:"258"
},
dataProviderID:"item_id",
enabled:true,
onDataChangeMethodID:"706F6422-4269-4FC3-B8E0-7B015BE1D5B2",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"FB35F7AF-285F-44A5-B1D4-CDCBF8C21C89",
visible:true
},
name:"fldSelectedPaper",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"32B7709C-FC84-48AD-911C-494A3E1A490A"
},
{
cssPosition:"54,-1,-1,280,173,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"280",
right:"-1",
top:"54",
width:"173"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.#originalsColor",
visible:true
},
name:"lblColorOriginals",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"34367BC5-DC63-4279-A4E5-48177D4E20E3"
},
{
cssPosition:"205,-1,-1,195,75,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"195",
right:"-1",
top:"205",
width:"75"
},
dataProviderID:"worktypesection_parent_up",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"fldParentUp",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"40A40831-810B-4702-8AD6-9CB97F5BDF02"
},
{
cssPosition:"104,-1,-1,475,148,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"475",
right:"-1",
top:"104",
width:"148"
},
enabled:true,
labelFor:"fldBleedTop",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.bleedsForLength",
visible:true
},
name:"lblBleedsForHeight",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"40EBB752-B10C-4CD9-BAA1-F0772CAC85EE"
},
{
cssPosition:"5,-1,-1,622,81,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"622",
right:"-1",
top:"5",
width:"81"
},
dataProviderID:"worktypesection_is_cover",
enabled:true,
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.cover",
visible:true
},
name:"ordrevds_is_cover",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"41756C08-DE63-4B75-B291-D08DD8BAE4C4"
},
{
cssPosition:"331,-1,-1,608,82,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"608",
right:"-1",
top:"331",
width:"82"
},
dataProviderID:"paper_color",
editable:true,
enabled:true,
onDataChangeMethodID:"BFEA7656-B1EE-4792-8087-1578D1BE5C37",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"2BD1EAC1-E527-4981-A0CC-40CBE54F25D0",
visible:true
},
name:"paper_color",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"48AF1962-A471-4987-A704-91FC9ABEF476"
},
{
cssPosition:"356,-1,-1,10,192,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"356",
width:"192"
},
enabled:true,
labelFor:"fldSelectedPaper",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.paperSelected",
visible:true
},
name:"lblSubstrateSelected",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"4CF41425-04E0-4EAF-90A1-4803B7DDAEAB"
},
{
cssPosition:"331,-1,-1,412,22,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"412",
right:"-1",
top:"331",
width:"22"
},
enabled:true,
onActionMethodID:"9B4B0E91-A37C-4340-913A-41233398C269",
styleClass:"listview_noborder label_bts text-center",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.lbl.clear",
visible:true
},
name:"btnClear_paperbrand_name",
styleClass:"listview_noborder label_bts text-center",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"517073E0-EE88-474D-B84F-DB98FA5BE6A6"
},
{
cssPosition:"29,-1,-1,475,144,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"475",
right:"-1",
top:"29",
width:"144"
},
dataProviderID:"worktypesection_work_type",
enabled:true,
onDataChangeMethodID:"2081E503-54A0-44A7-A16C-209BF364E17C",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"246AF2E5-1158-41A4-860A-7819C8E49AF0",
visible:true
},
name:"fldTypeOfWork",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"51A52F1D-DA71-406D-9E9A-BD57BD656DA2"
},
{
cssPosition:"281,-1,-1,608,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"608",
right:"-1",
top:"281",
width:"60"
},
dataProviderID:"sa_task_worktype_section_to_in_item_paper.paper_m_weight",
editable:false,
enabled:true,
selectOnEnter:false,
styleClass:"not_editable textbox_bts",
tabSeq:0,
visible:true
},
name:"component_8D6328EC",
styleClass:"not_editable textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"5635ECB2-C005-4530-8A6A-FC99B0CE8844"
},
{
cssPosition:"406,-1,-1,10,70,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"406",
width:"70"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.color",
visible:true
},
name:"component_AE594803",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"578B484C-CC82-44E3-81CD-9F3E6292FD4B"
},
{
cssPosition:"205,-1,-1,709,25,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"709",
right:"-1",
top:"205",
width:"25"
},
enabled:true,
onActionMethodID:"B3B5C984-7B89-453A-8AC7-018CDE710078",
styleClass:"listview_noborder label_bts",
tabSeq:0,
text:"%%globals.icon_clear%%",
toolTipText:"i18n:avanti.lbl.clear",
visible:true
},
name:"component_9B006A7C",
styleClass:"listview_noborder label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"5B09EF4B-BF7A-4688-B450-ABF77418F2C2"
},
{
cssPosition:"79,-1,-1,680,50,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"680",
right:"-1",
top:"79",
width:"50"
},
dataProviderID:"worktypesection_bleed_bottom",
editable:true,
enabled:true,
onDataChangeMethodID:"FA77AD65-7D64-49EA-91C4-015723312DB5",
selectOnEnter:false,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"fldBleedBottom_139",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"5D5386B8-7752-4212-80BD-832736D4AF3E"
},
{
cssPosition:"331,-1,-1,473,130,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"473",
right:"-1",
top:"331",
width:"130"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.item_color",
visible:true
},
name:"lblPaperColour",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"65584338-929C-4ED0-9C35-850F2C1F7084"
},
{
cssPosition:"104,-1,-1,627,50,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"627",
right:"-1",
top:"104",
width:"50"
},
dataProviderID:"worktypesection_bleed_left",
editable:true,
enabled:true,
onDataChangeMethodID:"FA77AD65-7D64-49EA-91C4-015723312DB5",
selectOnEnter:false,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"fldBleedLeft_139",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"659674D7-37A0-4FCB-BDF1-A459015F8298"
},
{
cssPosition:"356,-1,-1,608,55,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"608",
right:"-1",
top:"356",
width:"55"
},
dataProviderID:"sa_task_worktype_section_to_in_item_paper.item_number_of_tabs",
editable:false,
enabled:true,
selectOnEnter:false,
styleClass:"not_editable textbox_bts",
tabSeq:0,
visible:true
},
name:"fldItemTabs",
styleClass:"not_editable textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"67399FFB-6C02-4124-B74D-27AFA7E6E364"
},
{
cssPosition:"54,-1,-1,475,48,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"475",
right:"-1",
top:"54",
width:"48"
},
dataProviderID:"worktypesection_copies_colr",
editable:true,
enabled:true,
onDataChangeMethodID:"8A29AA71-6DE4-41CB-B1E2-77B38905675A",
selectOnEnter:false,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"fldColorCopies",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"68BAFA6E-8168-4E21-AF6A-9C2C31C9BE0E"
},
{
cssPosition:"281,-1,-1,206,205,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"206",
right:"-1",
top:"281",
width:"205"
},
dataProviderID:"paper_brand_name",
editable:true,
enabled:true,
onDataChangeMethodID:"BFEA7656-B1EE-4792-8087-1578D1BE5C37",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"7309F79E-BFE5-4609-82E1-A5E02D41F8F4",
visible:true
},
name:"paper_brand_name",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"6CC0AE90-9732-4055-ABB9-9FB88C78B50A"
},
{
cssPosition:"179,-1,-1,475,230,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"475",
right:"-1",
top:"179",
width:"230"
},
dataProviderID:"worktypesection_imposition",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"0AA09FF6-6319-4B34-8115-ACB3E75AB3A2",
visible:true
},
name:"fldImposition",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"6E4AE857-48FA-4F4D-B8AE-A577104153A9"
},
{
cssPosition:"230,-1,-1,195,75,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"195",
right:"-1",
top:"230",
width:"75"
},
dataProviderID:"worktypesection_cuts_pretrim",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"fldNrCuts",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"70A79CCF-90C7-40A1-9431-A8BADB46B7A4"
},
{
cssPosition:"79,-1,-1,475,148,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"475",
right:"-1",
top:"79",
width:"148"
},
enabled:true,
labelFor:"fldBleedLeft",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.bleedsForWidth",
visible:true
},
name:"lblBleedsForWidth",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"726C7B49-9042-4580-AC9B-6CF8BF7653F1"
},
{
cssPosition:"130,-1,-1,11,179,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"11",
right:"-1",
top:"130",
width:"179"
},
enabled:true,
labelFor:"worktypesection_roll_to_sheet",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.convertRollToSheet",
visible:true
},
name:"component_A0419A95",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"763A5158-C0B7-4D4F-B701-904AFF4A6500"
},
{
cssPosition:"130,-1,-1,280,181,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"280",
right:"-1",
top:"130",
width:"181"
},
enabled:true,
labelFor:"fldFinishedSizeAreaCalc",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.flatSizeAreaCalculation",
visible:true
},
name:"lblFlatSizeAreaCalculation",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"7751688C-ED07-4A5F-A3EB-3F0C9E57A0C6"
},
{
cssPosition:"29,-1,-1,622,111,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"622",
right:"-1",
top:"29",
width:"111"
},
dataProviderID:"worktypesection_flg_collate",
enabled:true,
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.collate",
visible:true
},
name:"worktypesection_flg_collate",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"77AEE619-4C23-449B-9174-BFE065615FF5"
},
{
cssPosition:"433,289,7,10,700,215",
json:{
containedForm:"EF09B2D6-4301-4E14-8C68-26032AB523C7",
cssPosition:{
bottom:"7",
height:"215",
left:"10",
right:"289",
top:"433",
width:"700"
},
relationName:"sa_task_worktype_section_to_sa_task_worktype_ink",
visible:true
},
name:"tabInks",
typeName:"servoycore-formcontainer",
typeid:47,
uuid:"80B7BCAA-9388-498D-B59A-104C5E2B46B3"
},
{
cssPosition:"54,-1,-1,594,111,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"594",
right:"-1",
top:"54",
width:"111"
},
dataProviderID:"worktypesection_copysides_colr",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"A1B785A0-7F99-4BA5-AA07-EB63FBE5DA2F",
visible:true
},
name:"fldColorSides",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"842E0799-47FD-414D-BF21-61100EAF2502"
},
{
cssPosition:"104,-1,-1,627,50,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"627",
right:"-1",
top:"104",
width:"50"
},
dataProviderID:"worktypesection_bleed_top",
editable:true,
enabled:true,
onDataChangeMethodID:"FA77AD65-7D64-49EA-91C4-015723312DB5",
selectOnEnter:false,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"fldBleedTop",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"865D2C5B-F1E6-479E-9F9E-A53FE9018819"
},
{
cssPosition:"381,-1,-1,10,192,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"381",
width:"192"
},
enabled:true,
styleClass:"bold label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.inks",
visible:true
},
name:"component_2DA81696",
styleClass:"bold label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"8C46EBB8-A3F0-4743-B609-81BF3F5B828C"
},
{
cssPosition:"205,-1,-1,281,191,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"281",
right:"-1",
top:"205",
width:"191"
},
enabled:true,
labelFor:"fldCombination",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.Combination(ImpositionModel)",
visible:true
},
name:"component_1A933734",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"8F9AAEF6-7017-442A-898A-BB19A96938F3"
},
{
cssPosition:"281,-1,-1,181,24,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"181",
right:"-1",
top:"281",
width:"24"
},
enabled:true,
onActionMethodID:"4B5768BE-7AAB-4109-BA93-DACCFFBD627D",
styleClass:"label_bts text-center",
tabSeq:0,
text:"%%globals.icon_lookup%%",
toolTipText:"i18n:avanti.tooltip.lookup",
visible:true
},
name:"btnLookupPaper",
styleClass:"label_bts text-center",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"97ECB1D7-DD05-4C90-9761-AE0E536570F1"
},
{
cssPosition:"29,-1,-1,11,88,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"11",
right:"-1",
top:"29",
width:"88"
},
dataProviderID:"worktypesection_flg_folder",
enabled:true,
onDataChangeMethodID:"9367EC21-2A99-42B5-8384-7AFEDA8D6D15",
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.folder",
visible:true
},
name:"fldSysFolder",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"98B7F939-78EC-4094-8266-2FF1B196A516"
},
{
cssPosition:"356,-1,-1,473,130,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"473",
right:"-1",
top:"356",
width:"130"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.numberOfTabs",
visible:true
},
name:"lblItemTabs",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"9C5DAFFB-CC83-4B29-98BE-DE16A2D94558"
},
{
cssPosition:"306,-1,-1,412,22,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"412",
right:"-1",
top:"306",
width:"22"
},
enabled:true,
onActionMethodID:"9B4B0E91-A37C-4340-913A-41233398C269",
styleClass:"listview_noborder label_bts text-center",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.lbl.clear",
visible:true
},
name:"btnClear_papergrade_name",
styleClass:"listview_noborder label_bts text-center",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"9E015BFE-1BA2-4854-9715-F20A0C77088C"
},
{
cssPosition:"180,-1,-1,195,75,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"195",
right:"-1",
top:"180",
width:"75"
},
dataProviderID:"worktypesection_press_sheet",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"fldPressSheetSize",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"9E58698C-1EA2-4573-915D-8CD835FF5A48"
},
{
cssPosition:"180,-1,-1,10,180,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"180",
width:"180"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.pressSheetSize_196",
visible:false
},
name:"lblPressSheetSize_196",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"A0A8B29D-8AC9-448B-9F75-90855022F689",
visible:false
},
{
cssPosition:"205,-1,-1,10,180,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"205",
width:"180"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.#fromParent_2",
visible:true
},
name:"lblNrFromParent",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"A0CB8BB4-2C53-4A77-B33E-0094E900DC7A"
},
{
cssPosition:"54,-1,-1,627,50,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"627",
right:"-1",
top:"54",
width:"50"
},
dataProviderID:"worktypesection_bleed_size",
editable:true,
enabled:true,
format:"#.####",
onDataChangeMethodID:"61EC999D-6259-4DF7-BD91-5EB569B80BDF",
selectOnEnter:false,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"fldBleedSize",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"A2E18686-6EFF-46F9-ADEB-EF0F9807E259"
},
{
cssPosition:"281,-1,-1,412,22,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"412",
right:"-1",
top:"281",
width:"22"
},
enabled:true,
onActionMethodID:"9B4B0E91-A37C-4340-913A-41233398C269",
styleClass:"listview_noborder label_bts text-center",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.lbl.clear",
visible:true
},
name:"btnClear_paper_brand_name",
styleClass:"listview_noborder label_bts text-center",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"A415612A-9777-4454-BEE0-329A357ABBCE"
},
{
cssPosition:"79,-1,-1,594,111,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"594",
right:"-1",
top:"79",
width:"111"
},
dataProviderID:"worktypesection_copysides_bw",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"A1B785A0-7F99-4BA5-AA07-EB63FBE5DA2F",
visible:true
},
name:"fldBWSides",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"A48360FD-FED6-45E9-87C3-F61BACD8E2BD"
},
{
cssPosition:"331,-1,-1,206,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"206",
right:"-1",
top:"331",
width:"60"
},
dataProviderID:"paper_weight",
editable:true,
enabled:true,
onDataChangeMethodID:"BFEA7656-B1EE-4792-8087-1578D1BE5C37",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"5FAFEDF3-ECAB-4C10-BBE8-88AA4F872913",
visible:true
},
name:"paper_weight",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"AE8151B3-6F86-4C85-82EE-877C64E7EE22"
},
{
cssPosition:"79,-1,-1,475,48,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"475",
right:"-1",
top:"79",
width:"48"
},
dataProviderID:"worktypesection_copies_bw",
editable:true,
enabled:true,
onDataChangeMethodID:"8A29AA71-6DE4-41CB-B1E2-77B38905675A",
selectOnEnter:false,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"fldBWCopies",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"AF3998B9-6B9F-4C46-9EE0-81FF161B86C9"
},
{
cssPosition:"205,-1,-1,475,230,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"475",
right:"-1",
top:"205",
width:"230"
},
dataProviderID:"worktypesection_combination",
editable:true,
enabled:true,
onDataChangeMethodID:"CD3FC409-2F1E-4CAF-9EF6-25839A36E640",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"fldCombination",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"AFC68945-5F32-48C0-A42C-34BBAE4E7E7D"
},
{
cssPosition:"130,-1,-1,476,65,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"476",
right:"-1",
top:"130",
width:"65"
},
dataProviderID:"worktypesection_fin_area",
editable:true,
enabled:true,
format:"#.####",
onDataChangeMethodID:"E5B5F94F-5746-485E-8368-77141F53F34A",
selectOnEnter:true,
styleClass:"text-center textbox_bts",
tabSeq:0,
visible:false
},
name:"fldFinishedSizeAreaCalc",
styleClass:"text-center textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"B3B79521-46B3-45A1-A961-6458FF190120",
visible:false
},
{
cssPosition:"331,-1,-1,10,166,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"331",
width:"166"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.paperGradeBrand",
visible:true
},
name:"lblPaperBrand",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"B8A9EC7D-E10A-4B22-B867-5AC3B712BAF2"
},
{
cssPosition:"306,-1,-1,10,192,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"306",
width:"192"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.paperGrade",
visible:true
},
name:"papergrade_id_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"BB26CB44-A53F-4CAA-AC18-17867B4653FD"
},
{
cssPosition:"180,-1,-1,10,180,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"180",
width:"180"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.pressSheetSize",
visible:false
},
name:"lblPressSheetSize",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"BD948C13-72F6-45B6-B37E-69DAB4BA6479",
visible:false
},
{
cssPosition:"54,-1,-1,11,173,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"11",
right:"-1",
top:"54",
width:"173"
},
dataProviderID:"worktypesection_flg_pretrim",
enabled:true,
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.preTrim",
visible:true
},
name:"component_207A2F12",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"BDE000C9-646F-4009-85C1-C7129B17132A"
},
{
cssPosition:"155,-1,-1,475,71,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"475",
right:"-1",
top:"155",
width:"71"
},
dataProviderID:"worktypesection_across_roll",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"worktypesection_across_roll",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"BF78477E-D153-4A92-ADE9-C0BA6740C815"
},
{
cssPosition:"230,-1,-1,475,230,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"475",
right:"-1",
top:"230",
width:"230"
},
dataProviderID:"worktypesection_up_combo",
editable:true,
enabled:true,
onDataChangeMethodID:"A36967F1-3298-41E6-B732-87AC57314E98",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"fldUpCombo",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"C2DA77AF-AB21-46DD-BED4-B2B0C68216E6"
},
{
cssPosition:"130,-1,-1,539,44,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"539",
right:"-1",
top:"130",
width:"44"
},
enabled:true,
labelFor:"fldFinishedSizeAreaCalc",
styleClass:"text-center label_bts",
tabSeq:-1,
text:"Sq.Ft.",
visible:true
},
name:"lblSquareFeet",
styleClass:"text-center label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"C3FE89CC-DF12-4ECA-8FDC-3E314ACEF500"
},
{
cssPosition:"306,-1,-1,206,205,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"206",
right:"-1",
top:"306",
width:"205"
},
dataProviderID:"papergrade_name",
editable:true,
enabled:true,
onDataChangeMethodID:"BFEA7656-B1EE-4792-8087-1578D1BE5C37",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"ADEDCDDF-2B32-486A-B06B-7577E9AB0414",
visible:true
},
name:"papergrade_name",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"C506EEDD-BA9F-41FC-A64D-FAD155E2CFF1"
},
{
cssPosition:"230,-1,-1,281,172,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"281",
right:"-1",
top:"230",
width:"172"
},
enabled:true,
labelFor:"fldUpCombo",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.#UpCombo",
visible:true
},
name:"component_E31EF9C5",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"C5291116-D89F-4CA3-881E-0561FDB51706"
},
{
cssPosition:"180,-1,-1,709,25,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"709",
right:"-1",
top:"180",
width:"25"
},
enabled:true,
onActionMethodID:"14FDFE49-81F7-448B-8C55-AE73811378DE",
styleClass:"listview_noborder label_bts",
tabSeq:0,
text:"%%globals.icon_clear%%",
toolTipText:"i18n:avanti.lbl.clear",
visible:true
},
name:"component_ED34E134",
styleClass:"listview_noborder label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"CB32373F-B83C-4026-9AD9-4693C31504BC"
},
{
cssPosition:"79,-1,-1,10,174,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"79",
width:"174"
},
dataProviderID:"worktypesection_flg_finaltrim",
enabled:true,
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.finalTrim",
visible:true
},
name:"component_701C6306",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"CBFCA7B9-E643-465D-9627-1CEDD5BC1487"
},
{
cssPosition:"130,-1,-1,195,86,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"195",
right:"-1",
top:"130",
width:"86"
},
dataProviderID:"worktypesection_roll_to_sheet",
enabled:true,
onDataChangeMethodID:"07F00027-A335-4F13-99F9-0655DEC17BE4",
selectSize:5,
styleClass:"checkbox_column",
tabSeq:0,
valuelistID:"161FEE62-CB9F-4A4F-979F-5631D1E12587",
visible:true
},
name:"worktypesection_roll_to_sheet",
styleClass:"checkbox_column",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"D44B7042-272B-4C77-BFE0-53E571CF664F"
},
{
cssPosition:"406,-1,-1,165,180,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"165",
right:"-1",
top:"406",
width:"180"
},
enabled:true,
styleClass:"label_bts text-right",
tabSeq:-1,
text:"i18n:avanti.lbl.inkCoverage",
visible:true
},
name:"component_B42B2783",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"D6E0154F-8854-4E52-B6A2-BCE3ECB1B718"
},
{
cssPosition:"29,-1,-1,98,132,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"98",
right:"-1",
top:"29",
width:"132"
},
dataProviderID:"jdffold_id",
enabled:true,
onDataChangeMethodID:"9367EC21-2A99-42B5-8384-7AFEDA8D6D15",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"730EC5E9-CC02-496C-96A8-8E96D3AAFF08",
visible:false
},
name:"fldFoldPatterns",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"D740EE61-9304-4F45-8D8F-1E82FFC9AA43",
visible:false
},
{
cssPosition:"306,-1,-1,608,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"608",
right:"-1",
top:"306",
width:"60"
},
dataProviderID:"sa_task_worktype_section_to_in_item_paper.paper_caliper",
editable:false,
enabled:true,
format:"0.####|0.####",
selectOnEnter:false,
styleClass:"not_editable textbox_bts",
tabSeq:0,
visible:true
},
name:"component_3E40F38C",
styleClass:"not_editable textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"D8525CD6-5CFC-4186-9F07-68D91081FE87"
},
{
cssPosition:"54,-1,-1,475,148,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"475",
right:"-1",
top:"54",
width:"148"
},
enabled:true,
labelFor:"fldBleedSize",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.bleedSizeLong",
visible:true
},
name:"component_DCB05F68",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"D8B5CEFF-CDC5-4DDD-BB6D-5373E804F964"
},
{
cssPosition:"230,-1,-1,11,180,23",
json:{
cssPosition:{
bottom:"-1",
height:"23",
left:"11",
right:"-1",
top:"230",
width:"180"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.#PreTrimCuts",
visible:true
},
name:"lblNrCuts",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"DA16C9D2-7AF2-4D9C-A0F8-E94A8966E189"
},
{
cssPosition:"130,-1,-1,462,13,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"462",
right:"-1",
top:"130",
width:"13"
},
enabled:true,
labelFor:"fldFinishedSizeAreaCalc",
styleClass:"over_warning label_bts text-center",
tabSeq:-1,
text:"%%clc_over_finished_area%%",
visible:true
},
name:"modFinishedSizeAreaCalc",
styleClass:"over_warning label_bts text-center",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"DAB16EC7-A5BD-4E61-B07D-D2EBFB4A6A86"
},
{
cssPosition:"5,-1,-1,11,192,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"11",
right:"-1",
top:"5",
width:"192"
},
enabled:true,
styleClass:"bold label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.systemTasks",
visible:true
},
name:"component_B5D5AFFB",
styleClass:"bold label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"DB151551-7689-4920-931C-08D4B47DFEAC"
},
{
cssPosition:"406,-1,-1,350,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"350",
right:"-1",
top:"406",
width:"60"
},
dataProviderID:"worktypesection_ink_coverage",
editable:true,
enabled:true,
onDataChangeMethodID:"191A8900-CC7B-4AD0-95FB-5ACF0DEECEB9",
selectOnEnter:false,
styleClass:"text-center textbox_bts",
tabSeq:0,
visible:true
},
name:"fldCoverage",
styleClass:"text-center textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"DBBD23D0-DEB5-40BA-A788-2613C6225801"
},
{
cssPosition:"104,-1,-1,680,50,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"680",
right:"-1",
top:"104",
width:"50"
},
dataProviderID:"worktypesection_bleed_right",
editable:true,
enabled:true,
onDataChangeMethodID:"FA77AD65-7D64-49EA-91C4-015723312DB5",
selectOnEnter:false,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"fldBleedRight_139",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"DF745B3F-CF40-4662-A826-B1DD56661786"
},
{
cssPosition:"281,-1,-1,473,130,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"473",
right:"-1",
top:"281",
width:"130"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.mWeight",
visible:true
},
name:"paper_m_weight_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"DF834970-41F3-41A4-9C05-53E2062E963E"
},
{
cssPosition:"79,-1,-1,627,50,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"627",
right:"-1",
top:"79",
width:"50"
},
dataProviderID:"worktypesection_bleed_top",
editable:true,
enabled:true,
onDataChangeMethodID:"FA77AD65-7D64-49EA-91C4-015723312DB5",
selectOnEnter:false,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"fldBleedTop_139",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"DFAD857B-52CC-4458-8319-74F86E9BD21D"
},
{
cssPosition:"205,-1,-1,739,207,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"739",
right:"-1",
top:"205",
width:"207"
},
dataProviderID:"multi_sig_cutter_on",
enabled:true,
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.CutterMultipleSignatures",
visible:true
},
name:"multi_sig_cutter_on",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"E88E1FCE-ABC9-4826-AB53-47160E09CBC3"
},
{
cssPosition:"230,-1,-1,709,25,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"709",
right:"-1",
top:"230",
width:"25"
},
enabled:true,
onActionMethodID:"DB8BB2E1-A820-4D36-ADEA-ABA85A8638DB",
styleClass:"listview_noborder label_bts",
tabSeq:0,
text:"%%globals.icon_clear%%",
toolTipText:"i18n:avanti.lbl.clear",
visible:true
},
name:"component_7DDDE61A",
styleClass:"listview_noborder label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"E8A10C4F-827F-416E-A8BD-6B95ED36A534"
},
{
cssPosition:"5,-1,-1,766,145,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"766",
right:"-1",
top:"5",
width:"145"
},
enabled:true,
labelFor:"fldPressPool",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.pressPool",
visible:true
},
name:"lblPressPool",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"ECC9FCF4-23A2-4178-92B5-9DD5EA99BC2F"
},
{
height:655,
partType:5,
typeid:19,
uuid:"EDB8A10E-5262-4A70-A630-4F8E13914332"
},
{
cssPosition:"79,-1,-1,475,148,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"475",
right:"-1",
top:"79",
width:"148"
},
enabled:true,
labelFor:"fldBleedLeft",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.bleedsForHeight",
visible:true
},
name:"lblBleedsForHeight_139",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"EE2F5B7D-CF71-4E23-A3CA-61FC8F5C887C"
},
{
cssPosition:"79,-1,-1,627,50,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"627",
right:"-1",
top:"79",
width:"50"
},
dataProviderID:"worktypesection_bleed_left",
editable:true,
enabled:true,
onDataChangeMethodID:"FA77AD65-7D64-49EA-91C4-015723312DB5",
selectOnEnter:false,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"fldBleedLeft",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"F0E4F9DF-B06E-478D-84C2-166D454CC780"
},
{
cssPosition:"406,-1,-1,138,22,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"138",
right:"-1",
top:"406",
width:"22"
},
enabled:true,
onActionMethodID:"C4804FF3-1143-4968-A12F-CC38A2D99AA3",
styleClass:"listview_noborder label_bts",
tabSeq:0,
text:"%%globals.icon_refresh%%",
toolTipText:"i18n:avanti.lbl.refreshInkColors",
visible:true
},
name:"btnAdd",
styleClass:"listview_noborder label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"F2ACADFC-5225-4CC4-8407-B479C9658FD8"
},
{
cssPosition:"406,-1,-1,85,50,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"85",
right:"-1",
top:"406",
width:"50"
},
dataProviderID:"worktypesection_colours",
editable:true,
enabled:true,
onDataChangeMethodID:"110D451E-B75E-436B-825B-6871BFF7C26D",
selectOnEnter:false,
styleClass:"text-center textbox_bts",
tabSeq:0,
visible:true
},
name:"fldColors",
styleClass:"text-center textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"F52ABC90-93D0-465E-882E-1AC432F4EDFB"
},
{
cssPosition:"79,-1,-1,680,50,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"680",
right:"-1",
top:"79",
width:"50"
},
dataProviderID:"worktypesection_bleed_right",
editable:true,
enabled:true,
onDataChangeMethodID:"FA77AD65-7D64-49EA-91C4-015723312DB5",
selectOnEnter:false,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"fldBleedRight",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"F561BA4B-C44E-42D0-A36D-983E42E24849"
},
{
cssPosition:"281,-1,-1,10,166,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"281",
width:"166"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.brandName",
visible:true
},
name:"paper_brand_name_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"F5639099-080F-45F1-A996-30E52136C334"
},
{
cssPosition:"155,-1,-1,280,172,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"280",
right:"-1",
top:"155",
width:"172"
},
enabled:true,
labelFor:"worktypesection_across_roll",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.sheetsAcrossRoll",
visible:true
},
name:"component_9F16C04C",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"F7C22818-8DC3-4991-9E66-FCDB0FF76E8D"
},
{
cssPosition:"29,-1,-1,766,228,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"766",
right:"-1",
top:"29",
width:"228"
},
dataProviderID:"scopes.avTask.selectedWorkTypePressID",
enabled:true,
onDataChangeMethodID:"28E39360-FF94-48CC-813A-4EDD76EC4555",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"CCE9B8AF-87C5-4965-8CD0-ADCD406E119C",
visible:true
},
name:"fldPressPool",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"F7F6194A-655B-413B-8676-52E43F91870B"
},
{
cssPosition:"79,-1,-1,766,228,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"766",
right:"-1",
top:"79",
width:"228"
},
dataProviderID:"_to_sa_task_worktype_press$selectedworktypepressid.worktypepress_variable",
enabled:true,
onDataChangeMethodID:"B47BF2CB-0880-44F5-9940-A1DE8905173D",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"CAF27AD4-E2CB-4CCF-89E7-FF1ECAF7D9BD",
visible:true
},
name:"fldPressVariable",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"F81D8FC0-8BDC-43E4-AE00-C1AA15C2C223"
},
{
cssPosition:"331,-1,-1,181,27,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"181",
right:"-1",
top:"331",
width:"27"
},
enabled:true,
onActionMethodID:"9B4B0E91-A37C-4340-913A-41233398C269",
styleClass:"listview_noborder label_bts text-center",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.lbl.clear",
visible:true
},
name:"btnClear_paper_weight",
styleClass:"listview_noborder label_bts text-center",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"FFEE5AD8-1350-4110-8A61-72FA75019510"
}
],
name:"sa_task_worktype_section_dlg",
onRecordSelectionMethodID:"9293C60E-E544-4580-B9BD-41BE41E0C47E",
onShowMethodID:"619908C2-7FE8-49CE-AF0A-5314BF2D043E",
scrollbars:33,
showInMenu:false,
size:"999,655",
styleName:"Avanti",
typeid:3,
uuid:"6E361593-466E-4E2A-B72A-1529F86A5B30"