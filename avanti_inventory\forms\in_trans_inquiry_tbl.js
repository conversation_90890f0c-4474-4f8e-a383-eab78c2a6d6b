/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"E3EAC945-3C25-4C92-BDD3-422546A842BD",variableType:4}
 */
var _gridReady = 0;

/**
 * Called when the table is ready to be shown.
 *
 * @protected
 *
 *
 * @properties={typeid:24,uuid:"FECBEA66-E2CD-4A87-90F7-5570177EC292"}
 */
function onReady() {
    _gridReady = 1;
}

/**
 * @param _firstShow
 * @param _event
 *
 * @properties={typeid:24,uuid:"15471A36-F1FE-4F3A-99C1-FE852CB75002"}
 */
function onShowForm(_firstShow, _event) {

    if (_firstShow) {
    	if (!_gridReady) {
    		application.executeLater(onShowForm, 500, [true, _event]);
    		return null;
    	}
    }

    forms.in_trans_inquiry_tbl_search.controller.enabled = true;
    forms.in_trans_inquiry_tbl_search.controller.readOnly = false;
    forms.svy_nav_fr_buttonbar_browser.elements.btn_export.enabled = true;
    _super.onShowForm(_firstShow, _event);

    controller.enabled = true;
    forms.in_trans_inquiry_tbl_search.controller.enabled = true;
    forms.in_trans_inquiry_tbl_search.controller.readOnly = false;

    forms.in_trans_inquiry_tbl_search.elements.transaction_date.format = globals.avBase_dateFormat;
    forms.in_trans_inquiry_tbl_search.elements.transaction_to_date.format = globals.avBase_dateFormat;
     
    elements.btnApplyBatchFilter.enabled = _batchFilter == 1;

    if (application.getValueListItems('vl_inquiryTransactionTypesWithEmptyValue').getMaxRowIndex() == 1) {
        /*** @type {JSDataSet} */
        var dataSet = application.getValueListItems('vl_transactionTypesWithEmptyValue');
        //remove the empty value, as it is used in a checkbox group;
        dataSet.removeRow(1)
        for (var i = 1; i <= dataSet.getMaxRowIndex(); i++) {
				dataSet.setValue(i, 2, dataSet.getValue(i, 1));
        }
        dataSet.addRow(["Purchase Order", "Purchase Order"]);
        dataSet.addRow(["Sales Order", "Sales Order"]);
        dataSet.addRow(["Price Change", "Price Change"]);
        dataSet.addRow(["Pick", "Pick"]);
        dataSet.addRow(["Count", "Count"]);
        dataSet.addRow(["Job", "Job"]);

        dataSet.sort(1, true);

        application.setValueListItems('vl_inquiryTransactionTypesWithEmptyValue', dataSet);

        /*** @type {JSDataSet} */
        var dataSet2 = databaseManager.createEmptyDataSet();
        dataSet2.addRow(["", null]);
        dataSet2.addRow(["Pending Change", "Pending Change"]);
        dataSet2.addRow(["Approved Change", "Approved Change"]);
        dataSet2.addRow(["Staged", "Staged"]);
        dataSet2.addRow(["Partially Shipped", "Partially Shipped"]);
        dataSet2.addRow(["Open", "Open"]);
        dataSet2.addRow(["On Hold", "On Hold"]);
        dataSet2.addRow(["Incomplete", "Incomplete"]);
        dataSet2.addRow(["Posted", "Posted"]);
        dataSet2.addRow(["Released Change", "Released Change"]);
        dataSet2.addRow(["Shipped", "Shipped"]);
        dataSet2.addRow(["Released For Prep", "Released For Prep"]);
        dataSet2.addRow(["Released", "Released"]);
        dataSet2.addRow(["Accounting Hold", "Accounting Hold"]);
        dataSet2.addRow(["Deleted Change", "Deleted Change"]);
        dataSet2.addRow(["Cancelled Change", "Cancelled Change"]);
        dataSet2.addRow(["Cancelled", "Cancelled"]);
        dataSet2.addRow(["Open Change", "Open Change"]);

        dataSet2.addRow(["Updated", "Updated"]);

        dataSet2.addRow(["Closed", "Closed"]);
        dataSet2.addRow(["Received", "Received"]);

        dataSet2.addRow(["InPicking", "InPicking"]);
        dataSet2.addRow(["Picked", "Picked"]);
        dataSet2.addRow(["PickedStaged", "PickedStaged"]);

        dataSet2.addRow(["Started", "Started"]);
        dataSet2.addRow(["Entered", "Entered"]);
        dataSet2.addRow(["Confirmed", "Confirmed"]);

        dataSet2.addRow(["Confirmed Printed", "Confirmed Printed"]);
        dataSet2.addRow(["Invoiced", "Invoiced"]);

        dataSet2.addRow(["PartiallyShipped", "PartiallyShipped"]);
        dataSet2.addRow(["OnHold", "OnHold"]);
        dataSet2.addRow(["Scheduled", "Scheduled"]);
        dataSet2.addRow(["Completed", "Completed"]);
        dataSet2.addRow(["Partially Received", "Partially Received"]);

        dataSet2.sort(1, true);

        application.setValueListItems('vl_inquiryTransactionStatusesWithEmptyValue', dataSet2);

    }
    controller.readOnly = false;
	elements.grid.setReadOnly(true);
}

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"9F14ABF8-65CD-49BB-B365-DF9C939865B4"}
 */
var itemCostFld_tooltip = 'Cost / Unit';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"543B262D-87C2-4FB9-A95E-BE64CF279D90"}
 */
var transrefFld_tooltip = 'Transaction Reference number';

/**
 * Called when the mouse is clicked on a row/cell (foundset and column indexes are given).
 * the foundsetindex is always -1 when there are grouped rows
 *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param {JSRecord} record
 * @param {JSEvent} event
 *
 * @private
 *
 * @properties={typeid:24,uuid:"FBFDF87C-744D-4EC9-AAB9-9437BAB0D6C2"}
 * @AllowToRunInFind
 */
function onCellClick(foundsetindex, columnindex, record, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		application.output("Cell click not handled in group mode", LOGGINGLEVEL.WARNING);		return;
	}
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "btn_template0" && col.styleClass && col.styleClass.search(' disabled') == -1) {
		scopes.globals.svy_nav_toggleView(event);
	}
}

/**
 * Called when the mouse is double clicked on a row/cell (foundset and column indexes are given).
 * the foundsetindex is always -1 when there are grouped rows
 *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param {JSRecord} record
 * @param {JSEvent} event
 *
 * @private
 *
 * @SuppressWarnings(unused)
 * @properties={typeid:24,uuid:"B82C4A2E-72AA-4779-8F21-809A37B145C9"}
 */
function onCellDoubleClick(foundsetindex, columnindex, record, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		application.output("Cell right click not handled in group mode", LOGGINGLEVEL.WARNING);		return;
	}
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	scopes.globals.svy_nav_toggleView(event)
}

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"D9E3823F-9478-4D58-83F6-B069BDC7D298",variableType:4}
 */
var _batchFilter = 1;

/**
 * @properties={typeid:35,uuid:"760E11CA-4E26-4557-BFB4-01618672C7B3",variableType:-4}
 */
var _aSelectedTransType = [];

/**
 * @properties={typeid:35,uuid:"7BBBBAF1-728F-4B27-A5CA-B664EF76F05B",variableType:-4}
 */
var _aSelectedAdjustmentType = [];

/**
 * @param event
 *
 * @properties={typeid:24,uuid:"0815674F-6122-498D-8CEE-10B086202B6E"}
 */
function onLoad(event) {
	_super.onLoad(event);
	controller.enabled = true;
	forms.in_trans_inquiry_tbl_search.controller.enabled = true;
	forms.in_trans_inquiry_tbl_search.controller.readOnly = false;
	forms.svy_nav_fr_buttonbar_browser.elements.btn_export.enabled = true;
	
	elements.grid.getColumn(elements.grid.getColumnIndex("transQtyFld")).format = globals.avUtilities_ItemQtyFormat(2);
	elements.grid.getColumn(elements.grid.getColumnIndex("itemCostFld")).format = globals.avUtilities_ItemQtyFormat(2);
	elements.grid.getColumn(elements.grid.getColumnIndex("totalCostFld")).format = globals.avUtilities_ItemQtyFormat(2);	
}

/**
 * @param event
 *
 * @properties={typeid:24,uuid:"17C36C3A-D300-4C5F-800A-892CD974C45F"}
 */
function onHide(event) {
	_super.onHide(event);
}

/**
 * @param _event
 *
 * @properties={typeid:24,uuid:"82DA53AC-DB1C-4715-A7AF-8D1478BB3F94"}
 */
function onUnload(_event) {
	_super.onUnload(_event);
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"17D5E0B2-391C-41A9-8827-5A71A29DC09F"}
 */
function onCheck_BatchFilter(event) {
        
    elements.btnApplyBatchFilter.enabled = _batchFilter == 1? true: false;
    
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"2A4263FB-9521-44F5-BFD0-3428EC859ECC"}
 */
function onAction_ApplyFilter(event) {

    getData();
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"1D178006-E435-424C-BB83-C467E9CC57BD"}
 */
function onAction_ClearAllFilters(event) {
    
    forms.in_trans_inquiry_tbl_search.onAction_ClearFilter(event);
    
}

/**
 * @properties={typeid:24,uuid:"CD3D19E1-17B3-4C30-AD1E-8FE4B03BB844"}
 */
function getData() {
	// Delete existing records for the current session
	scopes.avDB.deleteRecs("tmp_in_transaction_inquiry", ["session_id"], [globals.av_CurrentUser_SessionUUID]);
	foundset.removeFoundSetFilterParam('TransactionSession')

	/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	var oSQL = { };
	oSQL.table = "_v_in_transaction_inquiry";
	oSQL.server = globals.avBase_dbase_avanti;
	oSQL.sql = "INSERT INTO tmp_in_transaction_inquiry ( \
		            session_id, \
		            empl_id, \
		            org_id, \
		            inquiry_id, \
		            intranstype_id, \
		            transType, \
		            transNo, \
		            transDate, \
		            tranEmpl, \
		            tranStatus, \
		            customer, \
		            supplier, \
		            tranLine, \
		            salesordernum, \
		            salesorderlinenum, \
		            job_number, \
		            item_id, \
		            item_code, \
		            item_desc1, \
		            whse_desc, \
		            location, \
		            transQty, \
		            itemCost, \
		            itemUOM, \
		            totalCost, \
		            project, \
		            itemclass_id, \
		            adjustmentType, \
		            item_status, \
		            ingroup_id,  \
		            ingroup_desc, \
		            itemtype_desc, \
		            itemSize, \
		            item_max_weight, \
		            itemFinish, \
		            itemBrand, \
		            packingSlip, \
		            ledgerAccount, \
		            offsetAccount, \
		            item_color, \
		            offsetAccountDesc, \
		            ledgerAccountDesc, \
		            customer_po, \
		            item_standard_uom_id, \
		            offsetAccountCode, \
		            transRef, \
		            itemclass,  \
		            created_on \
		        ) \
		        SELECT  '" +  globals.av_CurrentUser_SessionUUID + "' , \
		            intraneh_createdby_empl_id,   \
		            org_id, \
		            NEWID(), \
		            NULL, \
		            transType, \
		            transNo, \
		            transDate, \
		            tranEmpl, \
		            tranStatus, \
		            customer, \
		            supplier, \
		            tranLine, \
		            salesordernum, \
		            salesorderlinenum, \
		            job_number, \
		            item_id, \
		            item_code, \
		            item_desc1, \
		            whse_desc,  \
		            location, \
		            transQty, \
		            itemCost, \
		            itemUOM, \
		            totalCost, \
		            project, \
		            itemclass_id, \
		            adjustmentType, \
		            item_status, \
		            ingroup_id,  \
		            ingroup_desc, \
		            itemtype_desc, \
		            itemSize, \
		            item_max_weight, \
		            itemFinish, \
		            itemBrand, \
		            packingSlip, \
		            ledgerAccount, \
		            offsetAccount, \
		            item_color,  \
		            offsetAccountDesc, \
		            ledgerAccountDesc, \
		            customer_po, \
		            item_standard_uom_id, \
		            offsetAccountCode, \
		            transRef, \
		            itemclass,  \
		            CURRENT_TIMESTAMP \
		        FROM _v_in_transaction_inquiry \
		        WHERE org_id = '" + globals.org_id + "' ";

	// Set the parameter for the org_id
	oSQL.args = [globals.org_id.toString()];
	
	// Filters
	
	var sTransTypeList ="";
	var sAdjustmentTypeList = "";
	
	//FILTER-01: Transaction Type
	if (forms.in_trans_inquiry_tbl_search._transactionType)
		//	name	in_trans_inquiry_tbl_search.js
		//	_transactionType) 
		{
		var aTransType = forms.in_trans_inquiry_tbl_search._transactionType.split('\n');
		for (var i = 0; i <= aTransType.length - 1; i++) {
			if (i != 0) {
				sTransTypeList += ',';
			}
			sTransTypeList += "'" + aTransType[i] + "'";
		}
	} 
	else {
		for (var j = 0; j <= _aSelectedTransType.length - 1; j++) {
			if (j != 0) {
				sTransTypeList += ',';
			}
			sTransTypeList += "'" + _aSelectedTransType[j] + "'";
		}		
	}
	
	var bPass = true;
	if (!sTransTypeList) {
		sTransTypeList = '';
		bPass = false;
	}

	if(sTransTypeList != null && bPass){
		oSQL.sql += " AND transType IN (" + sTransTypeList + ") ";
	}
	
	//FILTER-02: Transaction Number	
	if (forms.in_trans_inquiry_tbl_search._transactionNumber) {
        if (forms.in_trans_inquiry_tbl_search._transaction_number_criteria == 'LIKE')
           oSQL.sql += " AND transNo LIKE '%" + forms.in_trans_inquiry_tbl_search._transactionNumber + "%' ";
        else if (forms.in_trans_inquiry_tbl_search._transaction_number_criteria == 'SW')
        	oSQL.sql += " AND transNo LIKE '" + forms.in_trans_inquiry_tbl_search._transactionNumber + "%' ";
        else
        	oSQL.sql += " AND transNo " + forms.in_trans_inquiry_tbl_search._transaction_number_criteria + "'" + forms.in_trans_inquiry_tbl_search._transactionNumber + "' ";
    }	
	
    //FILTER-03: Transaction Date 
    if (forms.in_trans_inquiry_tbl_search._transaction_date) {
        if (forms.in_trans_inquiry_tbl_search._transaction_date_criteria == 'between') {
            oSQL.sql += " AND CAST(transDate AS DATE) BETWEEN '" + plugins.DateUtils.dateFormat( forms.in_trans_inquiry_tbl_search._transaction_date, globals.avBase_dateFormat)  + "' AND '" + plugins.DateUtils.dateFormat( forms.in_trans_inquiry_tbl_search._transaction_to_date, globals.avBase_dateFormat)  + "' ";
        }
        else if (forms.in_trans_inquiry_tbl_search._transaction_date_criteria == '=') {
        	oSQL.sql += " AND CAST(transDate AS DATE) = CAST('" + plugins.DateUtils.dateFormat( forms.in_trans_inquiry_tbl_search._transaction_date, globals.avBase_dateFormat) + "' AS DATE) ";
        }
        else
        	 oSQL.sql += " AND CAST(transDate AS DATE) " + forms.in_trans_inquiry_tbl_search._transaction_date_criteria  +  " '" + plugins.DateUtils.dateFormat( forms.in_trans_inquiry_tbl_search._transaction_date, globals.avBase_dateFormat) + "'  ";
    } 
    
    
    //FILTER-04: Transaction Reference
	if (forms.in_trans_inquiry_tbl_search._tranaction_reference_number) {
		if (forms.in_trans_inquiry_tbl_search._tranaction_reference_number_criteria == 'LIKE')
			oSQL.sql += " AND transRef LIKE '%" + forms.in_trans_inquiry_tbl_search._tranaction_reference_number + "%' ";
		else if (forms.in_trans_inquiry_tbl_search._tranaction_reference_number_criteria == 'SW')
			oSQL.sql += " AND transRef LIKE '" + forms.in_trans_inquiry_tbl_search._tranaction_reference_number + "%' ";
		else
			oSQL.sql += " AND transRef " + forms.in_trans_inquiry_tbl_search._tranaction_reference_number_criteria + "'" + forms.in_trans_inquiry_tbl_search._tranaction_reference_number + "' ";
	}
	
	//FILTER-05: Employee Name
	if (forms.in_trans_inquiry_tbl_search._user_name) {
		oSQL.sql += " AND intraneh_createdby_empl_id = '" + forms.in_trans_inquiry_tbl_search._user_name + "' ";
	}
	
	//FILTER-06: Transaction Status
	if (forms.in_trans_inquiry_tbl_search._status) {
		oSQL.sql += " AND tranStatus = '" + forms.in_trans_inquiry_tbl_search._status + "' ";
	}
	
	//FILTER-07: Customer
	if (forms.in_trans_inquiry_tbl_search._customer) {
		if (forms.in_trans_inquiry_tbl_search._customer_criteria == 'LIKE')
			oSQL.sql += " AND customer LIKE '%" + forms.in_trans_inquiry_tbl_search._customer + "%' ";
		else if (forms.in_trans_inquiry_tbl_search._customer_criteria == 'SW')
			oSQL.sql += " AND customer LIKE '" + forms.in_trans_inquiry_tbl_search._customer + "%' ";
		else
			oSQL.sql += " AND customer " + forms.in_trans_inquiry_tbl_search._customer_criteria + "'" + forms.in_trans_inquiry_tbl_search._customer + "' ";
	}
	
	//FILTER-08: Supplier
	if (forms.in_trans_inquiry_tbl_search._supplier) {
		if (forms.in_trans_inquiry_tbl_search._supplier_criteria == 'LIKE')
			oSQL.sql += " AND supplier LIKE '%" + forms.in_trans_inquiry_tbl_search._supplier + "%' ";
		else if (forms.in_trans_inquiry_tbl_search._supplier_criteria == 'SW')
			oSQL.sql += " AND supplier LIKE '" + forms.in_trans_inquiry_tbl_search._supplier + "%' ";
		else
			oSQL.sql += " AND supplier  " + forms.in_trans_inquiry_tbl_search._supplier_criteria + "'" + forms.in_trans_inquiry_tbl_search._supplier + "' ";
	}
	
	//FILTER-09: Sales Order
	if (forms.in_trans_inquiry_tbl_search._salesorder) {
		if (forms.in_trans_inquiry_tbl_search._salesorder_criteria == 'LIKE')
			oSQL.sql += " AND salesOrderNum LIKE '%" + forms.in_trans_inquiry_tbl_search._salesorder + "%' ";
		else if (forms.in_trans_inquiry_tbl_search._salesorder_criteria == 'SW')
			oSQL.sql += " AND salesOrderNum LIKE '" + forms.in_trans_inquiry_tbl_search._salesorder + "%' ";
		else
			oSQL.sql += " AND salesOrderNum  " + forms.in_trans_inquiry_tbl_search._salesorder_criteria + "'" + forms.in_trans_inquiry_tbl_search._salesorder + "' ";
	}
	
	//FILTER-10: Sales Order Line
	if (forms.in_trans_inquiry_tbl_search._salesorder_line_number) {
		if (forms.in_trans_inquiry_tbl_search._salesorder_line_number_criteria == 'LIKE')
			oSQL.sql += " AND salesOrderLineNum LIKE '%" + forms.in_trans_inquiry_tbl_search._salesorder_line_number + "%' ";
		else if (forms.in_trans_inquiry_tbl_search._salesorder_line_number_criteria == 'SW')
			oSQL.sql += " AND salesOrderLineNum LIKE '" + forms.in_trans_inquiry_tbl_search._salesorder_line_number + "%' ";
		else
			oSQL.sql += " AND salesOrderLineNum  " + forms.in_trans_inquiry_tbl_search._salesorder_line_number_criteria + "'" + forms.in_trans_inquiry_tbl_search._salesorder_line_number + "' ";
	}
	
	//FILTER-11: Sales Order Line
	if (forms.in_trans_inquiry_tbl_search._job_number) {
		if (forms.in_trans_inquiry_tbl_search._job_number_criteria == 'LIKE')
			oSQL.sql += " AND job_number LIKE '%" + forms.in_trans_inquiry_tbl_search._job_number + "%' ";
		else if (forms.in_trans_inquiry_tbl_search._job_number_criteria == 'SW')
			oSQL.sql += " AND job_number LIKE '" + forms.in_trans_inquiry_tbl_search._job_number + "%' ";
		else
			oSQL.sql += " AND job_number  " + forms.in_trans_inquiry_tbl_search._job_number_criteria + "'" + forms.in_trans_inquiry_tbl_search._job_number + "' ";
	}
	
	//FILTER-12: Item Code
	if (forms.in_trans_inquiry_tbl_search._item_code) {
		if (forms.in_trans_inquiry_tbl_search._item_code_criteria == 'LIKE')
			oSQL.sql += " AND item_code LIKE '%" + forms.in_trans_inquiry_tbl_search._item_code + "%' ";
		else if (forms.in_trans_inquiry_tbl_search._item_code_criteria == 'SW')
			oSQL.sql += " AND item_code LIKE '" + forms.in_trans_inquiry_tbl_search._item_code + "%' ";
		else
			oSQL.sql += " AND item_code  " + forms.in_trans_inquiry_tbl_search._item_code_criteria + "'" + forms.in_trans_inquiry_tbl_search._item_code + "' ";
	}
	
	//FILTER-13: Item Description
	if (forms.in_trans_inquiry_tbl_search._item_description) {
		if (forms.in_trans_inquiry_tbl_search._item_description_criteria == 'LIKE')
			oSQL.sql += " AND item_desc1 LIKE '%" + forms.in_trans_inquiry_tbl_search._item_description + "%' ";
		else if (forms.in_trans_inquiry_tbl_search._item_description_criteria == 'SW')
			oSQL.sql += " AND item_desc1 LIKE '" + forms.in_trans_inquiry_tbl_search._item_description + "%' ";
		else
			oSQL.sql += " AND item_desc1  " + forms.in_trans_inquiry_tbl_search._item_description_criteria + "'" + forms.in_trans_inquiry_tbl_search._item_description + "' ";
	}
	
	//FILTER-14: Warehouse
	if (forms.in_trans_inquiry_tbl_search._warehouse) {
			oSQL.sql += " AND whse_desc = '" + application.getValueListDisplayValue('vl_Warehouse', forms.in_trans_inquiry_tbl_search._warehouse) + "' ";
	}
	
	//FILTER-15: Location
	if (forms.in_trans_inquiry_tbl_search._location_code) {
		if (forms.in_trans_inquiry_tbl_search._location_code_criteria == 'LIKE')
			oSQL.sql += " AND location LIKE '%" + forms.in_trans_inquiry_tbl_search._location_code + "%' ";
		else if (forms.in_trans_inquiry_tbl_search._location_code_criteria == 'SW')
			oSQL.sql += " AND location LIKE '" + forms.in_trans_inquiry_tbl_search._location_code + "%' ";
		else
			oSQL.sql += " AND location  " + forms.in_trans_inquiry_tbl_search._location_code_criteria + "'" + forms.in_trans_inquiry_tbl_search._location_code + "' ";
	}

	//FILTER-16: Line #
	if (forms.in_trans_inquiry_tbl_search._line_number) {
		if (forms.in_trans_inquiry_tbl_search._line_number_criteria == 'LIKE')
			oSQL.sql += " AND tranLine LIKE '%" + forms.in_trans_inquiry_tbl_search._line_number + "%' ";
		else if (forms.in_trans_inquiry_tbl_search._line_number_criteria == 'SW')
			oSQL.sql += " AND tranLine LIKE '" + forms.in_trans_inquiry_tbl_search._line_number + "%' ";
		else
			oSQL.sql += " AND tranLine  " + forms.in_trans_inquiry_tbl_search._line_number_criteria + "'" + forms.in_trans_inquiry_tbl_search._line_number + "' ";
	}	
	
	//FILTER-17: Quantity
	if (forms.in_trans_inquiry_tbl_search._quantity) {
		oSQL.sql += " AND transQty  " + forms.in_trans_inquiry_tbl_search._quantity_criteria + forms.in_trans_inquiry_tbl_search._quantity + " ";
	}	
	
	//FILTER-18: Cost/Unit
	if (forms.in_trans_inquiry_tbl_search._cost_unit) {
		oSQL.sql += " AND itemCost  " + forms.in_trans_inquiry_tbl_search._cost_unit_criteria + forms.in_trans_inquiry_tbl_search._cost_unit + " ";
	}
	
	//FILTER-19: UOM
	if (forms.in_trans_inquiry_tbl_search._stocking_unit) {
		if (forms.in_trans_inquiry_tbl_search._stocking_unit_criteria == 'LIKE')
			oSQL.sql += " AND itemUOM LIKE '%" + forms.in_trans_inquiry_tbl_search._stocking_unit + "%' "
		else if (forms.in_trans_inquiry_tbl_search._stocking_unit_criteria == 'SW')
			oSQL.sql += " AND itemUOM LIKE '" + forms.in_trans_inquiry_tbl_search._stocking_unit + "%' "
		else
			oSQL.sql += " AND itemUOM  " + forms.in_trans_inquiry_tbl_search._stocking_unit_criteria + "'" + forms.in_trans_inquiry_tbl_search._stocking_unit + "' "
	}
	
	//FILTER-20: Total Cost
	if (forms.in_trans_inquiry_tbl_search._total_cost) {
		oSQL.sql += " AND totalCost  " + forms.in_trans_inquiry_tbl_search._total_cost_criteria + forms.in_trans_inquiry_tbl_search._total_cost + " " ;
	}
	
	//FILTER-21: Project
	if (forms.in_trans_inquiry_tbl_search._project) {
		oSQL.sql += " AND project = '" + application.getValueListDisplayValue('vl_CustomerProjectsWithEmptyValue', forms.in_trans_inquiry_tbl_search._project) + "' ";
	}
	
	//FILTER-22: Adjustment
	if (forms.in_trans_inquiry_tbl_search._adjustment_type)
		{
		var aAdjustmentType = forms.in_trans_inquiry_tbl_search._adjustment_type.split('\n');
		for (var k = 0; i <= aAdjustmentType.length - 1; k++) {
			if (k != 0) {
				sAdjustmentTypeList += ',';
			}
			sAdjustmentTypeList += "'" + aAdjustmentType[k] + "'";
		}
	} 
	else {
		for (var l = 0; l <= _aSelectedAdjustmentType.length - 1; l++) {
			if (j != 0) {
				sAdjustmentTypeList += ',';
			}
			sAdjustmentTypeList += "'" + _aSelectedAdjustmentType[l] + "'";
		}		
	}
	
	bPass = true;
	if (!sAdjustmentTypeList) {
		sAdjustmentTypeList = '';
		bPass = false;
	}
	
	if(sAdjustmentTypeList != null && bPass){
		oSQL.sql += " AND adjustmentType IN (" + sAdjustmentTypeList + ") ";
	}
	
	//FILTER-22: Item Class
	if (forms.in_trans_inquiry_tbl_search._item_class) {
		oSQL.sql += " AND itemclass = '" + application.getValueListDisplayValue('vl_itemClass_WithEmptyValue', forms.in_trans_inquiry_tbl_search._item_class) + "' ";
	}
	
	//FILTER-23: Item Type
	if (forms.in_trans_inquiry_tbl_search._item_type) {
		oSQL.sql += " AND itemtype_desc = '" + application.getValueListDisplayValue('vl_ItemTypesWithEmptyValue', forms.in_trans_inquiry_tbl_search._item_type) + "' ";
	}
	
	//FILTER-24: Item Status
	if (forms.in_trans_inquiry_tbl_search._item_status) {
		oSQL.sql += " AND item_status = '" + application.getValueListDisplayValue('vl_itemStatusEmptyValue', forms.in_trans_inquiry_tbl_search._item_status) + "' ";
	}
	
	//FILTER-25: Item Group
	if (forms.in_trans_inquiry_tbl_search._item_group) {
		oSQL.sql += " AND ingroup_desc = '" + application.getValueListDisplayValue('vl_ItemGroupAll', forms.in_trans_inquiry_tbl_search._item_group) + "' ";
	}
	
	//FILTER-27: Item Brand
	if (forms.in_trans_inquiry_tbl_search._item_brand) {
		if (forms.in_trans_inquiry_tbl_search._item_brand_criteria == 'LIKE')
			oSQL.sql += " AND itemBrand LIKE '%" + forms.in_trans_inquiry_tbl_search._item_brand + "%' ";
		else if (forms.in_trans_inquiry_tbl_search._item_brand_criteria == 'SW')
			oSQL.sql += " AND itemBrand LIKE '" + forms.in_trans_inquiry_tbl_search._item_brand + "%' ";
		else
			oSQL.sql += " AND itemBrand  " + forms.in_trans_inquiry_tbl_search._item_brand_criteria + "'" + forms.in_trans_inquiry_tbl_search._item_brand + "' ";
	}
	
	//FILTER-28: Item Size
	if (forms.in_trans_inquiry_tbl_search._item_size) {
		if (forms.in_trans_inquiry_tbl_search._item_size_criteria == 'LIKE')
			oSQL.sql += " AND itemSize LIKE '%" + forms.in_trans_inquiry_tbl_search._item_size + "%' ";
		else if (forms.in_trans_inquiry_tbl_search._item_size_criteria == 'SW')
			oSQL.sql += " AND itemSize LIKE '" + forms.in_trans_inquiry_tbl_search._item_size + "%' ";
		else
			oSQL.sql += " AND itemSize  " + forms.in_trans_inquiry_tbl_search._item_size_criteria + "'" + forms.in_trans_inquiry_tbl_search._item_size + "' ";
	}
	
	//FILTER-29: Item Weight
	if (forms.in_trans_inquiry_tbl_search._item_weight) {
			oSQL.sql += " AND item_max_weight  " + forms.in_trans_inquiry_tbl_search._item_weight_criteria + "'" + forms.in_trans_inquiry_tbl_search._item_weight + "' ";
	}
	
	//FILTER-30: Item Finish
	if (forms.in_trans_inquiry_tbl_search._item_finish) {
		if (forms.in_trans_inquiry_tbl_search._item_finish_criteria == 'LIKE')
			oSQL.sql += " AND itemFinish LIKE '%" + forms.in_trans_inquiry_tbl_search._item_finish + "%' ";
		else if (forms.in_trans_inquiry_tbl_search._item_finish_criteria == 'SW')
			oSQL.sql += " AND itemFinish  LIKE '" + forms.in_trans_inquiry_tbl_search._item_finish + "%' ";
		else
			oSQL.sql += " AND itemFinish  " + forms.in_trans_inquiry_tbl_search._item_finish_criteria + "'" + forms.in_trans_inquiry_tbl_search._item_finish + "' ";
	}
	
	//FILTER-31: Item Color
	if (forms.in_trans_inquiry_tbl_search._item_color) {
		if (forms.in_trans_inquiry_tbl_search._item_color_criteria == 'LIKE')
			oSQL.sql += " AND item_color LIKE '%" + forms.in_trans_inquiry_tbl_search._item_color + "%' ";
		else if (forms.in_trans_inquiry_tbl_search._item_color_criteria == 'SW')
			oSQL.sql += " AND item_color  LIKE '" + forms.in_trans_inquiry_tbl_search._item_color + "%' ";
		else
			oSQL.sql += " AND item_color  " + forms.in_trans_inquiry_tbl_search._item_color_criteria + "'" + forms.in_trans_inquiry_tbl_search._item_color + "' ";
	}
	
	//FILTER-32: Packing Slip
	if (forms.in_trans_inquiry_tbl_search._packing_slip) {
		if (forms.in_trans_inquiry_tbl_search._packing_slip_criteria == 'LIKE')
			oSQL.sql += " AND packingSlip LIKE '%" + forms.in_trans_inquiry_tbl_search._packing_slip + "%' ";
		else if (forms.in_trans_inquiry_tbl_search._packing_slip_criteria == 'SW')
			oSQL.sql += " AND packingSlip  LIKE '" + forms.in_trans_inquiry_tbl_search._packing_slip + "%' ";
		else
			oSQL.sql += " AND packingSlip  " + forms.in_trans_inquiry_tbl_search._packing_slip_criteria + "'" + forms.in_trans_inquiry_tbl_search._packing_slip + "' ";
	}
	
	//FILTER-33: Ledger Account
	if (forms.in_trans_inquiry_tbl_search._ledger_account) {
		if (forms.in_trans_inquiry_tbl_search._ledger_account_criteria == 'LIKE')
			oSQL.sql += " AND ledgerAccount LIKE '%" + forms.in_trans_inquiry_tbl_search._ledger_account + "%' ";
		else if (forms.in_trans_inquiry_tbl_search._ledger_account_criteria == 'SW')
			oSQL.sql += " AND ledgerAccount  LIKE '" + forms.in_trans_inquiry_tbl_search._ledger_account + "%' ";
		else
			oSQL.sql += " AND ledgerAccount " + forms.in_trans_inquiry_tbl_search._ledger_account_criteria + "'" + forms.in_trans_inquiry_tbl_search._ledger_account + "' ";
	}
	
	//FILTER-34: Offset Account
	if (forms.in_trans_inquiry_tbl_search._offset_account) {
		if (forms.in_trans_inquiry_tbl_search._offset_account_criteria == 'LIKE')
			oSQL.sql += " AND offsetAccountCode LIKE '%" + forms.in_trans_inquiry_tbl_search._offset_account + "%' ";
		else if (forms.in_trans_inquiry_tbl_search._offset_account_criteria == 'SW')
			oSQL.sql += " AND offsetAccountCode  LIKE '" + forms.in_trans_inquiry_tbl_search._offset_account + "%' ";
		else
			oSQL.sql += " AND offsetAccountCode " + forms.in_trans_inquiry_tbl_search._offset_account_criteria + "'" + forms.in_trans_inquiry_tbl_search._offset_account + "' ";
	}
	
	application.output(oSQL.sql)
	
	//FILTER-35: Customer PO
	if (forms.in_trans_inquiry_tbl_search._customer_po) {
		if (forms.in_trans_inquiry_tbl_search._customer_po_criteria == 'LIKE')
			oSQL.sql += " AND customer_po LIKE '%" + forms.in_trans_inquiry_tbl_search._customer_po + "%' ";
		else if (forms.in_trans_inquiry_tbl_search._customer_po_criteria == 'SW')
			oSQL.sql += " AND customer_po  LIKE '" + forms.in_trans_inquiry_tbl_search._customer_po + "%' ";
		else
			oSQL.sql += " AND customer_po " + forms.in_trans_inquiry_tbl_search._customer_po_criteria + "'" + forms.in_trans_inquiry_tbl_search._customer_po + "' ";
	}
	
	// Execute the SQL statement
	
	var done = plugins.rawSQL.executeSQL(oSQL.server,oSQL.sql)
if (!done)

{
	var msg = plugins.rawSQL.getException().getMessage(); //see exception node for more info about the exception obj
	plugins.dialogs.showErrorDialog('Error',  'SQL exception: '+msg,  'Ok')
}


	/** @type {JSFoundset<db:/avanti/tmp_in_transaction_inquiry>} */
	databaseManager.getFoundSet(globals.avBase_dbase_avanti, "tmp_in_transaction_inquiry");
	
	
	foundset.addFoundSetFilterParam('session_id', '=' , globals.av_CurrentUser_SessionUUID, 'TransactionSession');
	foundset.loadAllRecords()
	
	foundset.sort('transDate desc, transno desc, item_code desc');
	elements.grid.myFoundset.foundset.loadRecords(foundset);
}
