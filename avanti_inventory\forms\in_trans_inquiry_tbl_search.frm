customProperties:"useCssPosition:true",
dataSource:"db:/avanti/tmp_in_transaction_inquiry",
extendsID:"FB3CD7ED-175A-4485-A1D2-11C85D23AB0B",
items:[
{
cssPosition:"47,-1,-1,3928,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"3928",
right:"-1",
top:"47",
width:"140"
},
dataProviderID:"_item_weight",
editable:true,
enabled:true,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"item_weight",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"021D2F42-4180-41CE-AACB-************"
},
{
cssPosition:"8,-1,-1,3369,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"3369",
right:"-1",
top:"8",
width:"140"
},
enabled:true,
labelFor:"item_status",
styleClass:"label_bts",
tabSeq:-1,
text:"Item Status ",
toolTipText:"Item Status ",
visible:true
},
name:"item_status_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"03A481EB-21C2-4D62-99F4-054F46AA126D"
},
{
cssPosition:"49,-1,-1,1550,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"1550",
right:"-1",
top:"49",
width:"140"
},
dataProviderID:"_job_number",
editable:true,
enabled:true,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"job_number",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"049F6DEE-9054-443F-A453-E9DB1FBA3E24"
},
{
cssPosition:"48,-1,-1,2248,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"2248",
right:"-1",
top:"48",
width:"140"
},
dataProviderID:"_line_number",
editable:true,
enabled:true,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"line_number",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"07A204EE-A0C3-4B57-BC88-B234933718AC"
},
{
cssPosition:"9,-1,-1,4746,20,20",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"4746",
right:"-1",
top:"9",
width:"20"
},
enabled:true,
formIndex:9,
onActionMethodID:"21564C45-8432-499D-8BCD-42BFD1E001AD",
styleClass:"label_bts text-right",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.tooltip.clearFilter",
visible:true
},
name:"btnClearOffset_account",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"0AC72765-C11E-47A1-AC4E-988026970FA5"
},
{
cssPosition:"28,-1,-1,2248,140,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"2248",
right:"-1",
top:"28",
width:"140"
},
dataProviderID:"_line_number_criteria",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"********-2A28-41DC-AE58-EA8F55F3DE46",
visible:true
},
name:"line_number_criteria",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"0AE4A6E4-469F-47CB-9534-7257265F4FED"
},
{
cssPosition:"8,-1,-1,430,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"430",
right:"-1",
top:"8",
width:"140"
},
enabled:true,
labelFor:"transaction_date",
styleClass:"label_bts",
tabSeq:-1,
text:"Transaction Date",
toolTipText:"Transaction Date",
visible:true
},
name:"transaction_date_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"0BDC2ACE-DC1F-4DC1-A996-764BCC51A672"
},
{
cssPosition:"9,-1,-1,4766,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"4766",
right:"-1",
top:"9",
width:"140"
},
enabled:true,
labelFor:"customer_po",
styleClass:"label_bts",
tabSeq:-1,
text:"Customer PO",
toolTipText:"Customer PO",
visible:true
},
name:"customer_po_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"0C49BA21-EB92-47FB-8A6C-EE4A8F52118D"
},
{
cssPosition:"49,-1,-1,1270,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"1270",
right:"-1",
top:"49",
width:"140"
},
dataProviderID:"_salesorder",
editable:true,
enabled:true,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"salesorder",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"0DF37ECA-5E5C-448D-8489-CC4C11D49017"
},
{
cssPosition:"9,-1,-1,4346,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"4346",
right:"-1",
top:"9",
width:"140"
},
enabled:true,
labelFor:"packing_slip",
styleClass:"label_bts",
tabSeq:-1,
text:"Packing Slip",
toolTipText:"Packing Slip",
visible:true
},
name:"packing_slip_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"0F253017-06EB-4EF4-AA60-01EF474733B6"
},
{
cssPosition:"10,-1,-1,2785,20,20",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"2785",
right:"-1",
top:"10",
width:"20"
},
enabled:true,
formIndex:9,
onActionMethodID:"21564C45-8432-499D-8BCD-42BFD1E001AD",
styleClass:"label_bts text-right",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.tooltip.clearFilter",
visible:true
},
name:"btnClearStocking_unit",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"117323B0-D069-4411-84A5-74A52C3199B3"
},
{
cssPosition:"49,-1,-1,2666,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"2666",
right:"-1",
top:"49",
width:"140"
},
dataProviderID:"_stocking_unit",
editable:true,
enabled:true,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"stocking_unit",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"1327AD04-FB16-47C3-8ED5-04D90F12459E"
},
{
cssPosition:"10,-1,-1,1250,20,20",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"1250",
right:"-1",
top:"10",
width:"20"
},
enabled:true,
formIndex:9,
onActionMethodID:"21564C45-8432-499D-8BCD-42BFD1E001AD",
styleClass:"label_bts text-right",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.tooltip.clearFilter",
visible:true
},
name:"btnClearSupplier",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"14FE8C2C-26A9-45F5-B3BE-D70636FDBF46"
},
{
cssPosition:"9,-1,-1,2368,20,20",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"2368",
right:"-1",
top:"9",
width:"20"
},
enabled:true,
formIndex:9,
onActionMethodID:"21564C45-8432-499D-8BCD-42BFD1E001AD",
styleClass:"label_bts text-right",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.tooltip.clearFilter",
visible:true
},
name:"btnClearLine_number",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"15A21721-8308-4EEA-A7F3-1E56728EF249"
},
{
cssPosition:"47,-1,-1,4068,138,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"4068",
right:"-1",
top:"47",
width:"138"
},
dataProviderID:"_item_finish",
editable:true,
enabled:true,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"item_finish",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"19CA80C3-25FC-4EB1-ADEC-4B3A257D6055"
},
{
cssPosition:"29,-1,-1,1410,140,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"1410",
right:"-1",
top:"29",
width:"140"
},
dataProviderID:"_salesorder_line_number_criteria",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"********-2A28-41DC-AE58-EA8F55F3DE46",
visible:true
},
name:"salesorder_line_number_criteria",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"1BF047E7-0824-4BD0-8090-7D0AD72DC606"
},
{
cssPosition:"29,-1,-1,710,140,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"710",
right:"-1",
top:"29",
width:"140"
},
dataProviderID:"_user_name",
enabled:true,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"84D235B0-B99A-4521-9A53-E1ABA0E287D3",
visible:true
},
name:"user_name",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"1CCBF411-D933-4B2D-8A55-990F885C9CE9"
},
{
cssPosition:"28,-1,-1,4068,140,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"4068",
right:"-1",
top:"28",
width:"140"
},
dataProviderID:"_item_finish_criteria",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"95A50B98-5DF2-4D1D-A20D-75D920B0C5F7",
visible:true
},
name:"item_finish_criteria",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"2002918B-0CD4-464F-932B-764ED2824E42"
},
{
cssPosition:"28,-1,-1,4626,140,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"4626",
right:"-1",
top:"28",
width:"140"
},
dataProviderID:"_offset_account_criteria",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"95A50B98-5DF2-4D1D-A20D-75D920B0C5F7",
visible:true
},
name:"offset_account_criteria",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"225550B4-EB1D-489D-825B-430934CBBD0E"
},
{
cssPosition:"10,-1,-1,1130,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"1130",
right:"-1",
top:"10",
width:"140"
},
enabled:true,
labelFor:"supplier",
styleClass:"label_bts",
tabSeq:-1,
text:"Supplier",
toolTipText:"Supplier",
visible:true
},
name:"supplier_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"22C04982-D513-4FE2-B3E2-06463C7FC329"
},
{
cssPosition:"9,-1,-1,1270,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"1270",
right:"-1",
top:"9",
width:"140"
},
enabled:true,
labelFor:"salesorder",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.salesOrder",
toolTipText:"Customer",
visible:true
},
name:"salesorder_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"281B3E65-0282-4942-A73B-D24F3EC408B3"
},
{
cssPosition:"9,-1,-1,1690,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"1690",
right:"-1",
top:"9",
width:"140"
},
enabled:true,
labelFor:"item_code",
styleClass:"label_bts",
tabSeq:-1,
text:"Item Code",
toolTipText:"Item Code",
visible:true
},
name:"item_code_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"2A617EA5-CF3B-435A-9BEE-3154022DD1E9"
},
{
cssPosition:"47,-1,-1,4486,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"4486",
right:"-1",
top:"47",
width:"140"
},
dataProviderID:"_ledger_account",
editable:true,
enabled:true,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"ledger_account",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"2D201329-7F62-474B-AB3E-1983E255B74E"
},
{
cssPosition:"49,-1,-1,1410,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"1410",
right:"-1",
top:"49",
width:"140"
},
dataProviderID:"_salesorder_line_number",
editable:true,
enabled:true,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"salesorder_line_number",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"2DB81678-4D47-44AD-93A1-873930348FC0"
},
{
cssPosition:"8,-1,-1,3928,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"3928",
right:"-1",
top:"8",
width:"140"
},
enabled:true,
labelFor:"item_weight",
styleClass:"label_bts",
tabSeq:-1,
text:"Item Weight",
toolTipText:"Item Weight",
visible:true
},
name:"item_weight_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"2F0E4EFF-40D2-4513-AF6C-621ECBFC385D"
},
{
cssPosition:"28,-1,-1,3508,140,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"3508",
right:"-1",
top:"28",
width:"140"
},
dataProviderID:"_item_group",
enabled:true,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"2C06D49C-89B8-4ADD-95E6-91297C627FB4",
visible:true
},
name:"item_group",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"314A37AC-08E8-4D3F-8F9C-E90130461BBD"
},
{
cssPosition:"28,-1,-1,3788,140,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"3788",
right:"-1",
top:"28",
width:"140"
},
dataProviderID:"_item_size_criteria",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"95A50B98-5DF2-4D1D-A20D-75D920B0C5F7",
visible:true
},
name:"item_size_criteria",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"3325102F-9DBA-406F-90AC-2367D3778E0A"
},
{
cssPosition:"28,-1,-1,3648,140,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"3648",
right:"-1",
top:"28",
width:"140"
},
dataProviderID:"_item_brand_criteria",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"95A50B98-5DF2-4D1D-A20D-75D920B0C5F7",
visible:true
},
name:"item_brand_criteria",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"359F0D43-8521-486C-B901-60192B73412A"
},
{
cssPosition:"47,-1,-1,4766,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"4766",
right:"-1",
top:"47",
width:"140"
},
dataProviderID:"_customer_po",
editable:true,
enabled:true,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"customer_po",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"3611135B-396E-4714-9B77-131149AB0542"
},
{
cssPosition:"10,-1,-1,2666,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"2666",
right:"-1",
top:"10",
width:"140"
},
enabled:true,
labelFor:"stocking_unit",
styleClass:"label_bts",
tabSeq:-1,
text:"UOM",
toolTipText:"UOM",
visible:true
},
name:"stocking_unit_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"3848179F-17F5-477E-8F0E-7C23EC4CB6B6"
},
{
cssPosition:"8,-1,-1,4049,20,20",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"4049",
right:"-1",
top:"8",
width:"20"
},
enabled:true,
formIndex:9,
onActionMethodID:"21564C45-8432-499D-8BCD-42BFD1E001AD",
styleClass:"label_bts text-right",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.tooltip.clearFilter",
visible:true
},
name:"btnClearItem_weight",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"3A70D902-BFC2-40A6-87C8-9ADC0B7D76C7"
},
{
cssPosition:"9,-1,-1,4206,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"4206",
right:"-1",
top:"9",
width:"140"
},
enabled:true,
labelFor:"item_color",
styleClass:"label_bts",
tabSeq:-1,
text:"Item Color",
toolTipText:"Item Color",
visible:true
},
name:"item_color_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"3CDC2B2B-B689-4F45-B773-62295D3526F1"
},
{
cssPosition:"9,-1,-1,4467,20,20",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"4467",
right:"-1",
top:"9",
width:"20"
},
enabled:true,
formIndex:9,
onActionMethodID:"21564C45-8432-499D-8BCD-42BFD1E001AD",
styleClass:"label_bts text-right",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.tooltip.clearFilter",
visible:true
},
name:"btnClearPacking_slip",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"3D6E03C8-4F05-4E03-9D67-89551DDF5082"
},
{
cssPosition:"49,-1,-1,2387,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"2387",
right:"-1",
top:"49",
width:"140"
},
dataProviderID:"_quantity",
editable:true,
enabled:true,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"quantity",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"42F4CE1E-5048-4036-BEAB-E45901678480"
},
{
cssPosition:"9,-1,-1,3088,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"3088",
right:"-1",
top:"9",
width:"140"
},
enabled:true,
labelFor:"item_class",
styleClass:"label_bts",
tabSeq:-1,
text:"Item Class ",
toolTipText:"Item Class ",
visible:true
},
name:"item_class_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"435620CA-B0AF-405B-8561-6C2478B33FC3"
},
{
cssPosition:"9,-1,-1,1830,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"1830",
right:"-1",
top:"9",
width:"140"
},
enabled:true,
labelFor:"item_description",
styleClass:"label_bts",
tabSeq:-1,
text:"Item Description",
toolTipText:"Item Description",
visible:true
},
name:"item_decription_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"4478368F-C025-4C0B-A122-75230A56C902"
},
{
cssPosition:"8,-1,-1,3788,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"3788",
right:"-1",
top:"8",
width:"140"
},
enabled:true,
labelFor:"item_size",
styleClass:"label_bts",
tabSeq:-1,
text:"Item Size",
toolTipText:"Item Size",
visible:true
},
name:"item_size_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"44814D78-62D0-4554-9B31-4A3BB0BC210E"
},
{
cssPosition:"29,-1,-1,850,140,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"850",
right:"-1",
top:"29",
width:"140"
},
dataProviderID:"_status",
enabled:true,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
toolTipText:"Status",
valuelistID:"0187CD4F-0770-4EB7-B70D-D110CD9E2AB7",
visible:true
},
name:"status",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"47C8C3AE-C12C-40D7-88C8-D59656F8F57D"
},
{
cssPosition:"8,-1,-1,3627,20,20",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"3627",
right:"-1",
top:"8",
width:"20"
},
enabled:true,
formIndex:9,
onActionMethodID:"21564C45-8432-499D-8BCD-42BFD1E001AD",
styleClass:"label_bts text-right",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.tooltip.clearFilter",
visible:true
},
name:"btnClearItem_group",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"47E9B048-BC2F-41C4-9816-7E73AE8C7C41"
},
{
cssPosition:"8,-1,-1,4068,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"4068",
right:"-1",
top:"8",
width:"140"
},
enabled:true,
labelFor:"item_finish",
styleClass:"label_bts",
tabSeq:-1,
text:"Item Finish",
toolTipText:"Item Finish",
visible:true
},
name:"item_weight_labelc",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"4E469403-306A-4502-8CEA-9E3FF9C375A3"
},
{
cssPosition:"47,-1,-1,3788,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"3788",
right:"-1",
top:"47",
width:"140"
},
dataProviderID:"_item_size",
editable:true,
enabled:true,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"item_size",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"4E6399F3-C551-434E-9975-E700508F1713"
},
{
cssPosition:"28,-1,-1,3228,140,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"3228",
right:"-1",
top:"28",
width:"140"
},
dataProviderID:"_item_type",
enabled:true,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"3A6B56B7-7878-4AC0-8694-1BDA832EB65D",
visible:true
},
name:"item_type",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"4FC57F4B-C25E-43D3-822B-9BC668DA735F"
},
{
cssPosition:"8,-1,-1,3508,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"3508",
right:"-1",
top:"8",
width:"140"
},
enabled:true,
labelFor:"item_group",
styleClass:"label_bts",
tabSeq:-1,
text:"Item Group ",
toolTipText:"Item Group ",
visible:true
},
name:"item_group_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"4FD030B4-0EEC-4787-96C2-BCC34F1BFD28"
},
{
cssPosition:"30,-1,-1,13,274,65",
json:{
cssPosition:{
bottom:"-1",
height:"65",
left:"13",
right:"-1",
top:"30",
width:"274"
},
dataProviderID:"_transactionType",
onActionMethodID:null,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
styleClass:"checkbox_column",
valuelistID:"0104A8AB-DA09-438A-A0D0-B6003FE708E1"
},
name:"transactionType",
styleClass:"checkbox_column",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"50C84ADC-35A2-4A64-A754-19A15C9A3536"
},
{
cssPosition:"9,-1,-1,551,20,20",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"551",
right:"-1",
top:"9",
width:"20"
},
enabled:true,
formIndex:9,
onActionMethodID:"21564C45-8432-499D-8BCD-42BFD1E001AD",
styleClass:"label_bts text-right",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.tooltip.clearFilter",
visible:true
},
name:"btnClearTransaction_date",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"511E7E0C-7EA3-4E07-80EE-6DB5943E59BE"
},
{
cssPosition:"29,-1,-1,990,140,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"990",
right:"-1",
top:"29",
width:"140"
},
dataProviderID:"_customer_criteria",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"95A50B98-5DF2-4D1D-A20D-75D920B0C5F7",
visible:true
},
name:"customer_criteria",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"53281301-C91D-49BD-AB1F-4EB5A735D367"
},
{
cssPosition:"9,-1,-1,2108,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"2108",
right:"-1",
top:"9",
width:"140"
},
enabled:true,
labelFor:"location_code",
styleClass:"label_bts",
tabSeq:-1,
text:"Location",
toolTipText:"Location",
visible:true
},
name:"location_code_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"54399B7B-05E9-4467-A2F3-28CC741E13DE"
},
{
cssPosition:"7,-1,-1,268,20,20",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"268",
right:"-1",
top:"7",
width:"20"
},
enabled:true,
formIndex:9,
onActionMethodID:"21564C45-8432-499D-8BCD-42BFD1E001AD",
styleClass:"label_bts text-right",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.tooltip.clearFilter",
visible:true
},
name:"btnClearType",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"582C3ED2-B852-410B-BD3B-B4EC7096B152"
},
{
cssPosition:"47,-1,-1,3648,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"3648",
right:"-1",
top:"47",
width:"140"
},
dataProviderID:"_item_brand",
editable:true,
enabled:true,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"item_brand",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"585B8A63-FB76-4AF5-86B7-83159DE25CF8"
},
{
cssPosition:"10,-1,-1,970,20,20",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"970",
right:"-1",
top:"10",
width:"20"
},
enabled:true,
formIndex:9,
onActionMethodID:"21564C45-8432-499D-8BCD-42BFD1E001AD",
styleClass:"label_bts text-right",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.tooltip.clearFilter",
visible:true
},
name:"btnClearStatus",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"5A9BFFE9-E5DB-4C9D-A5F2-0A8803EAA340"
},
{
cssPosition:"47,-1,-1,4346,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"4346",
right:"-1",
top:"47",
width:"140"
},
dataProviderID:"_packing_slip",
editable:true,
enabled:true,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"packing_slip",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"5C7FBF83-E072-4399-9C25-5589222D324C"
},
{
cssPosition:"8,-1,-1,831,20,20",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"831",
right:"-1",
top:"8",
width:"20"
},
enabled:true,
formIndex:9,
onActionMethodID:"21564C45-8432-499D-8BCD-42BFD1E001AD",
styleClass:"label_bts text-right",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.tooltip.clearFilter",
visible:true
},
name:"btnClearUser_name",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"5E132AFD-3ADA-4843-A3D8-2CCBFC3BAF66"
},
{
cssPosition:"9,-1,-1,1950,20,20",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"1950",
right:"-1",
top:"9",
width:"20"
},
enabled:true,
formIndex:9,
onActionMethodID:"21564C45-8432-499D-8BCD-42BFD1E001AD",
styleClass:"label_bts text-right",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.tooltip.clearFilter",
visible:true
},
name:"btnClearItem_description",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"63FC3033-FA37-4CB2-A942-59249901E711"
},
{
cssPosition:"10,-1,-1,2507,20,20",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"2507",
right:"-1",
top:"10",
width:"20"
},
enabled:true,
formIndex:9,
onActionMethodID:"21564C45-8432-499D-8BCD-42BFD1E001AD",
styleClass:"label_bts text-right",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.tooltip.clearFilter",
visible:true
},
name:"btnClearQuantity",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"65C837DF-2321-425B-B118-5AAD782DD0CE"
},
{
cssPosition:"29,-1,-1,1130,140,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"1130",
right:"-1",
top:"29",
width:"140"
},
dataProviderID:"_supplier_criteria",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"95A50B98-5DF2-4D1D-A20D-75D920B0C5F7",
visible:true
},
name:"supplier_criteria",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"674538F4-32D8-4A89-A589-711EB66F4C40"
},
{
cssPosition:"10,-1,-1,1550,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"1550",
right:"-1",
top:"10",
width:"140"
},
enabled:true,
labelFor:"line_number",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.jobNumber",
toolTipText:"i18n:avanti.lbl.jobNumber",
visible:true
},
name:"job_number_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"6B977A81-AB7E-4D1E-B66C-C5F7B2926A96"
},
{
cssPosition:"28,-1,-1,4766,140,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"4766",
right:"-1",
top:"28",
width:"140"
},
dataProviderID:"_customer_po_criteria",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"95A50B98-5DF2-4D1D-A20D-75D920B0C5F7",
visible:true
},
name:"customer_po_criteria",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"6D5ED227-7714-40B8-807F-0F6023B6F487"
},
{
height:137,
partType:5,
typeid:19,
uuid:"6DFC0C47-4932-4E96-BCFC-70E55CDC7B70"
},
{
cssPosition:"9,-1,-1,990,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"990",
right:"-1",
top:"9",
width:"140"
},
enabled:true,
labelFor:"customer",
styleClass:"label_bts",
tabSeq:-1,
text:"Customer",
toolTipText:"Customer",
visible:true
},
name:"customer_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"6E0BFD5B-D5C0-4DD4-847D-E40BDDB52905"
},
{
cssPosition:"47,-1,-1,4626,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"4626",
right:"-1",
top:"47",
width:"140"
},
dataProviderID:"_offset_account",
editable:true,
enabled:true,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"offset_account",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"762DA175-77FA-4199-A99D-BA4A0CA4E8F1"
},
{
cssPosition:"28,-1,-1,3368,140,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"3368",
right:"-1",
top:"28",
width:"140"
},
dataProviderID:"_item_status",
enabled:true,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"DF73EFEC-4C5D-4779-A5AB-D6E8DF93F803",
visible:true
},
name:"item_status",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"7789F7DE-0A55-46FA-8507-6ED016FF6407"
},
{
cssPosition:"11,-1,-1,1670,20,20",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"1670",
right:"-1",
top:"11",
width:"20"
},
enabled:true,
formIndex:9,
onActionMethodID:"21564C45-8432-499D-8BCD-42BFD1E001AD",
styleClass:"label_bts text-right",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.tooltip.clearFilter",
visible:true
},
name:"btnClearJob_number",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"7A908F7B-FA36-4219-A985-FC1FB4305FAF"
},
{
cssPosition:"48,-1,-1,1830,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"1830",
right:"-1",
top:"48",
width:"140"
},
dataProviderID:"_item_description",
editable:true,
enabled:true,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"item_description",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"7C24037A-A0C5-409B-9341-971153BAE2C6"
},
{
cssPosition:"7,-1,-1,13,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"13",
right:"-1",
top:"7",
width:"140"
},
enabled:true,
labelFor:"transactionType",
styleClass:"label_bts",
tabSeq:-1,
text:"Transaction Type",
toolTipText:"Transaction Type",
visible:true
},
name:"transaction_type_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"7D7DD5AB-1DE8-4A3D-B3E2-18BE6BC1B321"
},
{
cssPosition:"9,-1,-1,2088,20,20",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"2088",
right:"-1",
top:"9",
width:"20"
},
enabled:true,
formIndex:9,
onActionMethodID:"21564C45-8432-499D-8BCD-42BFD1E001AD",
styleClass:"label_bts text-right",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.tooltip.clearFilter",
visible:true
},
name:"btnClearWarehouse",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"7FF85AA5-2D2A-4C19-A5FA-7E95D5DE75AF"
},
{
cssPosition:"48,-1,-1,2109,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"2109",
right:"-1",
top:"48",
width:"140"
},
dataProviderID:"_location_code",
editable:true,
enabled:true,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"location_code",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"82740BA5-3219-4450-A1D0-95B3269503E1"
},
{
cssPosition:"28,-1,-1,2109,140,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"2109",
right:"-1",
top:"28",
width:"140"
},
dataProviderID:"_location_code_criteria",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"95A50B98-5DF2-4D1D-A20D-75D920B0C5F7",
visible:true
},
name:"location_code_criteria",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"8597C5A2-0A5A-4C02-A628-CFB6EDD734F1"
},
{
cssPosition:"10,-1,-1,2945,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"2945",
right:"-1",
top:"10",
width:"140"
},
enabled:true,
labelFor:"project",
styleClass:"label_bts",
tabSeq:-1,
text:"Project ",
toolTipText:"Project ",
visible:true
},
name:"project_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"86FE5F4B-D9AC-46BE-894F-0E87ED30F566"
},
{
cssPosition:"28,-1,-1,4206,140,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"4206",
right:"-1",
top:"28",
width:"140"
},
dataProviderID:"_item_color_criteria",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"95A50B98-5DF2-4D1D-A20D-75D920B0C5F7",
visible:true
},
name:"item_color_criteria",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"8955EF9B-1710-4527-9610-95407A73111D"
},
{
cssPosition:"49,-1,-1,2526,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"2526",
right:"-1",
top:"49",
width:"140"
},
dataProviderID:"_cost_unit",
editable:true,
enabled:true,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"cost_unit",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"899B100A-6C40-4D56-A85C-43635FA05F6C"
},
{
cssPosition:"49,-1,-1,570,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"570",
right:"-1",
top:"49",
width:"140"
},
dataProviderID:"_tranaction_reference_number",
editable:true,
enabled:true,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"transaction_reference_number",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"8C16D390-A951-4F9A-823F-7A439AEF4B20"
},
{
cssPosition:"9,-1,-1,4327,20,20",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"4327",
right:"-1",
top:"9",
width:"20"
},
enabled:true,
formIndex:9,
onActionMethodID:"21564C45-8432-499D-8BCD-42BFD1E001AD",
styleClass:"label_bts text-right",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.tooltip.clearFilter",
visible:true
},
name:"btnClearItem_color",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"8CFEB057-0211-4E54-A5B1-645CAC617972"
},
{
cssPosition:"49,-1,-1,2805,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"2805",
right:"-1",
top:"49",
width:"140"
},
dataProviderID:"_total_cost",
editable:true,
enabled:true,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"total_cost",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"8DF50D49-77A0-443C-9FAC-5A6F83119024"
},
{
cssPosition:"28,-1,-1,3088,140,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"3088",
right:"-1",
top:"28",
width:"140"
},
dataProviderID:"_item_class",
enabled:true,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"8C8F5D68-CD98-4132-9C1E-3A6F4030F713",
visible:true
},
name:"item_class",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"8F34BD91-A8BA-4AD9-91E7-168C793F761D"
},
{
cssPosition:"8,-1,-1,3648,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"3648",
right:"-1",
top:"8",
width:"140"
},
enabled:true,
labelFor:"item_brand",
styleClass:"label_bts",
tabSeq:-1,
text:"Item Brand",
toolTipText:"Item Brand",
visible:true
},
name:"item_brand_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"913223D8-0423-4428-AE08-7A67962F28F5"
},
{
cssPosition:"9,-1,-1,570,123,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"570",
right:"-1",
top:"9",
width:"123"
},
enabled:true,
labelFor:"transaction_reference_number",
styleClass:"label_bts",
tabSeq:-1,
text:"Transaction Reference Number",
toolTipText:"Transaction Reference Number",
visible:true
},
name:"transaction_reference_number_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"9338B449-8D68-45ED-9F22-77D00D2EA8EE"
},
{
cssPosition:"9,-1,-1,4186,20,20",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"4186",
right:"-1",
top:"9",
width:"20"
},
enabled:true,
formIndex:9,
onActionMethodID:"21564C45-8432-499D-8BCD-42BFD1E001AD",
styleClass:"label_bts text-right",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.tooltip.clearFilter",
visible:true
},
name:"btnClearItem_finish",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"953775B1-7C25-4363-9362-33F316BF5CEE"
},
{
cssPosition:"10,-1,-1,2805,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"2805",
right:"-1",
top:"10",
width:"140"
},
enabled:true,
labelFor:"total_cost",
styleClass:"label_bts",
tabSeq:-1,
text:"Total cost",
toolTipText:"Total cost",
visible:true
},
name:"total_cost_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"9D859098-0B80-4EBA-9B21-83A26B2484A8"
},
{
cssPosition:"9,-1,-1,4626,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"4626",
right:"-1",
top:"9",
width:"140"
},
enabled:true,
labelFor:"offset_account",
styleClass:"label_bts",
tabSeq:-1,
text:"Offset Account",
toolTipText:"Offset Account ",
visible:true
},
name:"offset_account_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"9E065F7C-4BEE-45D3-9F85-A509D5BE254A"
},
{
cssPosition:"29,-1,-1,1550,140,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"1550",
right:"-1",
top:"29",
width:"140"
},
dataProviderID:"_job_number_criteria",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"95A50B98-5DF2-4D1D-A20D-75D920B0C5F7",
visible:true
},
name:"job_number_criteria",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"A1A40E53-3509-48F1-9E8B-01F7002764F8"
},
{
cssPosition:"8,-1,-1,290,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"290",
right:"-1",
top:"8",
width:"140"
},
enabled:true,
labelFor:"transactionNumber",
styleClass:"label_bts",
tabSeq:-1,
text:"Transaction Number",
toolTipText:"Transaction Number",
visible:true
},
name:"transaction_number_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"A3DD4AEA-E028-4FC9-9209-9EA62788B79A"
},
{
cssPosition:"70,-1,-1,430,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"430",
right:"-1",
top:"70",
width:"140"
},
dataProviderID:"_transaction_to_date",
enabled:true,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:0,
visible:false
},
name:"transaction_to_date",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"A4729D3E-B547-4414-A62B-0BA2C7E32F02",
visible:false
},
{
cssPosition:"29,-1,-1,430,140,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"430",
right:"-1",
top:"29",
width:"140"
},
dataProviderID:"_transaction_date_criteria",
enabled:true,
onDataChangeMethodID:"7B9B8BA3-E65D-4D3E-AE0A-2610B0B6DC71",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"EE6ED4CD-0F5C-49A0-8569-11F8807197C5",
visible:true
},
name:"tranaction_date_criteriac",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"A64B8B4E-33C4-46ED-BEDD-4135A57C5450"
},
{
cssPosition:"48,-1,-1,1690,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"1690",
right:"-1",
top:"48",
width:"140"
},
dataProviderID:"_item_code",
editable:true,
enabled:true,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"item_code",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"A7754C62-13C0-45C5-83D2-1776D987B517"
},
{
cssPosition:"8,-1,-1,1390,20,20",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"1390",
right:"-1",
top:"8",
width:"20"
},
enabled:true,
formIndex:9,
onActionMethodID:"21564C45-8432-499D-8BCD-42BFD1E001AD",
styleClass:"label_bts text-right",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.tooltip.clearFilter",
visible:true
},
name:"btnClearSalesOrder",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"AB648CC2-FC77-4C00-B4E5-0CC536EB9A93"
},
{
cssPosition:"29,-1,-1,570,140,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"570",
right:"-1",
top:"29",
width:"140"
},
dataProviderID:"_tranaction_reference_number_criteria",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"95A50B98-5DF2-4D1D-A20D-75D920B0C5F7",
visible:true
},
name:"tranaction_reference_number_criteria",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"ACE46F96-05DF-4A47-B98E-E6A67058B625"
},
{
cssPosition:"8,-1,-1,3909,20,20",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"3909",
right:"-1",
top:"8",
width:"20"
},
enabled:true,
formIndex:9,
onActionMethodID:"21564C45-8432-499D-8BCD-42BFD1E001AD",
styleClass:"label_bts text-right",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.tooltip.clearFilter",
visible:true
},
name:"btnClearItem_size",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"ADAC82FB-7AED-4EC8-B742-602456AF9D03"
},
{
cssPosition:"8,-1,-1,3348,20,20",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"3348",
right:"-1",
top:"8",
width:"20"
},
enabled:true,
formIndex:9,
onActionMethodID:"21564C45-8432-499D-8BCD-42BFD1E001AD",
styleClass:"label_bts text-right",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.tooltip.clearFilter",
visible:true
},
name:"btnClearItem_type",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"B01B9D88-7574-40A1-BA04-E00F12E86E12"
},
{
cssPosition:"9,-1,-1,3228,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"3228",
right:"-1",
top:"9",
width:"140"
},
enabled:true,
labelFor:"item_type",
styleClass:"label_bts",
tabSeq:-1,
text:"Item Type ",
toolTipText:"Item Type ",
visible:true
},
name:"item_type_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"B2E015CB-A529-4714-A479-454EEC9DDE2C"
},
{
cssPosition:"49,-1,-1,290,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"290",
right:"-1",
top:"49",
width:"140"
},
dataProviderID:"_transactionNumber",
editable:true,
enabled:true,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"transactionNumber",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"B57B7256-D960-4C85-BD6D-EF47D1ED3779"
},
{
cssPosition:"9,-1,-1,4607,20,20",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"4607",
right:"-1",
top:"9",
width:"20"
},
enabled:true,
formIndex:9,
onActionMethodID:"21564C45-8432-499D-8BCD-42BFD1E001AD",
styleClass:"label_bts text-right",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.tooltip.clearFilter",
visible:true
},
name:"btnClearLedger_account",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"B8E1EA19-24FC-4DB9-807D-F2246A59F0E8"
},
{
cssPosition:"28,-1,-1,1830,140,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"1830",
right:"-1",
top:"28",
width:"140"
},
dataProviderID:"_item_description_criteria",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"95A50B98-5DF2-4D1D-A20D-75D920B0C5F7",
visible:true
},
name:"item_description_criteria",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"B9695B79-3884-40F1-B284-0741B6C7282A"
},
{
cssPosition:"10,-1,-1,2925,20,20",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"2925",
right:"-1",
top:"10",
width:"20"
},
enabled:true,
formIndex:9,
onActionMethodID:"21564C45-8432-499D-8BCD-42BFD1E001AD",
styleClass:"label_bts text-right",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.tooltip.clearFilter",
visible:true
},
name:"btnClearTotal_cost",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"BBE38CB8-632C-41C9-8E7B-0D1BF46A1ED7"
},
{
cssPosition:"28,-1,-1,1690,140,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"1690",
right:"-1",
top:"28",
width:"140"
},
dataProviderID:"_item_code_criteria",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"95A50B98-5DF2-4D1D-A20D-75D920B0C5F7",
visible:true
},
name:"item_code_criteria",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"BD798D70-F6A4-4E67-8941-B6CD457D6D71"
},
{
cssPosition:"10,-1,-1,1530,20,20",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"1530",
right:"-1",
top:"10",
width:"20"
},
enabled:true,
formIndex:9,
onActionMethodID:"21564C45-8432-499D-8BCD-42BFD1E001AD",
styleClass:"label_bts text-right",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.tooltip.clearFilter",
visible:true
},
name:"btnClearSalesOrderLine_number",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"C002A647-AF93-4AD3-A428-8DEFFB651FF7"
},
{
cssPosition:"28,-1,-1,4486,140,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"4486",
right:"-1",
top:"28",
width:"140"
},
dataProviderID:"_ledger_account_criteria",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"95A50B98-5DF2-4D1D-A20D-75D920B0C5F7",
visible:true
},
name:"ledger_account_criteria",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"C04D664B-33D8-44EF-85BA-8FA68047FF55"
},
{
cssPosition:"49,-1,-1,1130,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"1130",
right:"-1",
top:"49",
width:"140"
},
dataProviderID:"_supplier",
editable:true,
enabled:true,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"supplier",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"C1C5FB4F-482D-4410-891B-147A8EFA3AC4"
},
{
cssPosition:"28,-1,-1,3928,140,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"3928",
right:"-1",
top:"28",
width:"140"
},
dataProviderID:"_item_weight_criteria",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"********-2A28-41DC-AE58-EA8F55F3DE46",
visible:true
},
name:"item_weight_criteria",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"C1F797BD-07F3-4EF3-961E-1DE532132830"
},
{
cssPosition:"10,-1,-1,690,20,20",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"690",
right:"-1",
top:"10",
width:"20"
},
enabled:true,
formIndex:9,
onActionMethodID:"21564C45-8432-499D-8BCD-42BFD1E001AD",
styleClass:"label_bts text-right",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.tooltip.clearFilter",
visible:true
},
name:"btnClearTransaction_reference_number",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"C2ACBEEB-7D7F-46B1-9E78-F44241BDD886"
},
{
cssPosition:"10,-1,-1,412,20,20",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"412",
right:"-1",
top:"10",
width:"20"
},
enabled:true,
formIndex:9,
onActionMethodID:"21564C45-8432-499D-8BCD-42BFD1E001AD",
styleClass:"label_bts text-right",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.tooltip.clearFilter",
visible:true
},
name:"btnClearTransactionNumber",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"C7D66E89-AC2C-4C4C-8E62-0768B6A70444"
},
{
cssPosition:"28,-1,-1,4346,140,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"4346",
right:"-1",
top:"28",
width:"140"
},
dataProviderID:"_packing_slip_criteria",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"95A50B98-5DF2-4D1D-A20D-75D920B0C5F7",
visible:true
},
name:"packing_slip_criteria",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"C9A3591F-07E5-42AB-9523-28991E95E285"
},
{
cssPosition:"8,-1,-1,3486,20,20",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"3486",
right:"-1",
top:"8",
width:"20"
},
enabled:true,
formIndex:9,
onActionMethodID:"21564C45-8432-499D-8BCD-42BFD1E001AD",
styleClass:"label_bts text-right",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.tooltip.clearFilter",
visible:true
},
name:"btnClearItem_status",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"CB653382-D6AD-4357-9DFC-EC09952E7E18"
},
{
cssPosition:"9,-1,-1,710,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"710",
right:"-1",
top:"9",
width:"140"
},
enabled:true,
labelFor:"user_name",
styleClass:"label_bts",
tabSeq:-1,
text:"Employee name",
toolTipText:"Employee name",
visible:true
},
name:"user_name_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"CB794931-308A-4351-AA05-AA57CF25BFAA"
},
{
cssPosition:"47,-1,-1,4206,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"4206",
right:"-1",
top:"47",
width:"140"
},
dataProviderID:"_item_color",
editable:true,
enabled:true,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"item_color",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"CE666F81-BE47-484B-A3D2-609BE3BB7090"
},
{
cssPosition:"29,-1,-1,2805,140,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"2805",
right:"-1",
top:"29",
width:"140"
},
dataProviderID:"_total_cost_criteria",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"********-2A28-41DC-AE58-EA8F55F3DE46",
visible:true
},
name:"total_cost_criteria",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"D06AB18C-1B9F-418A-979D-BC32E8A9DCA4"
},
{
cssPosition:"29,-1,-1,2945,140,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"2945",
right:"-1",
top:"29",
width:"140"
},
dataProviderID:"_project",
enabled:true,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"2B769921-A483-4585-8054-BA2831BD62A9",
visible:true
},
name:"project",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"D1B7E5A5-5CBF-4B97-A9E0-212C4D096DDD"
},
{
cssPosition:"29,-1,-1,1270,140,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"1270",
right:"-1",
top:"29",
width:"140"
},
dataProviderID:"_salesorder_criteria",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"95A50B98-5DF2-4D1D-A20D-75D920B0C5F7",
visible:true
},
name:"salesorder_criteria",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"D9119E2F-2FA1-4776-9604-6C4B9A0D3D7C"
},
{
cssPosition:"29,-1,-1,290,140,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"290",
right:"-1",
top:"29",
width:"140"
},
dataProviderID:"_transaction_number_criteria",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"95A50B98-5DF2-4D1D-A20D-75D920B0C5F7",
visible:true
},
name:"tranaction_number_criteria",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"D95459CC-9E82-43E6-BE00-E88546ACF515"
},
{
cssPosition:"9,-1,-1,3066,20,20",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"3066",
right:"-1",
top:"9",
width:"20"
},
enabled:true,
formIndex:9,
onActionMethodID:"21564C45-8432-499D-8BCD-42BFD1E001AD",
styleClass:"label_bts text-right",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.tooltip.clearFilter",
visible:true
},
name:"btnClearProject",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"DB80D413-77EC-4C56-A461-27F4FFCBF897"
},
{
cssPosition:"9,-1,-1,1811,20,20",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"1811",
right:"-1",
top:"9",
width:"20"
},
enabled:true,
formIndex:9,
onActionMethodID:"21564C45-8432-499D-8BCD-42BFD1E001AD",
styleClass:"label_bts text-right",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.tooltip.clearFilter",
visible:true
},
name:"btnClearItem_code",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"DBE073BC-2C64-4109-B272-9B36FF3BEED8"
},
{
cssPosition:"10,-1,-1,1110,20,20",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"1110",
right:"-1",
top:"10",
width:"20"
},
enabled:true,
formIndex:9,
onActionMethodID:"21564C45-8432-499D-8BCD-42BFD1E001AD",
styleClass:"label_bts text-right",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.tooltip.clearFilter",
visible:true
},
name:"btnClearCustomer",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"DDFA0B36-3B56-49E0-9752-80BF3C676C86"
},
{
cssPosition:"10,-1,-1,2387,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"2387",
right:"-1",
top:"10",
width:"140"
},
enabled:true,
labelFor:"quantity",
styleClass:"label_bts",
tabSeq:-1,
text:"Quantity",
toolTipText:"Quantity",
visible:true
},
name:"quantity_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"E54B493F-5C19-4E5D-8C77-D5FA191C9DC2"
},
{
cssPosition:"9,-1,-1,1969,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"1969",
right:"-1",
top:"9",
width:"140"
},
enabled:true,
labelFor:"warehouse",
styleClass:"label_bts",
tabSeq:-1,
text:"Warehouse",
toolTipText:"Warehouse",
visible:true
},
name:"warehouse_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"E59BCA28-D260-4805-AAF3-DCFD26CA5C79"
},
{
cssPosition:"10,-1,-1,2526,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"2526",
right:"-1",
top:"10",
width:"140"
},
enabled:true,
labelFor:"cost_unit",
styleClass:"label_bts",
tabSeq:-1,
text:"Cost / Unit",
toolTipText:"Cost / Unit",
visible:true
},
name:"cost_unit_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"E7B66272-F4E1-44F2-B3D6-DBF1C99135C5"
},
{
cssPosition:"8,-1,-1,2092,20,20",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"2092",
right:"-1",
top:"8",
width:"20"
},
enabled:true,
formIndex:9,
onActionMethodID:"21564C45-8432-499D-8BCD-42BFD1E001AD",
styleClass:"label_bts text-right",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.tooltip.clearFilter",
visible:true
},
name:"btnClearLocation_code",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"E7BF7DC1-CBDD-40DB-8F8D-A94FAB31A55C"
},
{
cssPosition:"49,-1,-1,990,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"990",
right:"-1",
top:"49",
width:"140"
},
dataProviderID:"_customer",
editable:true,
enabled:true,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"customer",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"EC34C377-4EDA-427F-B726-1418F6C74A14"
},
{
cssPosition:"28,-1,-1,1969,140,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"1969",
right:"-1",
top:"28",
width:"140"
},
dataProviderID:"_warehouse",
enabled:true,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"27FD514B-A2D9-4CEF-9866-5F61CBE306FA",
visible:true
},
name:"warehouse",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"EC862CE5-4D17-4FB0-B326-C9C0CF9FC4E4"
},
{
cssPosition:"7,-1,-1,3768,20,20",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"3768",
right:"-1",
top:"7",
width:"20"
},
enabled:true,
formIndex:9,
onActionMethodID:"21564C45-8432-499D-8BCD-42BFD1E001AD",
styleClass:"label_bts text-right",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.tooltip.clearFilter",
visible:true
},
name:"btnClearItem_brand",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"EE2AA2A6-1A96-4D25-9150-7D8EAD1FB85C"
},
{
cssPosition:"29,-1,-1,2666,140,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"2666",
right:"-1",
top:"29",
width:"140"
},
dataProviderID:"_stocking_unit_criteria",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"95A50B98-5DF2-4D1D-A20D-75D920B0C5F7",
visible:true
},
name:"stocking_unit_criteria",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"F184AB05-1C24-48BF-BA89-E121B84AC812"
},
{
cssPosition:"8,-1,-1,3208,20,20",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"3208",
right:"-1",
top:"8",
width:"20"
},
enabled:true,
formIndex:9,
onActionMethodID:"21564C45-8432-499D-8BCD-42BFD1E001AD",
styleClass:"label_bts text-right",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.tooltip.clearFilter",
visible:true
},
name:"btnClearItem_class",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"F1EF2AD4-B8BE-432A-8EC1-4014DC8302F2"
},
{
cssPosition:"9,-1,-1,4486,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"4486",
right:"-1",
top:"9",
width:"140"
},
enabled:true,
labelFor:"ledger_account",
styleClass:"label_bts",
tabSeq:-1,
text:"Ledger Account ",
toolTipText:"Ledger Account ",
visible:true
},
name:"ledger_account_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"F22D757A-6095-44DD-BB84-0A05799AC069"
},
{
cssPosition:"9,-1,-1,4885,20,20",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"4885",
right:"-1",
top:"9",
width:"20"
},
enabled:true,
formIndex:9,
onActionMethodID:"21564C45-8432-499D-8BCD-42BFD1E001AD",
styleClass:"label_bts text-right",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.tooltip.clearFilter",
visible:true
},
name:"btnClearCustomer_po",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"F3C72010-32C2-4562-B9A4-19023F1052C0"
},
{
cssPosition:"9,-1,-1,2248,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"2248",
right:"-1",
top:"9",
width:"140"
},
enabled:true,
labelFor:"line_number",
styleClass:"label_bts",
tabSeq:-1,
text:"Line #",
toolTipText:"Line #",
visible:true
},
name:"line_number_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"F44E8B93-5A5E-4EC5-857F-7536B035ADCE"
},
{
cssPosition:"29,-1,-1,2526,140,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"2526",
right:"-1",
top:"29",
width:"140"
},
dataProviderID:"_cost_unit_criteria",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"********-2A28-41DC-AE58-EA8F55F3DE46",
visible:true
},
name:"cost_unit_criteria",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"F87C19D2-6639-44DA-834A-D01A23338BD0"
},
{
cssPosition:"9,-1,-1,850,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"850",
right:"-1",
top:"9",
width:"140"
},
enabled:true,
labelFor:"status",
styleClass:"label_bts",
tabSeq:-1,
text:"Transaction Status",
toolTipText:"Transaction Status",
visible:true
},
name:"status_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"FABCC75B-B015-4BC0-9F4E-8C7C4D06DC6F"
},
{
cssPosition:"49,-1,-1,430,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"430",
right:"-1",
top:"49",
width:"140"
},
dataProviderID:"_transaction_date",
enabled:true,
onDataChangeMethodID:"FB29364C-67BC-4EF5-A931-47AF65776AA4",
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:0,
visible:true
},
name:"transaction_date",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"FB94C2CF-EB32-449A-AA7C-EDB87EEA1CDD"
},
{
cssPosition:"10,-1,-1,1410,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"1410",
right:"-1",
top:"10",
width:"140"
},
enabled:true,
labelFor:"salesorder_line_number",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.salesOrderAndLine",
toolTipText:"Line #",
visible:true
},
name:"salesorder_line_number_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"FD1DBDAE-DC54-418E-8DF0-7C096178F286"
},
{
cssPosition:"10,-1,-1,2646,20,20",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"2646",
right:"-1",
top:"10",
width:"20"
},
enabled:true,
formIndex:9,
onActionMethodID:"21564C45-8432-499D-8BCD-42BFD1E001AD",
styleClass:"label_bts text-right",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.tooltip.clearFilter",
visible:true
},
name:"btnClearCost_unit",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"FD742C7F-697D-4898-86DB-67CBD2F6A06C"
},
{
cssPosition:"29,-1,-1,2387,140,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"2387",
right:"-1",
top:"29",
width:"140"
},
dataProviderID:"_quantity_criteria",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"********-2A28-41DC-AE58-EA8F55F3DE46",
visible:true
},
name:"quantity_criteria",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"FE5D216A-A07F-461B-A204-9A588A2389F4"
}
],
name:"in_trans_inquiry_tbl_search",
scrollbars:33,
size:"4917,102",
styleName:"Avanti",
typeid:3,
uuid:"F29BCB34-0973-4DF8-88EC-E62E53C2C316",
view:0