// Inventory -> Transaction Inquiry

/**
 * @type {Date}
 *
 * @properties={typeid:35,uuid:"43C0F5C3-3EAF-4760-9054-E8503F4117C3",variableType:93}
 */
var _transaction_to_date = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"75C98A5B-ADB2-42BF-AF9E-6DC6EF1B3B26"}
 */
var _item_brand_criteria = 'LIKE';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"9957E79E-E1B0-4E8E-9D88-4920D857BB77"}
 */
var _item_brand = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"2F7EE218-3D9E-4422-8333-1EC5613D142F"}
 */
var _customer_po = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"6FC01B75-23A5-4697-996D-4A60DDDFF4AF"}
 */
var _customer_po_criteria = 'LIKE';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"F21F5597-DCC6-43E8-8F1E-30CA7698C52D"}
 */
var _offset_account = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"4F23F94F-D07C-47A8-B48D-BF7D1831E556"}
 */
var _offset_account_criteria = 'LIKE';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"2BDC17DA-8925-4CAA-B61B-400FA03E6EF0"}
 */
var _ledger_account = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"5FBE6697-6A37-4139-8445-54B4EBE8B1B8"}
 */
var _ledger_account_criteria = 'LIKE';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"A0044FB6-40BA-46F9-958D-06BBA8B09C7E"}
 */
var _packing_slip = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"1C7E93E3-F6D1-4A30-A559-887C8C08E09D"}
 */
var _packing_slip_criteria = 'LIKE';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"BAC7A61F-4FA9-47D0-991B-63C0CD4DD9D8"}
 */
var _item_color = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"3B4F401F-F0BE-4179-936D-A0AF98B2F587"}
 */
var _item_color_criteria = 'LIKE';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"73D65F5A-D456-4F18-AB22-A3350686CD58"}
 */
var _item_finish = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"D7BD01E2-3C2A-4FF7-856E-9B48547A30B6"}
 */
var _item_finish_criteria = 'LIKE';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"024513AF-644C-41CF-966D-7FC6790423B7"}
 */
var _item_weight = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"FA68D1FC-3834-45E3-A3AB-D382AEFF9164"}
 */
var _item_weight_criteria = '=';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"F8BE1BD1-468A-495E-A747-2F5A1D834A22"}
 */
var _item_size = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"C43EEC72-61A4-4FB9-B576-************"}
 */
var _item_size_criteria = 'LIKE';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"E609F795-70F1-41A5-93DF-4A1DF06C664A"}
 */
var _item_group = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"DBAD5E46-EB60-4E42-B5EE-2B3F626F5984"}
 */
var _item_status = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"BD0213AE-31ED-404F-962E-739FCB8C08F5"}
 */
var _item_type = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"84817613-E47B-4488-9A50-EB9D834BBC39"}
 */
var _item_class = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"0034D121-63FF-4390-AA8A-546FF583A7EC"}
 */
var _serial_number = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"F46F7CF6-5BCC-42A8-A143-05343B036D03"}
 */
var _serial_number_criteria = 'LIKE';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"DEAB69C3-B05D-4DE7-816F-2FEC9435FD08"}
 */
var _adjustment_type = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"45B2EA64-8BF1-4689-8F46-F14759538D41"}
 */
var _project = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"2C970E6D-A129-4D2C-9676-2EB63E6E8CA1"}
 */
var _total_cost = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"23E3A0B7-EB4A-4855-87E0-7DD948EA5F3E"}
 */
var _total_cost_criteria = '=';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"EB4FDEC5-A84D-4826-8DA7-83C6C5E9AF11"}
 */
var _stocking_unit = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"8CF78A11-8D82-429D-BDBD-A1D2863E12F1"}
 */
var _stocking_unit_criteria = 'LIKE';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"DC4138AE-1174-481A-B7B5-D29B6DB55812"}
 */
var _cost_unit = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"0239B806-DFAB-42A0-A5C9-48936255AB03"}
 */
var _cost_unit_criteria = '=';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"3C26F42B-0BAB-40AF-86FC-58467B309A16"}
 */
var _quantity = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"7FA92FEE-75D4-431A-915C-46335B55B5F2"}
 */
var _quantity_criteria = '=';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"AC342C72-01A4-403F-81EF-70099C2422D8"}
 */
var _location_code = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"2BFBD013-A986-4976-8012-5ADB1EEFA540"}
 */
var _location_code_criteria = 'LIKE';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"4B34B62F-305B-4276-9086-18735AC79217"}
 */
var _warehouse = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"1A39EE33-B786-47C1-85CC-7E1B6695BC96"}
 */
var _item_description = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"A391B3DF-F627-4A20-9A38-0D2CA4F1B757"}
 */
var _item_description_criteria = 'LIKE';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"11C6F6E0-D8E7-40C6-A0C7-A414747DB35F"}
 */
var _item_code = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"60A25C2F-6233-4934-A3B1-9751F9CD1CB1"}
 */
var _item_code_criteria = 'LIKE';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"82F45BC5-1556-49D2-BD9B-35AD1E8F627F"}
 */
var _line_number = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"E52E1696-D824-4C94-AF99-BFF88982BB6B"}
 */
var _line_number_criteria = '=';

/**
 * @type {String}
 * 
 * @properties={typeid:35,uuid:"9680EC39-6692-4939-B05C-2D8426197366"}
 */
var _job_number = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"ED4A5C3B-AD0C-407B-A3D2-3D45B82229A7"}
 */
var _job_number_criteria = 'LIKE';

/**
 * @type {String}
 * 
 * @properties={typeid:35,uuid:"23F97B0F-180C-4656-9669-667F746FC0D8"}
 */
var _salesorder = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"73C83E26-7530-428B-99E3-F5AC7EB8A977"}
 */
var _salesorder_criteria = 'LIKE';

/**
 * @type {String}
 * 
 * @properties={typeid:35,uuid:"C806E4C7-BD94-45C1-9EE1-08B524FE59B3"}
 */
var _salesorder_line_number = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"849BC8BD-8A08-4159-BDF4-45A4E395D3D1"}
 */
var _salesorder_line_number_criteria = '=';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"4B6E9CF1-E4F5-4D43-850E-08FC3E11D3E4"}
 */
var _supplier = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"3BDE3112-7119-4F83-8737-7A1CD7DB4101"}
 */
var _supplier_criteria = 'LIKE';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"A20ABC81-1944-401D-97CB-833B5E91B8B9"}
 */
var _customer = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"4BB29144-86E4-4650-93AC-29695E94A612"}
 */
var _customer_criteria = 'LIKE';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"5721AA3D-371C-4BA8-BA70-4C6E84F37E5D"}
 */
var _status = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"92DED997-8B3C-4AC6-8FF1-0CC3E790185E"}
 */
var _user_name = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"2B42F582-0A25-4773-B7B1-CF70F69B9B34"}
 */
var _tranaction_reference_number = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"ACEEDF27-AF29-45F8-91D5-2E7307E19486"}
 */
var _tranaction_reference_number_criteria = 'LIKE';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"C9F96EA0-4E40-4C83-8643-375D4CDF4739"}
 */
var _transaction_date_criteria = '=';

/**
 * @type {Date}
 *
 * @properties={typeid:35,uuid:"E54B6E07-5201-4F28-A244-F7B6AF3FA7DB",variableType:93}
 */
var _transaction_date = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"4E14958F-23BA-49A3-9BB6-818218A68704"}
 */
var _transaction_number_criteria = 'LIKE';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"24A40E8D-504B-461D-9C22-1D7F7A2D1C5A"}
 */
var _transactionNumber = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"16AC8B9C-7651-423F-B7B7-B5A9D6A0D43E"}
 */
var _transactionType = null;

/**
 * @properties={typeid:24,uuid:"10DA66D0-5BB7-4AF1-AA6A-F670A551A4E3"}
 */

/* declaration missing */

/**
 * @param event
 *
 * @properties={typeid:24,uuid:"21564C45-8432-499D-8BCD-42BFD1E001AD"}
 */
function onAction_ClearFilter(event)
{
	switch(event.getElementName())
	{
		case "btnClearAll":
		 _item_brand_criteria = 'LIKE';
		 _item_brand = null;
	 
		 _customer_po = null;
		 _customer_po_criteria = 'LIKE';
		 
		 _offset_account = null;
		 _offset_account_criteria = 'LIKE';
		 
		 _ledger_account = null;
		 _ledger_account_criteria = 'LIKE';
		 
		 _packing_slip = null;
		 _packing_slip_criteria = 'LIKE';
		 
		 _item_color = null;
		 _item_color_criteria = 'LIKE';
		 
		 _item_finish = null;
		 _item_finish_criteria = 'LIKE';
		 
		 _item_weight = null;
		 _item_weight_criteria = '=';
		 
		 _item_size = null;
		 _item_size_criteria = 'LIKE';
		 
		 _item_group = null;
		
		 _item_status = null;
		
		 _item_type = null;
		 
		 _item_class = null;
		
		 _serial_number = null;
		 _serial_number_criteria = 'LIKE';
		 
		 _adjustment_type = null;
		 
		 _project = null;
		 
		 _total_cost = null;
		 _total_cost_criteria = '=';
		
		 _stocking_unit = null;
		 _stocking_unit_criteria = 'LIKE';
		 
		 _cost_unit = null;
		 _cost_unit_criteria = '=';
		
		 _quantity = null;
		 _quantity_criteria = '=';
		 
		 _location_code = null;
		 _location_code_criteria = 'LIKE';
		 
		 _warehouse = null;
		 
		 _item_description = null;
		 _item_description_criteria = 'LIKE';
		 
		 _item_code = null;
		 _item_code_criteria = 'LIKE';
		 
		 _line_number = null;
		 _line_number_criteria = '=';
		 
		 _job_number = null;
		 _job_number_criteria = 'LIKE';
		 
		 _salesorder = null;
		 _salesorder_criteria = 'LIKE';
		 
		 _salesorder_line_number = null;
		 _salesorder_line_number_criteria = '=';
		
		 _supplier = null;
		 _supplier_criteria = 'LIKE';
		 
		 _customer = null;
		 _customer_criteria = 'LIKE';
		 
		 _status = null;
		 
		 _user_name = null;
		 
		 _tranaction_reference_number = null;
		 _tranaction_reference_number_criteria = 'LIKE';
		 
		 _transaction_date_criteria = '=';
		 _transaction_date = null;
		 _transaction_to_date = null;
		 
		 _transaction_number_criteria = 'LIKE';
		 _transactionNumber = null;
		
		 _transactionType = null;
		break;
	case "btnClearItem_brand":
		_item_brand_criteria = 'LIKE';
		_item_brand = null;
		break;
	case "btnClearCustomer_po":
		 _customer_po = null;
		 _customer_po_criteria = 'LIKE';
		break;
	case "btnClearOffset_account":
		 _offset_account = null;
		 _offset_account_criteria = 'LIKE';
		break;
	case "btnClearLedger_account":
		 _ledger_account = null;
		 _ledger_account_criteria = 'LIKE';
		break;
	case "btnClearPacking_slip":
		 _packing_slip = null;
		 _packing_slip_criteria = 'LIKE';
		break;
	case "btnClearItem_color":
		 _item_color = null;
		 _item_color_criteria = 'LIKE';
		 break;
	case "btnClearItem_finish":
		 _item_finish = null;
		 _item_finish_criteria = 'LIKE';
		 break;
	case "btnClearItem_weight":
		 _item_weight = null;
		 _item_weight_criteria = '=';
		 break;
	case "btnClearItem_size":
		 _item_size = null;
		 _item_size_criteria = 'LIKE';
		 break;
	case "btnClearItem_group":
	 	_item_group = null;
 		break;
	case "btnClearItem_status":
		_item_status = null;
		break;
	case "btnClearItem_type":
		 _item_type = null;
		 break;
	case "btnClearItem_class":
		 _item_class = null;
		 break;
	case "btnClearSerial_number":
		 _serial_number = null;
		 _serial_number_criteria = 'LIKE';
		 break;
	case "btnClearAdjustment_type":
		 _adjustment_type = null;
		 break;
	case "btnClearProject":
		 _project = null;
		 break;
	case "btnClearTotal_cost":
		 _total_cost = null;
		 _total_cost_criteria = '=';
		 break;
	case "btnClearStocking_unit":
		 _stocking_unit = null;
		 _stocking_unit_criteria = 'LIKE';
		 break;
	case "btnClearCost_unit":
		 _cost_unit = null;
		 _cost_unit_criteria = '=';
		 break;
	case "btnClearQuantity":
		 _quantity = null;
		 _quantity_criteria = '=';
		 break;
	case "btnClearLocation_code":
		 _location_code = null;
		 _location_code_criteria = 'LIKE';
		 break;
	case "btnClearWarehouse":
		 _warehouse = null;
		 break;
	case "btnClearItem_description":
		 _item_description = null;
		 _item_description_criteria = 'LIKE';
		 break;
	case "btnClearItem_code":
		 _item_code = null;
		 _item_code_criteria = 'LIKE';
		 break;
	case "btnClearLine_number":
		 _line_number = null;
		 _line_number_criteria = '=';
		 break;
	case "btnClearJob_number":
		 _job_number = null;
		 _job_number_criteria = 'LIKE';
		 break;
	case "btnClearSalesOrder":
		 _salesorder = null;
		 _salesorder_criteria = 'LIKE';
		 break;
	case "btnClearSalesOrderLine_number":
		 _salesorder_line_number = null;
		 _salesorder_line_number_criteria = '=';
		 break;
	case "btnClearSupplier":
		 _supplier = null;
		 _supplier_criteria = 'LIKE';
		 break;
	case "btnClearCustomer":
		 _customer = null;
		 _customer_criteria = 'LIKE';
		 break;
	case "btnClearStatus":
		 _status = null;
		 break;
	case "btnClearUser_name":
		 _user_name = null;
		 break;
	case "btnClearTransaction_reference_number":
		 _tranaction_reference_number = null;
		 _tranaction_reference_number_criteria = 'LIKE';
		 break;
	case "btnClearTransaction_date":
		 _transaction_date_criteria = '=';
		 _transaction_date = null;
		 break;
	case "btnClearTransactionNumber":
		 _transaction_number_criteria = 'LIKE';
		 _transactionNumber = null;
		 break;
	case "btnClearType":
		 _transactionType = null;
		 break;
	}
	
	forms.in_trans_inquiry_tbl.getData();
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"FB29364C-67BC-4EF5-A931-47AF65776AA4"}
 */
function onDataChangeElement(oldValue, newValue, event) { 
	application.output('oldValue: '+oldValue);
	application.output('newValue: '+newValue);

    if (!forms.in_trans_inquiry_tbl._batchFilter) {
    	forms.in_trans_inquiry_tbl.getData();
    }
    return true;
}

/**
 * @param _firstShow
 * @param _event
 *
 * @properties={typeid:24,uuid:"E585E794-64AC-4228-B086-F762E50352DF"}
 */
function onShowForm(_firstShow, _event) {
	_super.onShowForm(_firstShow,_event);
	controller.enabled = true;
    application.executeLater(setReadonly, 500, [false]);
}

/**
 * TODO generated, please specify type and doc for the params
 * @param readonly
 *
 * @properties={typeid:24,uuid:"5EF0BE16-19FB-4DCF-B0D2-D34682E3C5B3"}
 */
function setReadonly(readonly) {
	controller.readOnly = readonly;
}


/**
 * @properties={typeid:24,uuid:"F2B48F8F-0D32-466D-9C88-E998C7C5BE36"}
 * @AllowToRunInFind
 */
function doQuery() {
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('TransactionTypeFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('TransactionNumberFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('TransactionDateFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('TransactionRefFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('UserNameFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('StatusFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('CustomerFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('SupplierFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('SalesOrderFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('SalesOrderLineNumberFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('JobNumberFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('ItemCodeFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('ItemDescriptionFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('WarehouseFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('LocationCodeFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('LineNumberFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('QuantityFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('CostUnitFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('StockingUnitFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('TotalCostFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('ProjectFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('AdjustmentTypeFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('SerialNumberFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('ItemClassFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('ItemTypeFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('ItemStatusFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('ItemGroupFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('ItemBrandFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('ItemSizeFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('ItemWeightFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('ItemFinishFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('ItemColorFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('PackingSlipFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('LedgerAccountFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('OffsetAccountFilter');
    forms.in_trans_inquiry_tbl.foundset.removeFoundSetFilterParam('CustomerPoFilter');

    _bFilterAdded = false;

    if (_transactionType) {
        _bFilterAdded = true;
        forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('transType', 'sql:in', elements.transactionType.getSelectedElements(), 'TransactionTypeFilter');        
    }

    if (_transactionNumber) {
        _bFilterAdded = true;
        if (_transaction_number_criteria == 'LIKE')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('transno', _transaction_number_criteria, '%' + _transactionNumber + '%', 'TransactionNumberFilter');
        else if (_transaction_number_criteria == 'SW')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('transno', 'LIKE', _transactionNumber + '%', 'TransactionNumberFilter');
        else
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('transno', _transaction_number_criteria, _transactionNumber, 'TransactionNumberFilter');
    }

    if (_transaction_date) {
        _bFilterAdded = true;
        if (_transaction_date_criteria == 'between') {
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('transDate', 'between', [_transaction_date.setHours(0), _transaction_to_date.setHours(23)], 'TransactionDateFilter');
        }
        else if (_transaction_date_criteria == '=') {
            var _transaction_date_toD = new Date(_transaction_date);
            _transaction_date_toD.setHours(23);
            _transaction_date_toD.setMinutes(59);
            _transaction_date_toD.setSeconds(59);

            _transaction_date.setHours(0);
            _transaction_date.setMinutes(0);
            _transaction_date.setSeconds(0);

            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('transDate', 'between', [_transaction_date.setHours(0), _transaction_date_toD], 'TransactionDateFilter');
        }
        else
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('transDate', _transaction_date_criteria, _transaction_date, 'TransactionDateFilter');
    }

    if (_tranaction_reference_number) {
        _bFilterAdded = true;
        if (_tranaction_reference_number_criteria == 'LIKE')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('transRef', _tranaction_reference_number_criteria, '%' + _tranaction_reference_number + '%', 'TransactionRefFilter');
        else if (_tranaction_reference_number_criteria == 'SW')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('transRef', 'LIKE', _tranaction_reference_number + '%', 'TransactionRefFilter');
        else
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('transRef', _tranaction_reference_number_criteria, _tranaction_reference_number, 'TransactionRefFilter');
    }

    if (_user_name) {
        _bFilterAdded = true;
        var emplName = scopes.avUtils.getEmplFullName(_user_name);
        forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('tranempl', '=', emplName, 'UserNameFilter');
    }

    if (_status) {
        _bFilterAdded = true;
        forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('transtatus', '=', _status, 'StatusFilter');
    }

    if (_customer) {
        _bFilterAdded = true;
        if (_customer_criteria == 'LIKE')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('customer', _customer_criteria, '%' + _customer + '%', 'CustomerFilter');
        else if (_customer_criteria == 'SW')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('customer', 'LIKE', _customer + '%', 'CustomerFilter');
        else
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('customer', _customer_criteria, _customer, 'CustomerFilter');
    }

    if (_supplier) {
        _bFilterAdded = true;
        if (_supplier_criteria == 'LIKE')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('supplier', _supplier_criteria, '%' + _supplier + '%', 'SupplierFilter');
        else if (_supplier_criteria == 'SW')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('supplier', 'LIKE', _supplier + '%', 'SupplierFilter');
        else
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('supplier', _supplier_criteria, _supplier, 'SupplierFilter');
    }

    if (_line_number) {
        _bFilterAdded = true;
        if (_line_number_criteria == 'LIKE')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('tranLine', _line_number_criteria, '%' + _line_number + '%', 'LineNumberFilter');
        else if (_line_number_criteria == 'SW')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('tranLine', 'LIKE', _line_number + '%', 'LineNumberFilter');
        else
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('tranLine', _line_number_criteria, _line_number, 'LineNumberFilter');
    }
    
    if (_job_number) {
        _bFilterAdded = true;
        if (_job_number_criteria == 'LIKE')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('job_number', _job_number_criteria, '%' + _job_number + '%', 'JobNumberFilter');
        else if (_job_number_criteria == 'SW')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('job_number', 'LIKE', _job_number + '%', 'JobNumberFilter');
        else
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('job_number', _job_number_criteria, _job_number, 'JobNumberFilter');
    }
    
    if (_salesorder) {
        _bFilterAdded = true;
        if (_salesorder_criteria == 'LIKE')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('salesordernum', _salesorder_criteria, '%' + _salesorder + '%', 'SalesOrderFilter');
        else if (_salesorder_criteria == 'SW')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('salesordernum', 'LIKE', _salesorder + '%', 'SalesOrderFilter');
        else
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('salesordernum', _salesorder_criteria, _salesorder, 'SalesOrderFilter');
    }
    
    if (_salesorder_line_number) {
        _bFilterAdded = true;
        if (_salesorder_line_number_criteria == 'LIKE')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('salesorderlinenum', _salesorder_line_number_criteria, '%' + _salesorder_line_number + '%', 'SalesOrderLineNumberFilter');
        else if (_salesorder_line_number_criteria == 'SW')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('salesorderlinenum', 'LIKE', _salesorder_line_number + '%', 'SalesOrderLineNumberFilter');
        else
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('salesorderlinenum', _salesorder_line_number_criteria, _salesorder_line_number, 'SalesOrderLineNumberFilter');
    }

    if (_item_code) {
        _bFilterAdded = true;
        if (_item_code_criteria == 'LIKE')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('item_code', _item_code_criteria, '%' + _item_code + '%', 'ItemCodeFilter');
        else if (_item_code_criteria == 'SW')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('item_code', 'LIKE', _item_code + '%', 'ItemCodeFilter');
        else
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('item_code', _item_code_criteria, _item_code, 'ItemCodeFilter');
    }

    if (_item_description) {
        _bFilterAdded = true;
        if (_item_description_criteria == 'LIKE')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('item_desc1', _item_description_criteria, '%' + _item_description + '%', 'ItemDescriptionFilter');
        else if (_item_code_criteria == 'SW')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('item_desc1', 'LIKE', _item_description + '%', 'ItemDescriptionFilter');
        else
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('item_desc1', _item_description_criteria, _item_description, 'ItemDescriptionFilter');
    }

    if (_warehouse) {
        _bFilterAdded = true;
        forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('whse_desc', '=', application.getValueListDisplayValue('vl_Warehouse', _warehouse), 'WarehouseFilter');
    }

    if (_location_code) {
        _bFilterAdded = true;
        if (_location_code_criteria == 'LIKE')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('location', _location_code_criteria, '%' + _location_code + '%', 'LocationCodeFilter');
        else if (_location_code_criteria == 'SW')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('location', 'LIKE', _location_code + '%', 'LocationCodeFilter');
        else
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('location', _location_code_criteria, _location_code, 'LocationCodeFilter');
    }

    if (_quantity) {
        _bFilterAdded = true;
        if (_quantity_criteria == 'LIKE')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('transQty', _quantity_criteria, '%' + _quantity + '%', 'QuantityFilter');
        else if (_quantity_criteria == 'SW')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('transQty', 'LIKE', _quantity + '%', 'QuantityFilter');
        else
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('transQty', _quantity_criteria, _quantity, 'QuantityFilter');
    }

    if (_cost_unit) {
        _bFilterAdded = true;
        if (_cost_unit_criteria == 'LIKE')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('itemCost', _cost_unit_criteria, '%' + _cost_unit + '%', 'CostUnitFilter');
        else if (_cost_unit_criteria == 'SW')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('itemCost', 'LIKE', _cost_unit + '%', 'CostUnitFilter');
        else
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('itemCost', _cost_unit_criteria, parseFloat(_cost_unit), 'CostUnitFilter');
    }

    if (_stocking_unit) {
        _bFilterAdded = true;
        if (_stocking_unit_criteria == 'LIKE')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('itemUOM', _stocking_unit_criteria, '%' + _stocking_unit + '%', 'StockingUnitFilter');
        else if (_stocking_unit_criteria == 'SW')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('itemUOM', 'LIKE', _stocking_unit + '%', 'StockingUnitFilter');
        else
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('itemUOM', _stocking_unit_criteria, _stocking_unit, 'StockingUnitFilter');
    }

    if (_total_cost) {
        _bFilterAdded = true;
        if (_total_cost_criteria == 'LIKE')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('totalCost', _total_cost_criteria, '%' + _total_cost + '%', 'TotalCostFilter');
        else if (_total_cost_criteria == 'SW')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('totalCost', 'LIKE', _total_cost + '%', 'TotalCostFilter');
        else
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('totalCost', _total_cost_criteria, _total_cost, 'TotalCostFilter');
    }

    if (_project) {
        _bFilterAdded = true;
        forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('project', '=', application.getValueListDisplayValue('vl_CustomerProjectsWithEmptyValue', _project), 'ProjectFilter');
    }

    if (_adjustment_type) {
        _bFilterAdded = true;
        forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('adjustmentType', 'sql:in', elements.adjustment_type.getSelectedElements(), 'AdjustmentTypeFilter');
    }

    if (_serial_number) {
        _bFilterAdded = true;
        if (_serial_number_criteria == 'LIKE')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('serialNumber', _serial_number_criteria, '%' + _serial_number + '%', 'SerialNumberFilter');
        else if (_serial_number_criteria == 'SW')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('serialNumber', 'LIKE', _serial_number + '%', 'SerialNumberFilter');
        else
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('serialNumber', _serial_number_criteria, _serial_number, 'SerialNumberFilter');
    }

    if (_item_class) {
        _bFilterAdded = true;
        forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('itemClass', '=', application.getValueListDisplayValue('vl_itemClass_WithEmptyValue', _item_class), 'ItemClassFilter');
    }

    if (_item_type) {
        _bFilterAdded = true;
        forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('itemtype_desc', '=', application.getValueListDisplayValue('vl_ItemTypesWithEmptyValue', _item_type), 'ItemTypeFilter');
    }

    if (_item_status) {
        _bFilterAdded = true;
        forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('item_status', '=', application.getValueListDisplayValue('vl_itemStatusEmptyValue', _item_status), 'ItemStatusFilter');
    }

    if (_item_group) {
        _bFilterAdded = true;
        forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('ingroup_desc', '=', application.getValueListDisplayValue('vl_ItemGroupAll', _item_group), 'ItemGroupFilter');
    }

    if (_item_brand) {
        _bFilterAdded = true;
        if (_item_brand_criteria == 'LIKE')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('itemBrand', _item_brand_criteria, '%' + _item_brand + '%', 'ItemBrandFilter');
        else if (_item_brand_criteria == 'SW')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('itemBrand', 'LIKE', _item_brand + '%', 'ItemBrandFilter');
        else
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('itemBrand', _item_brand_criteria, _item_brand, 'ItemBrandFilter');
    }

    if (_item_size) {
        _bFilterAdded = true;
        if (_item_size_criteria == 'LIKE')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('itemSize', _item_size_criteria, '%' + _item_size + '%', 'ItemSizeFilter');
        else if (_item_size_criteria == 'SW')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('itemSize', 'LIKE', _item_size + '%', 'ItemSizeFilter');
        else
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('itemSize', _item_size_criteria, _item_size, 'ItemSizeFilter');
    }

    if (_item_weight) {
        _bFilterAdded = true;
        if (_item_weight_criteria == 'LIKE')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('item_max_weight', _item_weight_criteria, '%' + _item_weight + '%', 'ItemWeightFilter');
        else if (_item_weight_criteria == 'SW')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('item_max_weight', 'LIKE', _item_weight + '%', 'ItemWeightFilter');
        else
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('item_max_weight', _item_weight_criteria, _item_weight, 'ItemWeightFilter');
    }

    if (_item_finish) {
        _bFilterAdded = true;
        if (_item_finish_criteria == 'LIKE')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('itemFinish', _item_finish_criteria, '%' + _item_finish + '%', 'ItemFinishFilter');
        else if (_item_finish_criteria == 'SW')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('itemFinish', 'LIKE', _item_finish + '%', 'ItemFinishFilter');
        else
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('itemFinish', _item_finish_criteria, _item_finish, 'ItemFinishFilter');
    }

    if (_item_color) {
        _bFilterAdded = true;
        if (_item_color_criteria == 'LIKE')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('item_color', _item_color_criteria, '%' + _item_color + '%', 'ItemColorFilter');
        else if (_item_color_criteria == 'SW')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('item_color', 'LIKE', _item_color + '%', 'ItemColorFilter');
        else
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('item_color', _item_color_criteria, _item_color, 'ItemColorFilter');
    }

    if (_packing_slip) {
        _bFilterAdded = true;
        if (_packing_slip_criteria == 'LIKE')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('packingSlip', _packing_slip_criteria, '%' + _packing_slip + '%', 'PackingSlipFilter');
        else if (_packing_slip_criteria == 'SW')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('packingSlip', 'LIKE', _packing_slip + '%', 'PackingSlipFilter');
        else
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('packingSlip', _packing_slip_criteria, _packing_slip, 'PackingSlipFilter');
    }

    if (_ledger_account) {
        _bFilterAdded = true;
        if (_ledger_account_criteria == 'LIKE')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('ledgerAccount', _ledger_account_criteria, '%' + _ledger_account + '%', 'LedgerAccountFilter');
        else if (_ledger_account_criteria == 'SW')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('ledgerAccount', 'LIKE', _ledger_account + '%', 'LedgerAccountFilter');
        else
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('ledgerAccount', _ledger_account_criteria, _ledger_account, 'LedgerAccountFilter');
    }

    if (_offset_account) {
        _bFilterAdded = true;
        if (_offset_account_criteria == 'LIKE')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('offsetAccountCode', _offset_account_criteria, '%' + _offset_account + '%', 'OffsetAccountFilter');
        else if (_offset_account_criteria == 'SW')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('offsetAccountCode', 'LIKE', _offset_account + '%', 'OffsetAccountFilter');
        else
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('offsetAccountCode', _offset_account_criteria, _offset_account, 'OffsetAccountFilter');
    }

    if (_customer_po) {
        _bFilterAdded = true;
        if (_customer_po_criteria == 'LIKE')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('customer_po', _customer_po_criteria, '%' + _customer_po + '%', 'CustomerPoFilter');
        else if (_customer_po_criteria == 'SW')
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('customer_po', 'LIKE', _customer_po + '%', 'CustomerPoFilter');
        else
            forms.in_trans_inquiry_tbl.foundset.addFoundSetFilterParam('customer_po', _customer_po_criteria, _customer_po, 'CustomerPoFilter');
    }

    if (_bFilterAdded) {
        forms.in_trans_inquiry_tbl.foundset.loadAllRecords();
    }
    else {
        var timeInMs = Date.now() - 30 * 24 * 60 * 60000;
        var d = new Date(timeInMs);

        /*** @type {JSFoundSet<db:/avanti/_v_in_transaction_inquiry>} */
        var fsView = databaseManager.getFoundSet(globals.avBase_dbase_avanti, '_v_in_transaction_inquiry');
        fsView.addFoundSetFilterParam('transDate', '>=', d);
        fsView.loadAllRecords();

        forms.in_trans_inquiry_tbl.foundset.loadRecords(fsView);
    }

    forms.in_trans_inquiry_tbl.foundset.sort('transDate desc, transno desc, item_code desc');
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"7B9B8BA3-E65D-4D3E-AE0A-2610B0B6DC71"}
 */
function onDataChangeTranDateCriteria(oldValue, newValue, event) {
    elements.transaction_to_date.visible = false;
    if (newValue == 'between') {
        elements.transaction_to_date.visible = true;
    }
    return true;
}
